import { Component, OnInit } from '@angular/core';
import { ApiService } from 'src/app/services/api.service';
import { ToastrService } from 'ngx-toastr';
import { IPaycomHistory } from 'src/app/model/paycom-history.model';
import { environment } from 'src/environments/environment';
import { HttpClient } from '@angular/common/http';
import * as FileSaver from 'file-saver';

@Component({
  selector: 'app-report',
  templateUrl: './report.component.html',
  styleUrls: ['./report.component.css']
})
export class ReportComponent implements OnInit {
  paycomHistory: IPaycomHistory[];
  pageSize = 10;
  reportPage: number = 1;
  constructor(private apiService: ApiService, private toastMsg: ToastrService, private http: HttpClient) { }

  ngOnInit() {
    if (!this.apiService.checkPermission('ReportExport')) {
      // this.router.navigate(['/ui/dashboard'])
      this.apiService.goBack();
      this.toastMsg.error("Insufficient Permissions. Please make sure your account can access this information.", "Permissions");
    }
    this.getPaycomHistory();
  }

  getPaycomHistory() {
    this.apiService.get('GetData/PaycomHistory')
      .subscribe(data => {
        if (data && data.statusCode === "201" && data.result) {
          // console.log("API Response", data.result);
          this.paycomHistory = data.result.map(x => {return <IPaycomHistory>x})
          // console.log("Paycom History", this.paycomHistory);
        }
      }, err => {
        this.toastMsg.error(err.message, "Server Error!");
      })
  }

  getPaycomExport(commissionTransactionId: number) {
    this.http.get(`${environment.apiBaseUrl}Payments/PaycomExport/${commissionTransactionId}`, { responseType: 'blob' })
      .subscribe(data => {
        this.downLoadFile(data, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8");
      }, err => {
        this.toastMsg.error(err.message, "Error!");
      });
  }

  /**
   * Method is use to download file.
   * @param data - Array Buffer data
   * @param type - type of the document.
   */
  downLoadFile(data: any, type: string) {
    let blob = new Blob([data], { type: type });

    FileSaver.saveAs(blob, 'Paycom_Export.xlsx');
  }

}
