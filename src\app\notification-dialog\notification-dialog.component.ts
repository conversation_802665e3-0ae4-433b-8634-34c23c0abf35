import { Component, Inject, OnInit } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialog } from '@angular/material/dialog';
import { NotificationService } from '../services/notification.service';
import { ApiService } from '../services/api.service';
import { ToastrService } from 'ngx-toastr';
import { Subscription } from 'rxjs';
import { Router } from '@angular/router';
export interface DialogData {
  count: number;  
  notificationList:any[];
  totalRecord:number;
}

@Component({
  selector: 'app-notification-dialog',
  templateUrl: './notification-dialog.component.html',
  styleUrls: ['./notification-dialog.component.css']
})
export class NotificationDialogComponent implements OnInit {
  

  constructor(@Inject(MAT_DIALOG_DATA) public data: DialogData, private notificationSevice: NotificationService,public apiService: ApiService,private toastMsg: ToastrService,private dialog: MatDialog,private router:Router) { }
  notificationList:any[];
  count:number;
  clickedNotification:any[]=[];
  empId: any;
  notificationListSub:Subscription;
  modalScrollDistance = 1;
  modalScrollThrottle = 150;
  itemPerPage:number=20;
  pageNumber:number=1;
  lastpage:boolean = false;
  newList:any[] = [];
  isLoder:boolean = false;
  disableScroll= false;
  totalRecord:number;
  ngOnInit() {
    this.count = this.data.count;   
    this.notificationList = this.data.notificationList;
    this.totalRecord= this.data.totalRecord;
    if(this.notificationList.length === this.totalRecord){
      this.disableScroll = true;
    }
    this.highlightActiveNotification();    
    let currentUser= JSON.parse(localStorage.getItem('currentUser'));    
    this.empId = currentUser.empId;    
    this.getNotifications();
    this.notificationListSub = this.notificationSevice.updatedList.subscribe((res)=>{
      this.count = res.count;
      this.notificationList = res.list;
      this.highlightActiveNotification();      
    })    
  }  
  onModalScrollDown () {
    this.disableScroll = true;    
    this.loadNewlist();
  }

  highlightActiveNotification(){
    if(this.notificationList){
      this.notificationList.forEach((item,index) => {
        item['active'] = index <= this.count-1 ? true:false; 
        let dt = item.userModifiedTimestamp; // Assuming it's in a format like "2023-09-08T12:34:56"
        let date = new Date(dt + "Z");
        item.userModifiedTimestamp = date;
      });
    }    
  }

  onClickItem(index){
    this.notificationList.map((s,i)=> {
      if(i==index){
        s.active = false;
      }
    })    
    let updateCount = this.notificationList.filter(s=>s.active).length;
    this.notificationSevice.onUpdateCount(updateCount);
  }

  getNotifications() {
    let apiList = [this.apiService.get(`Notifications/NotificationCount/${this.empId}`),
    this.apiService.get(`Notifications/NotificationList?empId=`+this.empId+'&pageSize=20'+'&pageNumber=1')
    ]
    this.apiService.forkJoinData(apiList).subscribe((res: any) => {
      this.count = res[0].result;
      this.notificationList = res[1].result.notificationList;
      this.totalRecord = res[1].result.totalRecordCount;
      if(this.notificationList){
        this.notificationList.forEach((item,index) => {
          item['active'] = index <= this.count - 1? true:false; 
          let dt = item.userModifiedTimestamp; // Assuming it's in a format like "2023-09-08T12:34:56"
          let date = new Date(dt + "Z");
          item.userModifiedTimestamp = date;
        });
        this.notificationSevice.onUpdateCount(this.count);
      }
        // this.showNoticationIcon = true;
     
    },(err: any) => {
      // console.log(err)
      this.toastMsg.error(err, 'Server Error!')
    }
    )
  } 
  loadNewlist(){
    this.isLoder = true;
    this.pageNumber += 1;
    this.itemPerPage =20;    
    this.apiService.get(`Notifications/NotificationList?empId=`+ this.empId +'&pageSize='+this.itemPerPage+'&pageNumber='+this.pageNumber).subscribe((res:any) =>{      
      this.isLoder = false;
      this.newList = res.result.notificationList;
      this.totalRecord = res.result.totalRecordCount;
      
      if(this.newList){        
        this.newList.forEach((item,index) => {
          let dt = item.userModifiedTimestamp; // Assuming it's in a format like "2023-09-08T12:34:56"
          let date = new Date(dt + "Z");
          item.userModifiedTimestamp = date;
          if(this.notificationList.length){
            if(this.count > this.notificationList.length){
              item['active'] = index <= this.count - (this.notificationList.length) - 1? true:false; 
            }
          }
        })
        this.notificationList.push(...this.newList);
        if(this.notificationList.length === this.totalRecord){
          this.disableScroll = true
        }else{
          this.disableScroll = false;
        }        
        let count = 0;
  for (const obj of this.notificationList) {
    if (obj.active === true) {
      count++;
    }
  }
  
      }
    },(err: any) => {
      this.disableScroll = false;
      // console.log(err)
      this.toastMsg.error(err, 'Server Error!')
    })
  }

  navSettings(){    
    this.router.navigate([`/ui/settings/notification`]);
    this.dialog.closeAll();
  }  
  
  ngOnDestroy() {    
    this.notificationListSub.unsubscribe();
  }
}
