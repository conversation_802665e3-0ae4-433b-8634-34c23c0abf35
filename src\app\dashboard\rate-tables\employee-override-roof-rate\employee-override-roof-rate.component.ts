import { Component, OnInit, ViewChild, Input } from '@angular/core';
import { MatPaginator } from '@angular/material/paginator';
import { MatSort } from '@angular/material/sort';
import { MatTableDataSource } from '@angular/material/table';
import { Observable, Subscriber, OperatorFunction } from 'rxjs';
import { UntypedFormBuilder, UntypedFormGroup, Validators} from "@angular/forms";
import { ApiService } from "../../../services/api.service";
import { ToastrService } from 'ngx-toastr';
import { DatePipe, DecimalPipe } from '@angular/common';
import { TableFilterPipe } from '../../../pipe/table-filter.pipe';
import { IContact } from 'src/app/model/one-time-payment.model';
import { debounceTime, distinctUntilChanged, map, filter } from 'rxjs/operators';
import { MatDialog } from '@angular/material/dialog';
import { EmployeeOverrideRoofRateDialogComponent } from './employee-override-roof-rate-dialog/employee-override-roof-rate-dialog.component';


@Component({
  selector: 'app-employee-override-roof-rate',
  templateUrl: './employee-override-roof-rate.component.html',
  styleUrls: ['./employee-override-roof-rate.component.css']
})
export class EmployeeOverrideRoofRateComponent implements OnInit {
  @Input() editedRate: string;
  base64Output : any;
  tableArr: EmployeeOverrideRoofingModel[] = [];
  employeeOverrideRoofingRate: any;
  employeeOverrideRoofingRateEdit;
  employeeOverrideRoofingRateAdd;
  employeeOverrideFormAdd: UntypedFormGroup;
  employeeOverrideFormEdit: UntypedFormGroup;
  editRow: boolean = false;
  addRow: boolean = false;
  originalDataSource: any;
  dataSource: any;
  searchText: string = "";
  displayedColumns = [];
  dropdowns: any;
  fileUpload: File | null = null;
  contacts: IContact[] = [];
  hideEmployeeTitle: boolean = false;
  maxStartDateEdit: Date;
  minEndDateEdit: Date;
  maxStartDateAdd: Date;
  minEndDateAdd: Date;
  errorEntries: any;
  roleNames: string[] = ["VP", "RM", "SRM", "RVP", "SM", "ADM", "TM"];
  tiers: string[] = ["5.99-"];
  columnNames = [{
      id: "roleName",
      value: "Role Name"
    },
    {
      id: "overrideRate",
      value: "Override Rate"
    },
    {
      id: "tier",
      value: "Roof Sqare Range"
    },
    {
      id: "effectiveStartDate",
      value: "Effective Start Date"
    },
    {
      id: "effectiveEndDate",
      value: "Effective End Date"
    },
    
    // {
    //   id: "fromRoofSquares",
    //   value: "From Roof Squares"
    // },
    // {
    //   id: "toRoofSquares",
    //   value: "To Roof Squares"
    // }
  ];
  @ViewChild(MatSort, { static: true }) sort: MatSort;
  @ViewChild(MatPaginator, { static: true }) paginator: MatPaginator;

  formatter = (c: IContact) => c.contactName;
  search: OperatorFunction<string, readonly { contactId, contactName }[]> = (text$: Observable<string>) => text$.pipe(
    debounceTime(0),
    distinctUntilChanged(),
    filter(term => term.length >= 2),
    map(term => this.contacts.filter(c => new RegExp(term, 'mi').test(c.contactName)).slice(0, 10))
  )

  constructor(private formBuilder: UntypedFormBuilder, public apiService: ApiService, private toastMsg: ToastrService, private datePipe: DatePipe,
     private decimalPipe: DecimalPipe, private pipe: TableFilterPipe, private dialog: MatDialog) { }

  ngOnInit(): void {
    this.displayedColumns = this.columnNames.map(x => x.id);
    this.GetAllEmployeeOverrideRoofingRates();

    this.employeeOverrideFormEdit = this.formBuilder.group({
      roleName: ["", [Validators.required]],
      tier: ["", [Validators.required]],
      // fromRoofSquares: ["", [Validators.required]],
      // toRoofSquares: ["", [Validators.required]],
      employeeOverrideRoofingRateId: [0, [Validators.required]],
      effectiveStartDate: ["", [Validators.required]],
      effectiveEndDate: [""],
      overrideRate: [""],
    });

    this.employeeOverrideFormAdd = this.formBuilder.group({
      roleName: ["", [Validators.required]],
      tier: ["", [Validators.required]],
      // fromRoofSquares: ["", [Validators.required]],
      // toRoofSquares: ["", [Validators.required]],
      effectiveStartDate: ["", [Validators.required]],
      effectiveEndDate: [""],
      overrideRate: [""]
    });
   // this.onChanges();
  }

  // onChanges() {
  //   this.employeeOverrideFormAdd.get("salesDivision").valueChanges.subscribe(val => {
  //     if(val === this.salesDivisions[2]){
  //       this.employeeOverrideFormAdd.controls['employeeTitle'].enable();
  //       this.hideEmployeeTitle = true;
  //       }
  //       else{
  //         this.employeeOverrideFormAdd.controls['employeeTitle'].reset();
  //         this.employeeOverrideFormAdd.controls['employeeTitle'].disable({ onlySelf: true });
  //         this.hideEmployeeTitle = false;
  //       }
  //     }
  //   );
  //   this.employeeOverrideFormEdit.get("salesDivision").valueChanges.subscribe(val => {
  //     if(val === this.salesDivisions[2]){
  //         this.employeeOverrideFormEdit.controls['employeeTitle'].enable();
  //         this.hideEmployeeTitle = true;
  //       }
  //       else{
  //         this.employeeOverrideFormEdit.controls['employeeTitle'].reset();
  //         this.employeeOverrideFormEdit.controls['employeeTitle'].disable({ onlySelf: true });
  //         this.hideEmployeeTitle = false;
  //       }
  //     }
  //   );

  //   this.employeeOverrideFormAdd.get("employeeTitle").valueChanges.subscribe(val => {
  //     if(val === this.employeeTitles[0]){
  //       this.employeeOverrideFormAdd.controls['rate'].reset();
  //       this.employeeOverrideFormAdd.controls['rate'].setValue("0.00");
  //       this.employeeOverrideFormAdd.controls['rate'].disable({ onlySelf: true });
  //       }
  //       else{
  //         this.employeeOverrideFormAdd.controls['rate'].enable();
  //       }
  //     }
  //   );

  //   this.employeeOverrideFormEdit.get("employeeTitle").valueChanges.subscribe(val => {
  //     if(val === this.employeeTitles[0]){
  //       this.employeeOverrideFormEdit.controls['rate'].reset();
  //       this.employeeOverrideFormEdit.controls['rate'].setValue("0.00");
  //       this.employeeOverrideFormEdit.controls['rate'].disable({ onlySelf: true });
  //       }
  //       else{
  //         this.employeeOverrideFormEdit.controls['rate'].enable();
  //       }
  //     }
  //   );

  // }

  getRoleNames() {
    // this.apiService.get('GetData/GetActiveContacts')
    //   .subscribe(data => {
    //     if (data && data.result) {
    //       this.contacts = data.result.map(type => { return <IContact>{ contactId: type.id, contactName: type.name } })
    //     }
    //   }, err => {
    //     this.toastMsg.error(err.message, "Error!");
    //   })
  }

  GetAllEmployeeOverrideRoofingRates() {
    this.apiService.get('EmployeeOverrideRoofRate')
      .subscribe(data => {
        this.employeeOverrideRoofingRate = data;
        this.displayedColumns = this.columnNames.map(x => x.id);
        this.createTable();
      }, (err: any) => {
        this.toastMsg.error(err.message, 'Server Error!');
      });
  }

  rowClick(employeeoverriderate: any) {
    this.editRow = !this.editRow;
    // var overrideRate = this.employeeOverrideRoofingRate.filter(x => x.roleName === employeeoverriderate.roleName && x.fromRoofSquares === employeeoverriderate.fromRoofSquares && x.toRoofSquares === employeeoverriderate.toRoofSquares
    //  && this.decimalPipe.transform(x.overrideRate, '1.2-2') === employeeoverriderate.overrideRate && this.datePipe.transform(x.effectiveStartDate, 'yyyy-MM-dd') === this.datePipe.transform(employeeoverriderate.effectiveStartDate, 'yyyy-MM-dd'));
     var overrideRate = this.employeeOverrideRoofingRate.filter(x => x.roleName === employeeoverriderate.roleName && x.tier === employeeoverriderate.tier
      && this.decimalPipe.transform(x.overrideRate, '1.2-2') === employeeoverriderate.overrideRate && this.datePipe.transform(x.effectiveStartDate, 'yyyy-MM-dd') === this.datePipe.transform(employeeoverriderate.effectiveStartDate, 'yyyy-MM-dd'));
  
     this.employeeOverrideRoofingRateEdit = overrideRate;

    this.employeeOverrideFormEdit.controls['employeeOverrideRoofingRateId'].setValue(this.employeeOverrideRoofingRateEdit[0].employeeOverrideRoofingRateId);
    this.employeeOverrideFormEdit.controls['roleName'].setValue(this.employeeOverrideRoofingRateEdit[0].roleName);
    this.employeeOverrideFormEdit.controls['tier'].setValue(this.employeeOverrideRoofingRateEdit[0].tier);
    // this.employeeOverrideFormEdit.controls['fromRoofSquares'].setValue(this.employeeOverrideRoofingRateEdit[0].fromRoofSquares);
    // this.employeeOverrideFormEdit.controls['toRoofSquares'].setValue(this.employeeOverrideRoofingRateEdit[0].toRoofSquares);
    this.employeeOverrideFormEdit.controls['effectiveStartDate'].setValue(this.datePipe.transform(this.employeeOverrideRoofingRateEdit[0].effectiveStartDate, 'yyyy-MM-dd'));
    this.employeeOverrideFormEdit.controls['effectiveEndDate'].setValue(this.datePipe.transform(this.employeeOverrideRoofingRateEdit[0].effectiveEndDate, 'yyyy-MM-dd'));
    this.employeeOverrideFormEdit.controls['overrideRate'].setValue(this.employeeOverrideRoofingRateEdit[0].overrideRate);
    this.employeeOverrideFormEdit.controls['roleName'].disable();
    this.employeeOverrideFormEdit.controls['tier'].disable();
    // this.employeeOverrideFormEdit.controls['fromRoofSquares'].disable();
    // this.employeeOverrideFormEdit.controls['toRoofSquares'].disable();

    this.employeeOverrideFormAdd.controls['roleName'].setValue(this.employeeOverrideRoofingRateEdit[0].roleName);
    this.employeeOverrideFormAdd.controls['tier'].setValue(this.employeeOverrideRoofingRateEdit[0].tier);
    // this.employeeOverrideFormAdd.controls['fromRoofSquares'].setValue(this.employeeOverrideRoofingRateEdit[0].fromRoofSquares);
    // this.employeeOverrideFormAdd.controls['toRoofSquares'].setValue(this.employeeOverrideRoofingRateEdit[0].toRoofSquares);
    this.employeeOverrideFormAdd.controls['effectiveStartDate'].setValue(this.datePipe.transform(this.employeeOverrideRoofingRateEdit[0].effectiveStartDate, 'yyyy-MM-dd'));
    //this.employeeOverrideFormAdd.controls['effectiveEndDate'].setValue(this.datePipe.transform(this.employeeOverrideRoofingRateEdit[0].effectiveEndDate, 'yyyy-MM-dd'));
    this.employeeOverrideFormAdd.controls['overrideRate'].setValue(this.decimalPipe.transform(this.employeeOverrideRoofingRateEdit[0].overrideRate, '1.2-2'));
  
    // if(!this.employeeOverrideFormEdit.controls['employeeTitle'].disabled){
    //   this.hideEmployeeTitle = true;
    // }
    // else{
    //   this.hideEmployeeTitle = false;
    // }

  }

  Add() {
    this.employeeOverrideRoofingRateAdd = this.tableArr;
    this.addRow = !this.addRow;

    this.employeeOverrideFormAdd.controls['roleName'].setValue(this.employeeOverrideRoofingRateAdd[0].roleName);
    this.employeeOverrideFormAdd.controls['tier'].setValue(this.employeeOverrideRoofingRateAdd[0].tier);
    //this.employeeOverrideFormAdd.controls['fromRoofSquares'].setValue(this.employeeOverrideRoofingRateAdd[0].fromRoofSquares);
    //this.employeeOverrideFormAdd.controls['toRoofSquares'].setValue(this.employeeOverrideRoofingRateAdd[0].toRoofSquares);
    this.employeeOverrideFormAdd.controls['effectiveStartDate'].setValue(this.datePipe.transform(this.employeeOverrideRoofingRateAdd[0].effectiveStartDate, 'yyyy-MM-dd'));
    //this.employeeOverrideFormAdd.controls['effectiveEndDate'].setValue(this.datePipe.transform(this.employeeOverrideRoofingRateAdd[0].effectiveEndDate, 'yyyy-MM-dd'));
    this.employeeOverrideFormAdd.controls['overrideRate'].setValue(this.employeeOverrideRoofingRateAdd[0].overrideRate);
   
    // if(!this.employeeOverrideFormAdd.controls['employeeTitle'].disabled){
    //   this.hideEmployeeTitle = true;
    // }
    // else{
    //   this.hideEmployeeTitle = false;
    // }
  }

  createTable() {
    let tableArr: EmployeeOverrideRoofingModel[] = [];
    for(let i:number = 0; i <= this.employeeOverrideRoofingRate.length - 1; i++) {
      let currentRow = this.employeeOverrideRoofingRate[i];
      if(i==0)
      {
        this.tableArr[0] =this.employeeOverrideRoofingRate[0];
      }
      // tableArr.push({employeeOverrideRoofingRateId: currentRow.employeeOverrideRoofingRateId, roleName: currentRow.roleName, tier:currentRow.tier, fromRoofSquares: currentRow.fromRoofSquares, toRoofSquares: currentRow.toRoofSquares, effectiveStartDate: this.datePipe.transform(currentRow.effectiveStartDate), effectiveEndDate: this.datePipe.transform(currentRow.effectiveEndDate),
      //   overrideRate: this.decimalPipe.transform(currentRow.overrideRate, '1.2-2')});
      tableArr.push({employeeOverrideRoofingRateId: currentRow.employeeOverrideRoofingRateId, roleName: currentRow.roleName, tier:currentRow.tier, effectiveStartDate: this.datePipe.transform(currentRow.effectiveStartDate), effectiveEndDate: this.datePipe.transform(currentRow.effectiveEndDate),
        overrideRate: this.decimalPipe.transform(currentRow.overrideRate, '1.2-2')});
    }
    this.dataSource = new MatTableDataSource(tableArr);
    this.originalDataSource = tableArr;
    this.dataSource.sort = this.sort;
    this.dataSource.paginator = this.paginator;
  }

  onEditSubmit() {
      var values = {
        employeeOverrideRoofingRateId: this.employeeOverrideFormEdit.controls.employeeOverrideRoofingRateId.value,
        roleName: this.employeeOverrideFormEdit.controls.roleName.value,
        tier: this.employeeOverrideFormEdit.controls.tier.value,
        //fromRoofSquares: this.employeeOverrideFormEdit.controls.fromRoofSquares.value,
        //toRoofSquares: this.employeeOverrideFormEdit.controls.toRoofSquares.value,
        effectiveStartDate: this.employeeOverrideFormEdit.controls.effectiveStartDate.value,
        effectiveEndDate: this.employeeOverrideFormEdit.controls.effectiveEndDate.value,
        overrideRate: this.employeeOverrideFormEdit.controls.overrideRate.value
      }

      var sDate = new Date(values.effectiveStartDate);
      sDate.setDate(sDate.getDate() + 1);
      var fom = new Date(sDate.getFullYear(), sDate.getMonth(), 1);
      var convertedEOM = null;
      var valid = true;

      if(values.effectiveEndDate){
        var eDate = new Date(values.effectiveEndDate);
        var eom = new Date(eDate.getFullYear(), eDate.getMonth()+1, 0);
        convertedEOM = this.datePipe.transform(eom, 'yyyy-MM-dd');
        valid = values.effectiveStartDate < values.effectiveEndDate ? true : false;
      }
      

      if(values.effectiveStartDate == this.datePipe.transform(fom,  'yyyy-MM-dd') && (!values.effectiveEndDate || values.effectiveEndDate == convertedEOM) && valid){
        this.apiService.post('EmployeeOverrideRoofRate/UpdateOverrideRate', values)
          .subscribe(data => {
            this.toastMsg.success('Employee Override Roofing Rate Updated Successfully');
            this.GetAllEmployeeOverrideRoofingRates();
            this.editRow = !this.editRow;
          }, (err: any) => {
          if (err?.err?.message)
            this.toastMsg.error(err.message, "Server Error!");
          if (typeof err === "string") {
            let isJsonObject = false;            
            let parsedErr: any;
            try {
              parsedErr = JSON.parse(err);
              isJsonObject =parsedErr && typeof parsedErr === "object" &&!Array.isArray(parsedErr);
            } catch (e) {
              isJsonObject = false;
            }
            if (isJsonObject) {
              this.toastMsg.error(parsedErr.result, "Error!");
            } else this.toastMsg.error(err, "Error!");
          }
          if (err?.result) {
            this.toastMsg.error(err.result, "Error!");
          }
        });
        }
        else{
          this.toastMsg.error('Invalid Date Range, Submission must be first and last day of the month with end date effective after start date.');
        }
    }

    onAddSubmit() {
      var values = {
        roleName: this.employeeOverrideFormAdd.controls.roleName.value,
        tier: this.employeeOverrideFormAdd.controls.tier.value,
        //fromRoofSquares: this.employeeOverrideFormAdd.controls.fromRoofSquares.value,
        //toRoofSquares: this.employeeOverrideFormAdd.controls.toRoofSquares.value,
        effectiveStartDate: this.employeeOverrideFormAdd.controls.effectiveStartDate.value,
        effectiveEndDate: this.employeeOverrideFormAdd.controls.effectiveEndDate.value,
        overrideRate: this.employeeOverrideFormAdd.controls.overrideRate.value == null ? 0.00 : this.employeeOverrideFormAdd.controls.overrideRate.value,
      }

      var sDate = new Date(values.effectiveStartDate);
      sDate.setDate(sDate.getDate() + 1);
      var fom = new Date(sDate.getFullYear(), sDate.getMonth(), 1);
      var convertedEOM = null;
      var valid = true;

      if(values.effectiveEndDate){
        var eDate = new Date(values.effectiveEndDate);
        var eom = new Date(eDate.getFullYear(), eDate.getMonth()+1, 0);
        convertedEOM = this.datePipe.transform(eom, 'yyyy-MM-dd');
        var valid = values.effectiveStartDate < values.effectiveEndDate ? true : false;
      }

      if(values.effectiveStartDate == this.datePipe.transform(fom,  'yyyy-MM-dd') && (!values.effectiveEndDate || values.effectiveEndDate == convertedEOM) && valid){
        this.apiService.post('EmployeeOverrideRoofRate/AddOverrideRate', values)
          .subscribe(data => {
            this.toastMsg.success('Employee Override Roofing Rate Added Successfully');
            this.GetAllEmployeeOverrideRoofingRates();
            this.addRow = !this.addRow;
          }, (err: any) => {
          if (err?.err?.message)
            this.toastMsg.error(err.message, "Server Error!");
          if (typeof err === "string") {
            let isJsonObject = false;            
            let parsedErr: any;
            try {
              parsedErr = JSON.parse(err);
              isJsonObject =parsedErr && typeof parsedErr === "object" &&!Array.isArray(parsedErr);
            } catch (e) {
              isJsonObject = false;
            }
            if (isJsonObject) {
              this.toastMsg.error(parsedErr.result, "Error!");
            } else this.toastMsg.error(err, "Error!");
          }
          if (err?.result) {
            this.toastMsg.error(err.result, "Error!");
          }
        });
      }
      else{
        this.toastMsg.error('Invalid Date Range, Submission must be first and last day of the month with end date effective after start date.');
      }


    }

    // formatEmployeeTitle(title: string){
    //   var result = "";
    //   if(title == this.employeeTitles[0]){
    //     result = "DM";
    //   }
    //   else if(title == this.employeeTitles[1]) {
    //     result = "RM";
    //   }
    //   else if (title == this.employeeTitles[2]){
    //     result = "ADM"
    //   }

    //   return result;
    // }

    deleteRate(){
      var values = {
        employeeOverrideRoofingRateId: this.employeeOverrideFormEdit.controls.employeeOverrideRoofingRateId.value,
        roleName: this.employeeOverrideFormEdit.controls.roleName.value,
        tier: this.employeeOverrideFormEdit.controls.tier.value,
        // fromRoofSquares: this.employeeOverrideFormEdit.controls.fromRoofSquares.value,
        // toRoofSquares: this.employeeOverrideFormEdit.controls.toRoofSquares.value,
        effectiveStartDate: this.employeeOverrideFormEdit.controls.effectiveStartDate.value,
        effectiveEndDate: this.employeeOverrideFormEdit.controls.effectiveEndDate.value,
        overrideRate: this.employeeOverrideFormEdit.controls.overrideRate.value,
     }

      this.apiService.post('EmployeeOverrideRoofRate/DeleteOverrideRate', values)
        .subscribe(data => {
          this.toastMsg.success('Employee Override Rate Removed Successfully');
          this.GetAllEmployeeOverrideRoofingRates();
          this.editRow = !this.editRow;
        }, (err: any) => {
          if (err?.err?.message)
            this.toastMsg.error(err.message, "Server Error!");
          if (typeof err === "string") {
            let isJsonObject = false;            
            let parsedErr: any;
            try {
              parsedErr = JSON.parse(err);
              isJsonObject =parsedErr && typeof parsedErr === "object" &&!Array.isArray(parsedErr);
            } catch (e) {
              isJsonObject = false;
            }
            if (isJsonObject) {
              this.toastMsg.error(parsedErr.result, "Error!");
            } else this.toastMsg.error(err, "Error!");
          }
          if (err?.result) {
            this.toastMsg.error(err.result, "Error!");
          }
        });
    }

    searchForItem():void {
      let filteredResults: EmployeeOverrideRoofingModel[] = [];
      if (this.searchText == '') {
        this.dataSource = new MatTableDataSource(this.originalDataSource);
        this.dataSource.sort = this.sort;
      this.dataSource.paginator = this.paginator;
      } else {
        filteredResults = this.pipe.transform(this.originalDataSource, this.searchText);
        this.dataSource = new MatTableDataSource(filteredResults);
        this.dataSource.sort = this.sort;
        this.dataSource.paginator = this.paginator;
      }
    }

    clearEditStartDate(date: HTMLInputElement) {
      date.value = "";
      this.employeeOverrideFormEdit.controls.effectiveStartDate.setValue('');
    }
    clearEditEndDate(date: HTMLInputElement) {
      date.value = "";
      this.employeeOverrideFormEdit.controls.effectiveEndDate.setValue('');
    }
    clearAddStartDate(date: HTMLInputElement) {
      date.value = "";
      this.employeeOverrideFormAdd.controls.effectiveStartDate.setValue('');
    }
    clearAddEndDate(date: HTMLInputElement) {
      date.value = "";
      this.employeeOverrideFormAdd.controls.effectiveEndDate.setValue('');
    }

    handleFileInput($event: Event) {
      var file = ($event.target as HTMLInputElement).files[0];
      this.convertFile(file);
      ($event.target as HTMLInputElement).value = "";
    }

    convertFile(file: File) {
      this.base64Output = null;
      var observe = new Observable((subscriber: Subscriber<any>) => {
        this.readFile(file, subscriber);
      });
      observe.subscribe((d) => {
        this.base64Output = d;
      });
    }

    readFile(file: File, subscriber: Subscriber<any>){
      var reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onload=()=>{
        subscriber.next(reader.result);
        subscriber.complete();
      }
      reader.onerror=(error)=>{
        subscriber.error(error);
        subscriber.complete();
      }
    }
    
    upload(){
    if(!this.base64Output){
      this.toastMsg.error('File has not been uploaded or processed!');
    }
    else{
      this.apiService.post('EmployeeOverrideRoofRate/UpdateFromExcel', {"output": this.base64Output.split(';base64,')[1]})
      .subscribe((data) => {
        var res: any = data;
        if(res.length == 0){
          this.GetAllEmployeeOverrideRoofingRates();
          this.toastMsg.success('Employee Override Roofing Rate Updated Successfully');
          this.base64Output = null;
        }
        else{
          this.GetAllEmployeeOverrideRoofingRates();
          this.errorEntries = data;
          this.dialog.open(EmployeeOverrideRoofRateDialogComponent, {
            width: '800px',
            data: {
              errors: this.errorEntries
            },
          });
          this.base64Output = null;
        }

      }, (err: any) => {
          if (err?.err?.message)
            this.toastMsg.error(err.message, "Server Error!");
          if (typeof err === "string") {
            let isJsonObject = false;            
            let parsedErr: any;
            try {
              parsedErr = JSON.parse(err);
              isJsonObject =parsedErr && typeof parsedErr === "object" &&!Array.isArray(parsedErr);
            } catch (e) {
              isJsonObject = false;
            }
            if (isJsonObject) {
              this.toastMsg.error(parsedErr.result, "Error!");
            } else this.toastMsg.error(err, "Error!");
          }
          if (err?.result) {
            this.toastMsg.error(err.result, "Error!");
          }
        });
    }
  }

}

export interface EmployeeOverrideRoofingModel{
  employeeOverrideRoofingRateId: number;
  roleName: string;
  tier: string;
  // fromRoofSquares: string;
  // toRoofSquares: string;
  effectiveStartDate: string;
  effectiveEndDate: string;  
  overrideRate: string;
}