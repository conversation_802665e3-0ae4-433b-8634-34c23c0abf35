import { BrowserModule } from '@angular/platform-browser';
import { NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { AppRoutingModule } from './app-routing.module';
import { DashboardModule } from './dashboard/dashboard.module';
import { AppComponent } from './app.component';
// import { HTTP_INTERCEPTORS, HttpClientModule } from '@angular/common/http';
import { HttpClientModule, HttpClient, HTTP_INTERCEPTORS } from '@angular/common/http';
import { ApiService } from './services/api.service';
import { TokenInterceptor } from './services/interceptor';
import { ErrorInterceptor } from './services/error.interceptor';
import { HeaderComponent } from './header/header.component';
import { ForgotpasswordComponent } from './forgotpassword/forgotpassword.component';
import { ResetpasswordComponent } from './resetpassword/resetpassword.component';
import { LogoutComponent } from './logout/logout.component';
import { AuthGuard } from './guards/auth.guard';
import { FooterComponent } from './footer/footer.component';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { ToastrModule } from 'ngx-toastr';
import { NotfoundComponent } from './notfound/notfound.component';
import { LoaderComponent } from './loader/loader.component';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { MatInputModule } from '@angular/material/input';
import { MatPaginatorModule } from '@angular/material/paginator';
import { MatSelectModule } from '@angular/material/select';
import { MatToolbarModule } from '@angular/material/toolbar';
import { MatTooltipModule } from '@angular/material/tooltip';
import { DatePipe, DecimalPipe } from '@angular/common';
import { DragDropModule } from '@angular/cdk/drag-drop';
import { MainPipeModule } from './pipe/main-pipe.module';
import { SharedModule } from './shared-module/shared.module';
import { MatSidenavModule } from '@angular/material/sidenav';
import { MatButtonModule } from '@angular/material/button';
import { MatDialogModule } from '@angular/material/dialog';
import { SidenavService } from './services/sidenav.service';
import { SidebarComponent } from './sidebar/sidebar.component';
import { SearchComponent } from './search/search.component';
import { NgxPaginationModule } from 'ngx-pagination';

import { environment } from 'src/environments/environment';
import { MsalModule, MsalInterceptor, MSAL_INSTANCE, MsalService, MsalInterceptorConfiguration, MSAL_INTERCEPTOR_CONFIG } from '@azure/msal-angular';
import { MsalUserService } from './services/msaluser.service';

export const protectedResourceMap: any =
  [
    [environment.baseUrl, environment.scopeUri
    ]
  ];
import { SharedSidebarService } from './shared/sidebar-icon';
import { NewNavigationComponent } from './new-navigation/new-navigation.component';
import { LayoutModule } from '@angular/cdk/layout';
import { MatIconModule } from '@angular/material/icon';
import { MatListModule } from '@angular/material/list';
import { UnauthorizedComponent } from './unauthorized/unauthorized.component';
import { ExportService } from './services/export.service';
import { NgMultiSelectDropDownModule } from 'ng-multiselect-dropdown';
import { NotificationDialogComponent } from './notification-dialog/notification-dialog.component';
import { TimeagoModule } from 'ngx-timeago';
import { InfiniteScrollModule } from 'ngx-infinite-scroll';
import { IPublicClientApplication, InteractionType, PublicClientApplication,BrowserCacheLocation } from '@azure/msal-browser';

export function MSALInterceptorConfigFactory(): MsalInterceptorConfiguration {
  const protectedResourceMap = new Map<string, Array<string>>();
  protectedResourceMap.set(environment.baseUrl , environment.scopeUri);
  return {
    interactionType: InteractionType.Redirect,
    protectedResourceMap
  };
}
export function MSALInstanceFactory(): IPublicClientApplication{
    return new PublicClientApplication({
      auth:{
        clientId:environment.uiClienId,
        redirectUri:environment.redirectUrl,
        authority:environment.authority,
      },cache: {
        cacheLocation: BrowserCacheLocation.LocalStorage
      }
    })
  }

@NgModule({
    declarations: [
        AppComponent,
        HeaderComponent,
        ForgotpasswordComponent,
        ResetpasswordComponent,
        LogoutComponent,
        FooterComponent,
        NotfoundComponent,
        LoaderComponent,
        SidebarComponent,
        SearchComponent,
        NewNavigationComponent,
        UnauthorizedComponent,
        NotificationDialogComponent
    ],
    imports: [
        BrowserModule,
        MatProgressSpinnerModule,
        MatTooltipModule,
        MatDialogModule,
        FormsModule,
        ReactiveFormsModule,
        AppRoutingModule,
        DashboardModule,
        HttpClientModule,
        DragDropModule,
        BrowserAnimationsModule,
        ToastrModule.forRoot({
            timeOut: 3000,
            positionClass: 'toast-top-full-width',
            preventDuplicates: true,
            maxOpened: 1
        }),
        MsalModule,
        MainPipeModule,
        NgMultiSelectDropDownModule.forRoot(),
        SharedModule,
        MatSidenavModule,
        MatButtonModule,
        MatPaginatorModule,
        NgxPaginationModule,
        MatSelectModule,
        MatInputModule,
        MatAutocompleteModule,
        MatToolbarModule,
        LayoutModule,
        MatIconModule,
        MatListModule,
        TimeagoModule.forRoot(),
        InfiniteScrollModule
    ],
    exports: [
        MatTooltipModule
    ],
    providers: [
        ApiService,
        {
            provide:MSAL_INSTANCE,
            useFactory:MSALInstanceFactory
        },
        { provide: MSAL_INTERCEPTOR_CONFIG, useFactory: MSALInterceptorConfigFactory },
          MsalService,
        { provide: HTTP_INTERCEPTORS, useClass: TokenInterceptor, multi: true },
        { provide: HTTP_INTERCEPTORS, useClass: ErrorInterceptor, multi: true },
        {
          provide: HTTP_INTERCEPTORS,
          useClass: MsalInterceptor,
          multi: true
        },
        AuthGuard,
        DatePipe,
        DecimalPipe,
        SidenavService,
        HttpClient,
        MsalUserService,
        // {
        //   provide: HTTP_INTERCEPTORS, useClass: MsalInterceptor, multi: true
        // },
        SharedSidebarService,
        ExportService
    ],
    bootstrap: [AppComponent]
})
export class AppModule { }
