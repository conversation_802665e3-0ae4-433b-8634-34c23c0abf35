import { Component, OnInit } from "@angular/core";
import { Router, ActivatedRoute } from "@angular/router";
import { ApiService } from "../../services/api.service";
import { ToastrService } from "ngx-toastr";
declare var $: any;
import { DatePipe } from "@angular/common";
import { IPaymentBook } from "../../model/pay-book.model";
import { IEmployeeIncentive } from "src/app/model/employee-incentive.model";
import { IContactBasePayStructure } from "src/app/model/base-pay-structure.model";
import { IRule } from 'src/app/model/rule.model';
import { IPlanDetails } from 'src/app/model/plan-details.model';
import { IBasicPlan } from 'src/app/model/basic-plan.model';
import { IContactPlan } from 'src/app/model/contact-plan.model';
import { IPlanDetail } from "src/app/model/plan.model";
import { AddOnRule } from "../change-plan/payment-book-schedule-dialog/payment-book-schedule-dialog.component";
import { MatDialog } from "@angular/material/dialog";
import { EmployeeIncentiveDialogComponent } from "../change-plan/employee-incentive-dialog/employee-incentive-dialog.component";
import { IPrompt, Prompt } from "src/app/model/prompt.model";
import { IRulePrompt } from "src/app/model/rule-prompt.model";
import { IRuleItem } from "src/app/model/rule-item.model";
import { AddPlanInclusionDialogComponent } from "./add-plan-inclusion-dialog/add-plan-inclusion-dialog/add-plan-inclusion-dialog.component";
import { IInclusionRules } from "src/app/model/IInclusionRules";

@Component({
  selector: "app-sales-rep-configuration",
  templateUrl: "./sales-rep-configuration.component.html",
  styleUrls: ["./sales-rep-configuration.component.css"]
})
export class SalesRepConfigurationComponent implements OnInit {
  // data: any[] = [{ "parent": "Base Pay", "child": ["On Watt Sold"] }, { "parent": "Bonus", "child": ["Sonnova Lease", "Sunnova Loan"] }, { "parent": "Bonus Incentives", "child": ["Backend"] },
  // { "parent": "Rate Incentives", "child": ["High Flyer", "President’s Club"] }, { "parent": "Payment Books", "child": ["Straight Pay"] }];

  ruleType: string;
  showPreview: boolean = false;
  ruleId: number;
  basePayRuleId: number;
  contactsDetails: any;
  planDetails: IPlanDetails;
  contactId: number;
  planId: number = 0;
  contactPlanId: number = 0;
  contactPlan: IContactPlan;
  basicPlan: any[] = [];
  noRule: boolean = false;
  ruleName: string;
  addOns: AddOnRule[] = [];
  metadataPrompts: Prompt[] = [];
  promptsValues: object = {};
  planInclusionRules: IInclusionRules;
  inclusionRuleSelected: boolean = false;
  exclusionWithInclusion: boolean = false;

  constructor(
    private router: Router,
    public apiService: ApiService,
    private toastMsg: ToastrService,
    datePipe: DatePipe,
    private activatedRoute: ActivatedRoute,
    private dialog: MatDialog
  ) {
    // this.contactId = this.activatedRoute.snapshot.params.contact_id;
    //this.planId = this.activatedRoute.snapshot.params.plan_id
  }

  ngOnInit() {
    // if(this.planId != 'new' && this.planId > 0)
    //   this.getPlanDetails(this.planId)
    this.activatedRoute.params.subscribe(params => {
      this.contactId = params.contact_id;
      if (params.plan_id != undefined) {
        this.planId = params.plan_id;
      }
      if (params.contact_plan_id != undefined) {
        this.contactPlanId = params.contact_plan_id;
      }
      this.basicPlan = [];

      this.showPreview = false;
      this.ruleType = null;
      this.ruleId = null;

      this.getRuleTypes();
      //Get Contacts Details
      if (this.contactId) this.getContactsDetails();
      // if(localStorage.getItem('contacts_details')){
      //   this.contactsDetails = JSON.parse(localStorage.getItem('contacts_details'))
      //   console.log("Contacts Details", this.contactsDetails)
      // }else{
      //   this.getContactsDetails()
      // }
    });
  }

  changePlan() {
    this.router.navigate([
      "/ui/commissions/salesrepconfiguration/changeplan",
      this.contactId
    ]);
  }

  /**
   * Get contact details
   */
  getContactsDetails() {
    this.apiService.get("Contacts/" + this.contactId).subscribe(
      data => {
        if (data["statusCode"] === "201" && data.result && data.result[0]) {
          this.getRulePrompts();
          this.contactsDetails = data.result[0];
          if (data.result[0].data[0] && data.result[0].data[0].planID) {
            if (this.planId == 0 || this.contactPlanId == 0) {
              this.planId = data.result[0].data[0].planID;
              this.contactPlanId = data.result[0].data[0].contactPlanID;
            }
            this.getContactPlan(this.contactPlanId);
            this.getRules();
          }
          // console.log(
          //   "Contacts Details",
          //   this.contactsDetails,
          //   this.planDetails
          // );
        } else {
          this.toastMsg.error("No contacts found.", "Server Error!");
        }
      },
      (err: any) => {
        // console.log(err);
        this.toastMsg.error(err.message, "Server Error!");
      }
    );
  }

  getRuleTypes() {
    this.apiService.get("getdata/GetRuleType").subscribe(
      data => {
        if (data.statusCode === "201" && data.result) {
          data.result.forEach((row: any) => {
            this.basicPlan.push({ parent: row.ruleCd, child: [""] });
          });
          //console.log("rules", data.result)
        }
      },
      (err: any) => {
        this.toastMsg.error(err.message, "Server Error!");
      }
    );
  }

  /**
   * Get plan details
   * NO LONGER USED
   */
  getPlanDetails() {
    this.apiService.get("PlanAssign/" + this.contactId).subscribe(
      data => {
        if (data["statusCode"] === "201" && data.result) {
          this.planDetails = data.result;
          // console.log("Plan Details", this.planDetails);
          this.planId = this.planDetails.planId
          data.result.planDetails.forEach((row: any) => {
            let ruleArray = this.basicPlan.filter(
              item => item.parent == row.ruleTypeName
            );
            if (ruleArray && ruleArray.length > 0) {
              ruleArray[0].child = [];
              row.rules.forEach((row1: any) => {
                if (row1.ruleName)
                  ruleArray[0].child.push({
                    ruleName: row1.ruleName,
                    ruleId: row1.ruleId
                  });
              });
            }
          });
          this.getPaymentBook();
          this.getEmployeeIncentives();
          this.getBasePayStructure();

        } else {
          this.toastMsg.error("No plans found.", "Server Error!");
        }
      },
      (err: any) => {
        // console.log(err);
        this.toastMsg.error(err.message, "Server Error!");
      }
    );
  }

  getRules() {
    this.apiService.get('Plans/RepConfig/' + this.planId + '/ContactPlan/' + this.contactPlanId)
      .subscribe(data => {
        if (data.statusCode === "200" || data.statusCode === "201") {
          this.planDetails = <IPlanDetails>data.result;
          //console.log(this.planDetails)
          this.basicPlan = this.planDetails.planDetails.map(pd => {
            return <IBasicPlan>{
              parent: pd.ruleTypeName,
              child: pd.rules
            }
          });
          // this.toastMsg.success("Plan fetched successfully", 'Success!');
          this.getPaymentBook();
          this.getEmployeeIncentives();
          this.getBasePayStructure();
          // this.getRateIncentiveGoal();
        } else {
          this.toastMsg.error("Server Error", 'Error!')
        }
      }, (err: any) => {
        this.toastMsg.error(err.message, 'Error!')
      });
  }

  getPaymentBook() {
    this.apiService.get('PaymentBook/GetCurrent/' + this.contactId).subscribe(
      data => {
        let arr: IPaymentBook[] = [];
        if (data && data.result) {
          let currentPaymentBook = <IPaymentBook>{
            ruleId: data.result.commissionRuleId,
            ruleName: data.result.commissionRuleName
          };

          arr.push(currentPaymentBook);
        }


        if (this.basicPlan.find(item => item.parent == "Payment Book") != null) {
          this.basicPlan.filter(
            item => item.parent == "Payment Book"
          )[0].child = arr;
        } else {
          this.basicPlan.push({
            parent: "Payment Book",
            child: arr
          });
        }
      },
      (err: any) => {
        this.toastMsg.error(err.message, "Server Error!");
      }
    );
  }

  getEmployeeIncentives() {
    this.apiService
      .get(`EmployeeIncentives/GetCurrent/${this.contactPlanId}`)
      .subscribe(
        data => {
          let arr: IEmployeeIncentive[] = [];
          if (data && data.result) {
            var currentEmployeeIncentives = data.result.map(x => {
              return <IEmployeeIncentive>{
                ruleId: x.commissionRuleId,
                ruleName: x.commissionRuleName
              };
            });

            arr = currentEmployeeIncentives;
          }

          if (this.basicPlan.find(i => i.parent == "Employee Incentive") != null) {
            this.basicPlan.filter(
              i => i.parent == "Employee Incentive"
            )[0].child = arr;
          } else {
            this.basicPlan.push({
              parent: "Employee Incentive",
              child: arr
            })
          }
        },
        (err: any) => {
          this.toastMsg.error(err.message, "Server Error!");
        }
      );
  }

  getRateIncentiveGoal() {
    // this.ruleId = 12;
    // alert(this.ruleId);

    this.apiService.get('EmployeeIncentives/GetCurrentRateIncentiveGoal/' + this.ruleId).subscribe(
      data => {
        let arr: IEmployeeIncentive[] = [];
        if (data && data.result) {
          var currentEmployeeIncentives = data.result.map(x => {
            return <IEmployeeIncentive>{
              ruleId: x.commissionRuleId,
              ruleName: x.commissionRuleName
            };
          });
          arr = currentEmployeeIncentives;
        }
        if (this.basicPlan.find(i => i.parent == "Rate Incentive Goal") != null) {

          this.basicPlan.filter(
            i => i.parent == "Rate Incentive Goal"
          )[0].child = arr;
        } else {
          this.basicPlan.push({
            parent: "Rate Incentive Goal",
            child: arr
          })
        }
      },
      (err: any) => {
        this.toastMsg.error(err.message, "Server Error!");
      }
    );
  }

  getBasePayStructure() {
    this.apiService
      .get(`BasePayStructures/GetCurrent/${this.contactPlanId}`)
      .subscribe(
        data => {
          let arr: IStructureRule[] = [];
          if (data && data.result) {
            data.result.forEach(x => {
              let currentBasePayStructure = <IStructureRule>{
                ruleId: x.commissionRuleId,
                ruleName: x.commissionRuleName,
                basePayRuleId: x.basePayRuleId,
                basePayRuleName: x.basePayRuleName
              };
              arr.push(currentBasePayStructure);
            })
          }
          if (this.basicPlan.find(i => i.parent == "Base Pay Structure") != null) {
            this.basicPlan.filter(
              i => i.parent == "Base Pay Structure"
            )[0].child = arr;
          } else {
            this.basicPlan.push({
              parent: "Base Pay Structure",
              child: arr
            })
          }
        },
        (err: any) => {
          this.toastMsg.error(err.message, "Server Error!");
        }
      );
  }

  onChildClick(parent: any, child: any) {
    if (parent && parent.parent) {
      if (parent.parent === "Contact Plan Inclusions")
        this.inclusionRuleSelected = true;
      else if (parent.parent === "Base Pay"){
        this.inclusionRuleSelected = false;
        var inclusion = this.planDetails.planDetails.find(x => x.ruleTypeName === "Contact Plan Inclusions");
        if(inclusion && inclusion.rules && inclusion.rules.length > 0) {
          if (inclusion.rules.find(x => x.ruleId == child.ruleId))
            this.exclusionWithInclusion = !inclusion.rules.find(x => x.ruleId == child.ruleId) ? false : true;
        }
      }
      else {
        this.inclusionRuleSelected = false;
        this.exclusionWithInclusion = false;
      }
    }
    if (child && child.ruleId > 0) {
      this.showPreview = true;
      this.ruleType = parent.parent;
      this.ruleId = child.ruleId;
      this.noRule = false;
      if (parent.parent === "Contact Plan Inclusions")
        this.inclusionRuleSelected = true;
      else
        this.inclusionRuleSelected = false;
      if (parent.parent === "Base Pay Structure")
        this.basePayRuleId = child.basePayRuleId;
    } else {
      this.showPreview = false;
      this.noRule = true;
      this.ruleName = child.ruleName;
    }
    // if (this.ruleType = "Rate Incentive Goal")
    //   {
    //     this.getRateIncentiveGoal();
    //   }
  }

  onUpdated(updated: boolean) {
    this.getPlanDetails();
  }

  getContactPlan(contactPlanId: number) {
    this.apiService.get(`ContactPlan/${contactPlanId}`)
      .subscribe((data) => {
        if (data && data.result) {
          var contactPlan = <IContactPlan>data.result;
          this.contactPlan = contactPlan;
        }
      }, (err) => {
        this.toastMsg.error(err.message, "Error!");
      })
  }

  addEmployeeIncentive() {
    var employeeIncentiveRules: IPlanDetail[];
    this.apiService.get('GetData/EmployeeIncentiveRules')
      .subscribe(data => {
        if (data && data.result) {
          // console.log(data);
          employeeIncentiveRules = data.result.map(x => { return <IPlanDetail>x });
          var existingRules = this.addOns.map(x => { return x.ruleId });
          employeeIncentiveRules[0].rules = employeeIncentiveRules[0].rules.filter(x => !existingRules.includes(x.ruleId));

          const dialogRef = this.dialog.open(EmployeeIncentiveDialogComponent, {
            width: '80%',

            data: {
              employeeIncentiveRules: employeeIncentiveRules,
              prompts: this.metadataPrompts
            }
          });

          dialogRef.afterClosed().subscribe(result => {
            // console.log(result);
            if (result) {
              this.addOns = [];
              this.promptsValues = [];
              this.addOns.push(result);
              this.addOns = this.addOns.slice();
              var ruleId = 0;
              this.addOns.forEach(addOn => {
                var addOnPromptsValues = addOn.promptValues;

                ruleId = addOn.ruleId;
                if (addOnPromptsValues[addOn.ruleId] == null)
                  addOnPromptsValues[addOn.ruleId] = {
                    commissionRuleId: addOn.ruleId,
                    ruleTypeName: addOn.ruleTypeName
                  };
                if (addOnPromptsValues[addOn.ruleId].commissionRuleId == null) {
                  addOnPromptsValues[addOn.ruleId].commissionRuleId = addOn.ruleId;
                  addOnPromptsValues[addOn.ruleId].ruleTypeName = addOn.ruleTypeName;
                }
                if (addOnPromptsValues[addOn.ruleId].ruleItems == null)
                  addOnPromptsValues[addOn.ruleId].ruleItems = [];

                if (addOnPromptsValues[addOn.ruleId] != null)
                  addOnPromptsValues[addOn.ruleId].ruleTypeName = addOn.ruleTypeName;

                this.promptsValues[addOn.ruleId] = addOnPromptsValues[addOn.ruleId];
              });

              let formData = {
                ContactId: this.contactId,
                ContactPlanId: this.contactPlanId,
                PlanId: this.planId,
                PlanRules: Object.values(this.promptsValues)
              }

              var datesValid = true;
              var eiInputsValid = true;

              Object.values(this.promptsValues).forEach((x: IRulePrompt) => {
                x.ruleItems.forEach((y: IRuleItem) => {
                  // Validate Dates
                  let startDateInputs = y.ruleItemInputs.filter(input => input.columnName == 'Start_Date');
                  let endDateInputs = y.ruleItemInputs.filter(input => input.columnName == 'End_Date');

                  if (startDateInputs.length > 0 && endDateInputs.length > 0) {
                    let startDate = new Date(startDateInputs[0].columnValue);
                    let endDate = new Date(endDateInputs[0].columnValue);
                    if (startDate > endDate) datesValid = false;
                  }
                  if (y.ruleItemInputs.find(x => x.columnName != "Start_Date" && x.columnName != "End_Date" && ((x.columnValue == null || x.columnValue == "") && x.columnValue !== 0)) != null) {
                    eiInputsValid = false;
                  }
                });
              });
              if (!datesValid) {
                this.toastMsg.warning("Start Dates cannot be greater than End Dates");
                return false;
              } else {
                this.apiService.post('employeeIncentives/add', formData)
                  .subscribe(data => {
                    if (data["statusCode"] === "201" && data.result) {
                      this.toastMsg.success("Employee incentive added successfully.", 'Success!')
                      this.router.navigate(['/ui/commissions/salesrepconfiguration', this.contactId]);
                    } else {
                      this.toastMsg.error("Employee incentive already exists.", 'Server Error!')
                    }
                  }, (err: any) => {
                    this.toastMsg.error(err.message, 'Server Error!')
                  });
              }
            }
          });
        }
      }, err => {
        this.toastMsg.error(err.message, "Error");
      });
  }

  addPlanInclusion(){
    this.apiService.get(`ContactPlanInclusion/${this.planId}`)
    .subscribe(data => {
      //console.log(data)
      if (data && data.result) {
        this.planInclusionRules = data.result;
        var inclusion = this.planDetails.planDetails.find(x => x.ruleTypeName === "Contact Plan Inclusions");
        var existing = this.planDetails.planDetails.filter(x => x.ruleTypeName !== "Contact Plan Inclusions");
        if (inclusion && inclusion.rules.length > 0) {
            var inclusionRules = inclusion.rules.map(x => x.ruleId);
            existing.forEach(x => {
              x.rules.forEach(r => {
                if (x.ruleTypeName === "Base Pay") {
                  this.planInclusionRules.basePay.splice(this.planInclusionRules.basePay.findIndex(i => i.commissionRuleId == r.ruleId && !inclusionRules.includes(r.ruleId)), 1)
                }
                else if (x.ruleTypeName === "Bonus") {
                  this.planInclusionRules.bonuses.splice(this.planInclusionRules.bonuses.findIndex(i => i.commissionRuleId == r.ruleId && !inclusionRules.includes(r.ruleId)), 1)
                }
                else if (x.ruleTypeName === "Bonus Incentive Goal") {
                  this.planInclusionRules.bonusIncentiveGoals.splice(this.planInclusionRules.bonusIncentiveGoals.findIndex(i => i.commissionRuleTypeId == r.ruleTypeId && !inclusionRules.includes(r.ruleId)), 1)
                }
                else if (x.ruleTypeName === "Rate Incentive Goal") {
                  this.planInclusionRules.rateIncentiveGoals.splice(this.planInclusionRules.rateIncentiveGoals.findIndex(i => i.commissionRuleTypeId == r.ruleTypeId && !inclusionRules.includes(r.ruleId)), 1)
                }
              })
            });
        }
        else {
          existing.forEach(x => {
            x.rules.forEach(r => {
              if (x.ruleTypeName === "Base Pay") {
                this.planInclusionRules.basePay.splice(this.planInclusionRules.basePay.findIndex(i => i.commissionRuleId == r.ruleId), 1)
              }
              else if (x.ruleTypeName === "Bonus") {
                this.planInclusionRules.bonuses.splice(this.planInclusionRules.bonuses.findIndex(i => i.commissionRuleId == r.ruleId), 1)
              }
              else if (x.ruleTypeName === "Bonus Incentive Goal") {
                this.planInclusionRules.bonusIncentiveGoals.splice(this.planInclusionRules.bonusIncentiveGoals.findIndex(i => i.commissionRuleTypeId == r.ruleTypeId), 1)
              }
              else if (x.ruleTypeName === "Rate Incentive Goal") {
                this.planInclusionRules.rateIncentiveGoals.splice(this.planInclusionRules.rateIncentiveGoals.findIndex(i => i.commissionRuleTypeId == r.ruleTypeId), 1)
              }
            })
          });
        }

        const inclusionRef = this.dialog.open(AddPlanInclusionDialogComponent, {
          width: '80%',
          data:{
            planInclusionRules: this.planInclusionRules,
            component: "repConfig",
            planId: this.planId
          }
        })

        inclusionRef.afterClosed().subscribe(result => { 
          if(result) {
              result.contactPlanId = this.contactPlanId;
              this.apiService.post('ContactPlanInclusion', result).subscribe(data => {
                  console.log(data)
                  if (data["statusCode"] === "201" && data.result == true) {
                      this.toastMsg.success("Plan Inclusion added successfully.", 'Success!')
                      this.ngOnInit();
                      //this.router.navigate(['/ui/commissions/salesrepconfiguration', this.contactId]);
                  }
                  else {
                      console.log(data.message)
                      this.toastMsg.error(data.message)
                  }
              }, (err: any) => {
                console.log(err)
                this.toastMsg.error(err || err.message)
              })
          }
        })

      }
    } , (err: any) => {
      console.log(err)
    });
  }

  getRulePrompts(): void {
    if (!this.planId) {
      return;
    }

    this.apiService.get(`Rule/Prompts/${this.planId}`)
      .subscribe(data => {
        if (data && data.statusCode == "201") {
          this.metadataPrompts = data.result.map(prompt => {
            return <IPrompt>{
              ruleId: prompt.ruleId,
              schema: prompt.schema,
              tableName: prompt.tableName,
              columnName: prompt.columnName,
              displayName: prompt.displayName
            }
          }).map(iPrompt => {
            return <Prompt>{ ...iPrompt }
          });

          // console.log("Metadata Prompts", this.metadataPrompts);
        }
      }, (err: any) => {
        // this.toastMsg.error("Server Error", "Error");
        return;
      });
  }

}


export interface IStructureRule {
  ruleId: number;
  ruleName: string;
  basePayRuleId: number;
  basePayRuleName: string;
}