import { Component, OnInit, ViewChild, ViewChildren, QueryList } from '@angular/core';
import { Router, ActivatedRoute } from "@angular/router";
import { ApiService } from "../../services/api.service";
import { ToastrService } from "ngx-toastr";
declare var $: any;
import { DatePipe, DecimalPipe } from "@angular/common";
import { ISalesRepPayment } from 'src/app/model/sales-rep-payment.model';
import { ISalesRepProcessedPayment } from 'src/app/model/sales-rep-processed-payment.model';
import { MatPaginator } from '@angular/material/paginator';
import { MatSort } from '@angular/material/sort';
import { MatTableDataSource } from '@angular/material/table';
import { IContactPaymentBook, IMyOpportinity,IMyOppoEnabledColumns, IPaymentBookTransaction } from 'src/app/model/pay-book.model';
import { MatDialog } from  '@angular/material/dialog';
import { SalesRepOverrideDetailsPopupComponent } from '../sales-rep-override-details-popup/sales-rep-override-details-popup.component';
import { SalesRepOutreachOverrideDetailsPopupComponent } from '../sales-rep-outreach-override-details-popup/sales-rep-outreach-override-details-popup.component';
import { HttpClient } from '@angular/common/http';
import { IPaymentHistory } from 'src/app/model/payment-history.model';
import { PaymentHistoryDialogComponent } from '../commission-view/payments/payment-history-dialog/payment-history-dialog.component';
import { SalesRepProcessedPaymentPopupComponent } from '../sales-rep-processed-payment-popup/sales-rep-processed-payment-popup.component';
import { IOverrideDetails } from '../../model/IOverrideDetails';
import { IOutreachOverrideDetails } from '../../model/IOutreachOverrideDetails';

@Component({
  selector: 'app-salesrep-dashboard',
  templateUrl: './salesrep-dashboard.component.html',
  styleUrls: ['./salesrep-dashboard.component.css']
})
export class SalesrepDashboardComponent implements OnInit {
  @ViewChild(MatSort) set content(sort: MatSort) {
    this.transactions.sort = sort;
  }   
  @ViewChild(MatPaginator) set content2(paginator: MatPaginator) {
    this.transactions.paginator = paginator;
  }

  contactPaymentBook: IContactPaymentBook = <IContactPaymentBook>{};
  @ViewChildren(MatPaginator) paginator = new QueryList<MatPaginator>();
  @ViewChildren(MatSort) sort = new QueryList<MatSort>();

  processedPaymentColumns: string[] = ["paymentFor", "paymentType", "dateProcessed", "transactionType", "systemSize", "cppw", "amount", "actualInstallDate"];
  columns: string[] = ["paymentFor", "paymentType", "dateProcessed", "transactionType", "systemSize", "amount", "actualInstallDate"];
  pendingPaymentsColumn:string[]= ["paymentFor", "paymentType", "dateProcessed", "transactionType", "systemSize", "amount", "actualInstallDate","roofSquares","roofInstallDate","dealType"];
  pageSizeOptions: number[] = [5,10,25,50,100];

  contactId: number;
  contactsDetails: any;
  OpportunityName: string = "";
  FromDate: string;
  ToDate: string;
  ProcessedPayments: MatTableDataSource<ISalesRepProcessedPayment> = new MatTableDataSource([]);
  recentProcessedPayments: MatTableDataSource<ISalesRepProcessedPayment> = new MatTableDataSource([]);
  PendingPayments: MatTableDataSource<ISalesRepPayment> = new MatTableDataSource([]);
  PendingReclaims: MatTableDataSource<ISalesRepPayment> = new MatTableDataSource([]);
  ApprovedOnHoldPayments: MatTableDataSource<ISalesRepPayment> = new MatTableDataSource([]);
  ProcessedPaymentsTotal: number = 0
  PendingPaymentsTotal: number = 0
  ApprovedOnHoldPaymentsTotal: number = 0
  PendingReclaimsTotal: number = 0
  IsShowBreadcrumbs: boolean = true;
  showProcessedPaymentsLink = false;
  basedOn: string = "Modified Date";
  dateRange: string = "Custom Range";


  colTransations: string[] = ["paymentTransactionId", "dateProcessed", "opportunityName", "commissionTransactionTypeName", "paymentTypeName", "credit", "debit"];
  transactions: MatTableDataSource<IPaymentBookTransaction> = new MatTableDataSource();
  transactionsForCurrentBal: MatTableDataSource<IPaymentBookTransaction> = new MatTableDataSource();

  isTrinitySalesPersonOpp: boolean = false;
  isLeadGeneratorOpp: boolean = false;
  isSdrInsideSalesOpp: boolean = false;
  isSalesSuccessRepresentativeOpp: boolean = false;
  isAccountExecutiveOpp: boolean = false;
  isBatterySalesPersonOpp: boolean = false;
  isRoofingSalesPersonOpp: boolean = false;
  
  trinitySalesPersonOpportunities: IMyOpportinity[] = [];
  leadGeneratorOpportunities: IMyOpportinity[] = [];
  sdrInsideSalesOpportunities: IMyOpportinity[] = [];
  salesSuccessRepresentativeOpportunities: IMyOpportinity[] = [];
  accountExecutiveOpportunities: IMyOpportinity[] = [];
  batterySalespersonOpportunities: IMyOpportinity[] = [];
  recruiterIdOpportunities: IMyOpportinity[] = [];
  roofingSalespersonOpportunities: IMyOpportinity[] = [];
  MyOppoEnabledColumns: IMyOppoEnabledColumns[] = [];
  overridesAvailable: string = "";
  overrides: Array<IOverrideDetails> = [];
  outreachOverrides: Array<IOutreachOverrideDetails> = [];
  originalPendingPayments: any;
  originalApprovedOnHoldPayments: any;
  selectedDealerType:string='All';
  selectedApprovedOnHoldType:string='All';

  constructor(
    private router: Router,
    public apiService: ApiService,
    private toastMsg: ToastrService,
    datePipe: DatePipe,
    private activatedRoute: ActivatedRoute,
    private  dialogRef : MatDialog,
    private http: HttpClient, 
  ) { }

  ngOnInit() {
    this.activatedRoute.params.subscribe(params => {
      this.contactId = params.contactId;
      this.IsShowBreadcrumbs = true;
      var role = this.apiService.getRole();
      if (this.apiService.checkPermission('ViewSalesRepDashboard') || role == 'Sales Reps' || role == 'Sales Manager - Direct' || role == 'Sales Manager - Outreach' || role == 'Sales Manager - Traditional') {
        if (this.apiService.getRole() == 'Sales Reps')
        {
          this.IsShowBreadcrumbs = false;          
        }
        else
        {
          this.IsShowBreadcrumbs = true;          
        }
        if (this.apiService.getRole() == 'Sales Reps' && params.contactId != localStorage.getItem("cid"))
        {
          this.apiService.goBack();
          this.toastMsg.error("Insufficient Permissions. Please make sure your account can access this information.", "Permissions");          
        }
        else
        {
          this.overridesAvailable = "";
          this.getPaymentBookTransactionsForCurrentBal();
          this.getContactsDetails();
          this.getContactPaymentBook();
          this.getPaymentBookTransactions();
          this.getSalesRepDashBoard();
          this.getMyOpportunities();
          this.GetMyOppoTabs(); 
          this.getOverrideDetails(this.contactId);
          this.getOutreachOverrideDetails(this.contactId);
        }
      } else {
        this.router.navigate(['/'])
      }
      this.selectedDealerType = 'All';
      this.selectedApprovedOnHoldType = 'All';
    });    
  }

  onChangeDealType(event:any){
    let filterData =[];
    filterData = this.selectedDealerType == 'All'? this.originalPendingPayments : this.originalPendingPayments.filter(s=>s.dealType === this.selectedDealerType);
    let totalAmount = 0;    
     this.PendingPayments = new MatTableDataSource(filterData);
     this.PendingPayments.paginator = this.paginator.toArray()[2];
     this.PendingPayments.sort = this.sort.toArray()[2];
     filterData.forEach(item => {      
      if(item.amount){
        totalAmount += item.amount;
      this.PendingPaymentsTotal = totalAmount;
      }       
    });
    if(filterData.length < 1){
      this.PendingPaymentsTotal = 0;      
    }
  }

  onChangeApprovedOnHoldDealType(){
    let filterData =[];
    filterData = this.selectedApprovedOnHoldType == 'All'? this.originalApprovedOnHoldPayments : this.originalApprovedOnHoldPayments.filter(s=>s.dealType === this.selectedApprovedOnHoldType);
    let totalAmount = 0;    
     this.ApprovedOnHoldPayments = new MatTableDataSource(filterData);
     this.ApprovedOnHoldPayments.paginator = this.paginator.toArray()[3];
     this.ApprovedOnHoldPayments.sort = this.sort.toArray()[3];
     filterData.forEach(item => {      
      if(item.amount){
        totalAmount += item.amount;
      this.ApprovedOnHoldPaymentsTotal = totalAmount;
      }       
    });
    if(filterData.length < 1){
      this.ApprovedOnHoldPaymentsTotal = 0;      
    }
  }

  search() {
    this.getPaymentBookTransactions();
    this.getSalesRepDashBoard();
    this.getMyOpportunities();
  }

   getPaymentBookTransactions() {
    if (this.contactId) {

      let url = "PaymentBookTransactions/SalesRepPaymentBookTransactions/" + this.contactId;
      if (this.OpportunityName || this.FromDate || this.ToDate || this.basedOn) {
        url += "?";
        if (this.OpportunityName) {
          url += `OpportunityName=${this.OpportunityName.trim()}&`;
        } 

        if (this.FromDate) {
          url += `FromDate=${this.FromDate}&`;
        } 
        if (this.ToDate) {
          url += `ToDate=${this.ToDate}&`;
        }
       
        if (this.basedOn) {
          url += `BasedOn=${this.basedOn}&`;
        }
  
        if (url[url.length - 1] == "&") {
          url = url.substring(0, url.length - 1);
        }
      }

      this.transactions = new MatTableDataSource([]);

      this.apiService.get(url)
        .subscribe(data => {
          if (data && data.result) {
            var trans = data.result.map((trans: IPaymentBookTransaction) => {
              return trans;
            });

          this.transactions = new MatTableDataSource(trans);
          this.transactions.paginator = this.paginator.toArray()[0];
          this.transactions.sort = this.sort.toArray()[0];
          }
        }, err => {
          this.toastMsg.error(err.message, "Error!");
        })
    }
   }
  
  getPaymentBookTransactionsForCurrentBal() {
    if (this.contactId) {
      this.apiService.get(`PaymentBookTransactions/${this.contactId}`)
        .subscribe(data => {
          if (data && data.result) {
            var trans = data.result.map((trans: IPaymentBookTransaction) => {
              return trans;
            });

            this.transactionsForCurrentBal = new MatTableDataSource(trans);
          }
        }, err => {
          this.toastMsg.error(err.message, "Error!");
        })
    }
  }

  getOverrideDetails(contactId: number) {
    this.apiService.get("Contacts/Overrides/" + contactId)
    .subscribe((data) => {
      this.overrides = data.result;
    }, (err: any) => {
      this.toastMsg.error(err.message, "Error!");
    });
  }


getOutreachOverrideDetails(contactId: number) {
  this.apiService.get("Contacts/OutreachOverrides/" + contactId)
  .subscribe(data => {
    this.outreachOverrides = data.result;
  }, (err: any) => {
    this.toastMsg.error(err.message, 'Error!')
  });
}

  GetMyOppoTabs() {
    this.apiService.get(`Dashboard/GetMyOppoTabs`)
        .subscribe(data => {
          if (data && data.result) {
            this.MyOppoEnabledColumns =data.result;
            var opps = data.result.map((opps: IMyOppoEnabledColumns) => {
              return opps;
            });

            for (let i: number = 0; i < opps.length; i++) {
              var ColumnName = opps[i].columnName;

              if (ColumnName == "Trinity_Salesperson_Id") {
                this.isTrinitySalesPersonOpp = true;
              }
              if (ColumnName == "Lead_Generator_Id") {
                this.isLeadGeneratorOpp = true;
              }
              if (ColumnName == "SDR_Inside_Sales_Id") {
                this.isSdrInsideSalesOpp = true;
              }
              if (ColumnName == "Account_Executive_Id") {
                this.isAccountExecutiveOpp = true;
              }
              if (ColumnName == "Sales_Success_Representative_Id") {
                this.isSalesSuccessRepresentativeOpp = true;
              }
              if (ColumnName == "Battery_Salesperson_Id") {
                this.isBatterySalesPersonOpp = true;
              }
              if (ColumnName == "Roofing_Salesperson_Id") {
                this.isRoofingSalesPersonOpp = true;
              }
            }
          }
        }, err => {
          this.toastMsg.error(err.message, "Error!");
        })
  }

  getMyOpportunities() {
    this.apiService.get(`Dashboard/GetMyOpportunities/${this.contactId}`)
        .subscribe(data => {
          if (data && data.result) {
            var opps = data.result.map((opps: IMyOpportinity) => {
              return <IMyOpportinity> opps;
            });
            var fd = this.FromDate;
            if (this.FromDate == "" || this.FromDate == undefined) {
              fd = "1900-01-01";
            }
            var td = this.ToDate;
            if (this.ToDate == "" || this.ToDate == undefined) {
              td = "2100-01-01";
            }
            if (this.OpportunityName == undefined) {
             this.OpportunityName = "";
            }
            if (this.basedOn == "Demo Date") {
              this.trinitySalesPersonOpportunities = opps.filter(rec=>rec.trinitySalespersonId == this.contactId && new Date(rec.demoDate) > new Date(fd) && new Date(rec.demoDate) <= new Date(td) && rec.opportunityName.indexOf(this.OpportunityName) > -1); 
              this.leadGeneratorOpportunities = opps.filter(rec=> rec.leadGeneratorId == this.contactId && new Date(rec.demoDate) > new Date(fd) && new Date(rec.demoDate) <= new Date(td) && rec.opportunityName.indexOf(this.OpportunityName) > -1); 
              this.sdrInsideSalesOpportunities = opps.filter(rec=> rec.sdrInsideSalesId == this.contactId && new Date(rec.demoDate) > new Date(fd) && new Date(rec.demoDate) <= new Date(td) && rec.opportunityName.indexOf(this.OpportunityName) > -1); 
            } else if (this.basedOn == "Actual Install Date") {
              this.trinitySalesPersonOpportunities = opps.filter(rec=>rec.trinitySalespersonId == this.contactId && new Date(rec.actualInstallDate) > new Date(fd) && new Date(rec.actualInstallDate) <= new Date(td) && rec.opportunityName.indexOf(this.OpportunityName) > -1); 
              this.leadGeneratorOpportunities = opps.filter(rec=> rec.leadGeneratorId == this.contactId && new Date(rec.actualInstallDate) > new Date(fd) && new Date(rec.actualInstallDate) <= new Date(td) && rec.opportunityName.indexOf(this.OpportunityName) > -1); 
              this.sdrInsideSalesOpportunities = opps.filter(rec=> rec.sdrInsideSalesId == this.contactId && new Date(rec.actualInstallDate) > new Date(fd) && new Date(rec.actualInstallDate) <= new Date(td) && rec.opportunityName.indexOf(this.OpportunityName) > -1); 
            } else if (this.basedOn == "Contract Signed Date") {
              this.trinitySalesPersonOpportunities = opps.filter(rec=>rec.trinitySalespersonId == this.contactId && new Date(rec.dateContractSigned) > new Date(fd) && new Date(rec.dateContractSigned) <= new Date(td) && rec.opportunityName.indexOf(this.OpportunityName) > -1); 
              this.leadGeneratorOpportunities = opps.filter(rec=> rec.leadGeneratorId == this.contactId && new Date(rec.dateContractSigned) > new Date(fd) && new Date(rec.dateContractSigned) <= new Date(td) && rec.opportunityName.indexOf(this.OpportunityName) > -1);  
              this.sdrInsideSalesOpportunities = opps.filter(rec=> rec.sdrInsideSalesId == this.contactId && new Date(rec.dateContractSigned) > new Date(fd) && new Date(rec.dateContractSigned) <= new Date(td) && rec.opportunityName.indexOf(this.OpportunityName) > -1);  
            } else {
              this.trinitySalesPersonOpportunities = opps.filter(rec=>rec.trinitySalespersonId == this.contactId && rec.opportunityName.indexOf(this.OpportunityName) > -1);
              this.leadGeneratorOpportunities = opps.filter(rec=> rec.leadGeneratorId == this.contactId && rec.opportunityName.indexOf(this.OpportunityName) > -1);
              this.sdrInsideSalesOpportunities = opps.filter(rec=> rec.sdrInsideSalesId == this.contactId && rec.opportunityName.indexOf(this.OpportunityName) > -1);
            }
            this.sdrInsideSalesOpportunities = opps.filter(rec=> rec.sdrInsideSalesId == this.contactId);
            this.salesSuccessRepresentativeOpportunities = opps.filter(rec=>rec.salesSuccessRepresentativeId == this.contactId);
            this.accountExecutiveOpportunities = opps.filter(rec=>rec.accountExecutiveId == this.contactId);
            this.batterySalespersonOpportunities = opps.filter(rec=>rec.batterySalespersonId == this.contactId);
            this.roofingSalespersonOpportunities = opps.filter(rec=>rec.roofingSalespersonId == this.contactId);
          }
        }, err => {
          this.toastMsg.error(err.message, "Error!");
        })
  }
  
  getContactPaymentBook() {
    if (this.contactId) {
      this.apiService.get(`PaymentBook/GetContactPaymentBook/${this.contactId}`)
        .subscribe(data => {
          if (data && data.result) {
            this.contactPaymentBook = <IContactPaymentBook>data.result;
          }
        }, err => {
          this.toastMsg.error(err.message, "Error!");
        })
    }
  }
  getCurrentBalance() {
    if (this.transactionsForCurrentBal && this.transactionsForCurrentBal.data.length > 0) {
      var credits = this.transactionsForCurrentBal.data.filter(x => x.debitCredit == "C").map(x => { return x.amount });
      if (credits && credits.length > 0) {
        var totalCredit = credits.reduce((a, b) => a + b);
      } else {
        var totalCredit = 0;
      }

      var debits = this.transactionsForCurrentBal.data.filter(x => x.debitCredit == "D").map(x => { return x.amount })
      if (debits && debits.length > 0) {
        var totalDebit = debits.reduce((a, b) => a + b);
      } else {
        var totalDebit = 0;
      }

      return totalCredit - totalDebit;
    }
  }
  
  /**
   * Get contact details
   */
  getContactsDetails() {
    this.apiService.get("Contacts/" + this.contactId).subscribe(
      data => {
        // console.log("Contact Details", data)
        if (data["statusCode"] === "201" && data.result && data.result[0]) {
          this.contactsDetails = data.result[0];
        } else {
          this.toastMsg.error("No contacts found.", "Server Error!");
        }
      },
      (err: any) => {
        // console.log(err);
        this.toastMsg.error(err.message, "Server Error!");
      }
    );
  }
 
  /**
   * Get Sales Rep Dashboard
   */
  getSalesRepDashBoard() {
    let url = "GetData/SalesRepDashBoard/" + this.contactId;
    if (this.OpportunityName || this.FromDate || this.ToDate) {
      url += "?";
      if (this.OpportunityName) {
        url += `OpportunityName=${this.OpportunityName.trim()}&`;
      } 
      if (this.FromDate) {
        url += `FromDate=${this.FromDate}&`;
      } 
      if (this.ToDate) {
        url += `ToDate=${this.ToDate}&`;
      }
      if (this.basedOn) {
        url += `BasedOn=${this.basedOn}&`;
      }

      if (url[url.length - 1] == "&") {
        url = url.substring(0, url.length - 1);
      }
    }

    //Reset Data
    this.PendingPayments = new MatTableDataSource([]);
    this.PendingPaymentsTotal = 0;
    this.ProcessedPayments = new MatTableDataSource([]);
    this.ProcessedPaymentsTotal = 0;
    this.ApprovedOnHoldPayments = new MatTableDataSource([]);
    this.ApprovedOnHoldPaymentsTotal = 0;
    this.PendingReclaims = new MatTableDataSource([]);
    this.PendingReclaimsTotal = 0;
    this.transactions = new MatTableDataSource([]);
    this.apiService.get(url).subscribe(
      data => {
        // console.log("Sales Rep Dashboard Details", data)
        if (data["statusCode"] === "201" && data.result && data.result.salesRepDashBoardProcessedPaymentDetails) {
          this.showProcessedPaymentsLink = true;
          if (data.result.salesRepDashBoardProcessedPaymentDetails.length > 0){
            let paymentDetails = data.result.salesRepDashBoardProcessedPaymentDetails;
            if (paymentDetails && paymentDetails.length > 0) {
              paymentDetails = paymentDetails.map(x => { return <ISalesRepPayment>x });
              this.ProcessedPayments = new MatTableDataSource(paymentDetails.filter(paymentObj => paymentObj.paymentStatus == "Approved"));
              this.ProcessedPayments.paginator = this.paginator.toArray()[1];
              this.ProcessedPayments.sort = this.sort.toArray()[1];     
              this.ProcessedPayments.sort.sort({ id: 'dateProcessed', start: 'desc', disableClear: false });
              this.ProcessedPaymentsTotal = data.result.totalAmountProcessed;

              let paymentDtls = paymentDetails.filter(paymentObj => paymentObj.paymentType != "Reclaim" && (paymentObj.paymentStatus == "Created" || paymentObj.paymentStatus == "On Hold"))
              this.originalPendingPayments =paymentDtls;
              this.PendingPayments = new MatTableDataSource(paymentDtls);              
              this.PendingPayments.paginator = this.paginator.toArray()[2];
              this.PendingPayments.sort = this.sort.toArray()[2];
              this.PendingPayments.sort.sort({ id: 'dateProcessed', start: 'desc', disableClear: false });
              this.PendingPaymentsTotal = data.result.totalAmountPending

              let approvedOnHoldDtls = paymentDetails.filter(paymentObj => paymentObj.paymentType != "Reclaim" && paymentObj.paymentStatus == "Approved-On Hold");
              this.originalApprovedOnHoldPayments = approvedOnHoldDtls;
              this.ApprovedOnHoldPayments = new MatTableDataSource(approvedOnHoldDtls);
              this.ApprovedOnHoldPayments.paginator =  this.paginator.toArray()[3];
              this.ApprovedOnHoldPayments.sort = this.sort.toArray()[3];
              this.ApprovedOnHoldPayments.sort.sort({ id: 'dateProcessed', start: 'desc', disableClear: false });
              this.ApprovedOnHoldPaymentsTotal = data.result.totalAmountApprovedOnHold

              this.PendingReclaims = new MatTableDataSource(paymentDetails.filter(paymentObj => paymentObj.paymentType == "Reclaim" && (paymentObj.paymentStatus == "Created" || paymentObj.paymentStatus == "On Hold")));
              this.PendingReclaims.paginator =  this.paginator.toArray()[4];
              this.PendingReclaims.sort = this.sort.toArray()[4];
              this.PendingReclaims.sort.sort({ id: 'dateProcessed', start: 'desc', disableClear: false });
              this.PendingReclaimsTotal = data.result.totalAmountPendingReclaims

            } 
          }else {
            this.toastMsg.warning(`The commission for ${this.OpportunityName} could not be found within the provided dates.`, "No Commission Found");
          }
        } else {
          this.toastMsg.error(data.result, "Warning!");
        }
      },
      (err: any) => {
        this.toastMsg.error(err.message, "Warning!");
      }
    );
  }

  openDialog(){
    this.dialogRef.open(SalesRepOverrideDetailsPopupComponent, {
      data:{
        overrides: this.overrides
      }
    });
  }

  openOutreachDialog(){
    this.dialogRef.open(SalesRepOutreachOverrideDetailsPopupComponent, {
      data:{
        outreachOverrides: this.outreachOverrides
      }
    });
  }

  getProcessedPayments(dateProcessed: string) {
    let endDate = new Date(dateProcessed);
    let startDate = new Date(endDate);
    startDate.setDate(endDate.getDate() - 7);
    this.recentProcessedPayments = new MatTableDataSource(this.ProcessedPayments.filteredData.filter(m => new Date(m.dateProcessed) > startDate && new Date(m.dateProcessed) <= endDate));
    const dialogRef = this.dialogRef.open(SalesRepProcessedPaymentPopupComponent, {
      data: {ProcessedPayments: this.recentProcessedPayments,
        withdrawalDate: startDate}
    });

    dialogRef.afterClosed().subscribe(result => {
      console.log('The dialog was closed');
    });
  }

  resetData() {
    this.OpportunityName = ""
    this.FromDate = ""
    this.ToDate = ""
    this.getPaymentBookTransactions();
    this.getSalesRepDashBoard();
  }

  onChangeBasedOn(ev: any) {
     this.basedOn = ev.target.options[ev.target.selectedIndex].text;
  }
  onChangeDateRange(ev: any) {
    this.dateRange = ev.target.options[ev.target.selectedIndex].text;

    if (this.dateRange == "This Week") {
      let lastMonday = this.firstDayOfWeek(new Date(), 1)
      this.FromDate = this.formatDate(lastMonday);
      this.ToDate = this.formatDate(new Date());
    }
    if (this.dateRange == "Last Week") {
      let lastWeekMondayDate = this.firstDayOfWeek(new Date(), -7)
      let lastWeekMonday = this.firstDayOfWeek(lastWeekMondayDate, 1)
      this.FromDate = this.formatDate(lastWeekMonday);
      this.ToDate =  this.formatDate(this.addDays(lastWeekMondayDate, 7));
    }
    if (this.dateRange == "This Month") {
      let firstDayOfMonth = this.startOfMonth(new Date());
      this.FromDate = this.formatDate(firstDayOfMonth);
      this.ToDate = this.formatDate(new Date());
    }
    if (this.dateRange == "Last Month") {
      let firstDayOfLastMonth = this.startOfLastMonth(new Date());
      this.FromDate = this.formatDate(firstDayOfLastMonth);
      let firstDayOfMonth = this.startOfMonth(new Date());
      let lastDayOfMonth = this.addDays(firstDayOfMonth, -1);
      this.ToDate = this.formatDate(lastDayOfMonth);
    }
    if (this.dateRange == "This Year") {
      let firstDayOfYear = this.startOfYear(new Date());
      this.FromDate = this.formatDate(firstDayOfYear);
      this.ToDate = this.formatDate(new Date());
    }
}


 formatDate(date) {
  var d = new Date(date),
      month = '' + (d.getMonth() + 1),
      day = '' + d.getDate(),
      year = d.getFullYear();

  if (month.length < 2) 
      month = '0' + month;
  if (day.length < 2) 
      day = '0' + day;

  return [year, month, day].join('-');
}

firstDayOfWeek(dateObject, firstDayOfWeekIndex) {
  const dayOfWeek = dateObject.getDay(),
      firstDayOfWeek = new Date(dateObject),
      diff = dayOfWeek >= firstDayOfWeekIndex ?
          dayOfWeek - firstDayOfWeekIndex :
          6 - dayOfWeek
  firstDayOfWeek.setDate(dateObject.getDate() - diff)
  firstDayOfWeek.setHours(0,0,0,0)
  return firstDayOfWeek
}

firstDayOfLastWeek(dateObject, firstDayOfWeekIndex) {
const dayOfWeek = dateObject.getDay(),
    firstDayOfWeek = new Date(dateObject),
    diff = dayOfWeek >= firstDayOfWeekIndex ?
        dayOfWeek - firstDayOfWeekIndex :
        6 - dayOfWeek
firstDayOfWeek.setDate(dateObject.getDate() - diff)
firstDayOfWeek.setHours(0,0,0,0)
return firstDayOfWeek
}

addDays(date, days) {
var result = new Date(date);
result.setDate(result.getDate() + days);
return result;
}

startOfMonth(date)
{
 return new Date(date.getFullYear(), date.getMonth(), 1);
}

startOfYear(date)
{
 return new Date(date.getFullYear(), 1, 1);
}

startOfLastMonth(date)
{
 return new Date(date.getFullYear(), date.getMonth()-1, 1);
}
endOfLastMonth(date)
{
 return new Date(date.getFullYear(), date.getMonth(), 1);
}

}
