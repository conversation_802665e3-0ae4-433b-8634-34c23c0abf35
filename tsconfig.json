{"compileOnSave": false, "compilerOptions": {"baseUrl": "./", "outDir": "./dist/out-tsc", "sourceMap": true, "declaration": false, "module": "es2020", "moduleResolution": "node", "emitDecoratorMetadata": true, "experimentalDecorators": true, "importHelpers": true, "resolveJsonModule": true, "allowSyntheticDefaultImports": true, "target": "es2020", "typeRoots": ["node_modules/@types"], "lib": ["es2018", "dom"]}}