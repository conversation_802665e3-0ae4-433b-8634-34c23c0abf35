import { Component, OnInit, ViewChild } from '@angular/core';
import { UntypedFormBuilder, UntypedFormGroup, Validators } from "@angular/forms";
import { ApiService } from "../../../services/api.service";
import { ToastrService } from 'ngx-toastr';
import { dateLessThanDate, maxPurchaseMethodDeductionDate } from '../../../shared/validators';
import { MatSort } from '@angular/material/sort';
import { MatTableDataSource } from '@angular/material/table';
import { DataSource } from '@angular/cdk/table';
import { MatPaginator } from '@angular/material/paginator';
import { TableFilterPipe } from '../../../pipe/table-filter.pipe';
import { DatePipe, PercentPipe, CurrencyPipe } from '@angular/common';
import { MatDialog } from '@angular/material/dialog';
import { ProductPurchaseMethodDeductionMaintenanceDialogComponent } from '../product-purchase-method-deduction-maintenance-dialog/product-purchase-method-deduction-maintenance-dialog.component';

@Component({
  selector: 'app-purchase-method-deduction-maintenance',
  templateUrl: './product-purchase-method-deduction-maintenance.component.html',
  styleUrls: ['./product-purchase-method-deduction-maintenance.component.css']
})
export class ProductPurchaseMethodDeductionMaintenanceComponent implements OnInit {
  allPurchaseMethodDeductions: any;
  activePurchaseMethodDeductions: any;
  purchaseMethodDeductionGroup: any;
  dropdowns: any;
  purchaseMethodDeductionForm: UntypedFormGroup;
  addInd: boolean = false;
  purchaseMethodDefault: number = 1;
  salesTerritoryDefault: number = 1;
  form: any;
  isReloading :boolean = false ;
  p: number = 1;
  tableArr: Element[] = [];
  purchaseMethodDeductionRate1; 
   ispurchaseMethodDeductionRateSelected : boolean = false;
  searchText: string = "";
  public date: Date;
  originalDataSource;
  dataSource;
  displayedColumns = [];
  @ViewChild(MatSort, { static: true }) sort: MatSort;
  @ViewChild(MatPaginator, { static: true }) paginator: MatPaginator;

  columnNames = [{
    id: "purchaseMethod",
    value: "Purchase Method"

  }, {
    id: "salesTerritory",
    value: "Sales Territory"
  },
  {
    id: "effectiveStartDate",
    value: "Effective Start Date"
  },
  {
    id: "effectiveEndDate",
    value: "Effective End Date"
  },
  {
    id: "purchaseMethodDeductionRate",
    value: "Purchase Method Deduction Rate"
  }];

  constructor(public apiService: ApiService, private toastMsg: ToastrService, private formBuilder: UntypedFormBuilder, private pipe: TableFilterPipe,
    private datePipe: DatePipe, private percentPipe: PercentPipe, private currencyPipe: CurrencyPipe, private dialog: MatDialog) {

  }

  ngOnInit() {
    if (!this.apiService.checkPermission('ViewRateTables')) {
      // this.router.navigate(['/ui/dashboard'])
      this.apiService.goBack();
      this.toastMsg.error("Insufficient Permissions. Please make sure your account can access this information.", "Permissions");
    }
    this.getDropdowns();

    this.purchaseMethodDeductionForm = this.formBuilder.group({
      purchaseMethod: [this.purchaseMethodDefault, [Validators.required]],
      salesTerritory: [this.salesTerritoryDefault, [Validators.required]],
      effectiveStartDate: ['', [Validators.required]],
      purchaseMethodDeductionRate: [0, [Validators.required, Validators.max(20)]],
    });

    this.onChanges();
  }

  onChanges() {
    this.purchaseMethodDeductionForm.valueChanges.subscribe(val => {
      // console.log(this.purchaseMethodDeductionForm.errors);
    });
  }

  clearDate(date: HTMLInputElement) {    
    date.value = "";
    this.date = null;
    this.purchaseMethodDeductionForm.controls.effectiveStartDate.setValue('');
    event.stopPropagation();
  }

  onSubmit() {
    if (!this.purchaseMethodDeductionForm.invalid) {
      var values = {
        purchaseMethodId: this.purchaseMethodDeductionForm.controls.purchaseMethod.value,
        salesTerritoryId: this.purchaseMethodDeductionForm.controls.salesTerritory.value,
        effectiveStartDate: this.purchaseMethodDeductionForm.controls.effectiveStartDate.value,
        purchaseMethodDeductionRate: this.purchaseMethodDeductionForm.controls.purchaseMethodDeductionRate.value,
      }

      var body = {
        newProductPurchaseMethodDeduction: values,
        productTypeName: "Roof"
      }
      // console.log("Body  202 => "+JSON.stringify(body));

      this.apiService.post('ProductPurchaseMethodDeductionMaintenance', body)
        .subscribe(data => {
          this.toastMsg.success('Purchase Method Deduction Successfully Added');
          this.isReloading = true ;
          this.getAllPurchaseMethodDeductions();
          this.getActivePurchaseMethodDeductions();
          this.addInd = !this.addInd;
        }, (err: any) => {
          if (err?.err?.message)
            this.toastMsg.error(err.message, "Server Error!");
          if (typeof err === "string") {
            let isJsonObject = false;            
            let parsedErr: any;
            try {
              parsedErr = JSON.parse(err);
              isJsonObject =parsedErr && typeof parsedErr === "object" &&!Array.isArray(parsedErr);
            } catch (e) {
              isJsonObject = false;
            }
            if (isJsonObject) {
              this.toastMsg.error(parsedErr.result, "Error!");
            } else this.toastMsg.error(err, "Error!");
          }
          if (err?.result) {
            this.toastMsg.error(err.result, "Error!");
          }
        });
    }
  }

  getAllPurchaseMethodDeductions() {
    this.apiService.get('ProductPurchaseMethodDeductionMaintenance/Retrieveall/Roof')
      .subscribe(data => {
        this.allPurchaseMethodDeductions = data;

        if(!this.isReloading)
        {
        this.purchaseMethodDeductionForm.setValidators([maxPurchaseMethodDeductionDate(this.allPurchaseMethodDeductions)]);
        } else {
          this.purchaseMethodDeductionForm.clearValidators();
        }

        if (this.purchaseMethodDeductionGroup) this.getPurchaseMethodDeductionGroup(this.purchaseMethodDeductionGroup[0]);
      }, (err: any) => {
        this.toastMsg.error(err.message, 'Server Error!');
      });
  }

  getActivePurchaseMethodDeductions() {
    this.apiService.get('ProductPurchaseMethodDeductionMaintenance/Retrieveactive/Roof')
      .subscribe(data => {
        this.activePurchaseMethodDeductions = data;
        this.displayedColumns = this.columnNames.map(x => x.id);
        this.createTable();
      }, (err: any) => {
        this.toastMsg.error(err.message, 'Server Error!');
      });
  }

  getDropdowns() {
    this.apiService.get('ProductPurchaseMethodDeductionMaintenance/dropdowns')
      .subscribe(data => {
        this.dropdowns = data;
        this.getAllPurchaseMethodDeductions();
        this.getActivePurchaseMethodDeductions();
      }, (err: any) => {
        this.toastMsg.error(err.message, 'Server Error!');
      });
  }

  getPurchaseMethodDeductionGroup(purchaseMethodDeduction: any) {
    var purchaseMethodDeductions = this.allPurchaseMethodDeductions.filter(x => x.purchaseMethodId === purchaseMethodDeduction.purchaseMethodId);
    
    this.purchaseMethodDeductionGroup = purchaseMethodDeductions;
  }

  get purchaseMethodDeductionRate() { return this.purchaseMethodDeductionForm.get('purchaseMethodDeductionRate'); }

  rowClick(purchaseMethodDeduction: any) {
    // All Sunnova Loans will have the same history because we are filtering on purchaseMethod ID.
    var filteredPurchaseMethodDeduction = this.allPurchaseMethodDeductions.filter(x => x.purchaseMethodId === purchaseMethodDeduction.purchaseMethodId);
    filteredPurchaseMethodDeduction = filteredPurchaseMethodDeduction.filter(x => x.salesTerritoryId === purchaseMethodDeduction.salesTerritoryId);
    this.purchaseMethodDeductionRate1 = filteredPurchaseMethodDeduction;
    this.ispurchaseMethodDeductionRateSelected = true;
    // console.log("purchaseMethodDeductionRate1 = >" + JSON.stringify(this.purchaseMethodDeductionRate1));
    // console.log("this.purchaseMethodDeductionRate1  = >" + JSON.stringify(this.purchaseMethodDeductionRate1));

    this.purchaseMethodDeductionForm.controls.purchaseMethod.setValue(this.purchaseMethodDeductionRate1[0].purchaseMethodId);
    this.purchaseMethodDeductionForm.controls.salesTerritory.setValue(this.purchaseMethodDeductionRate1[0].salesTerritoryId);
    this.purchaseMethodDeductionForm.controls.purchaseMethodDeductionRate.setValue(this.purchaseMethodDeductionRate1[0].purchaseMethodDeductionRate);

    const dialogRef = this.dialog.open(ProductPurchaseMethodDeductionMaintenanceDialogComponent, {
      width: '80%', data: { filteredPurchaseMethodDeduction }
    });
    dialogRef.afterClosed().subscribe(result => {
      // console.log(result);
    });
  }

  
Add(){
  // console.log("Add this.tableArr => 292   " +JSON.stringify(this.tableArr));
  this.addInd = !this.addInd;
  this.purchaseMethodDeductionRate1 = this.tableArr;
  this.isReloading = true ;
  if(!this.isReloading)
  {
   this.purchaseMethodDeductionForm.setValidators([maxPurchaseMethodDeductionDate(this.allPurchaseMethodDeductions)]);
  } else {
    this.purchaseMethodDeductionForm.clearValidators();
  }
  // console.log("Add this.purchaseMethodDeductionRate1 => 292   " +JSON.stringify(this.purchaseMethodDeductionRate1));

  this.purchaseMethodDeductionForm.controls.purchaseMethod.setValue(this.purchaseMethodDeductionRate1[0].purchaseMethodId);
  this.purchaseMethodDeductionForm.controls.salesTerritory.setValue(this.purchaseMethodDeductionRate1[0].salesTerritoryId);
  this.purchaseMethodDeductionForm.controls.purchaseMethodDeductionRate.setValue(this.purchaseMethodDeductionRate1[0].purchaseMethodDeductionRate);
}

  createTable() {
    let tableArr: Element[] = [];
    for (let i: number = 0; i <= this.activePurchaseMethodDeductions.length - 1; i++) {
      let currentRow = this.activePurchaseMethodDeductions[i];
      if(i==0)
      {
        this.tableArr[0] =this.activePurchaseMethodDeductions[0];
      // console.log(" this.tableArr = 301 > "+ JSON.stringify(this.tableArr));
      }
      tableArr.push({
        purchaseMethod: currentRow.purchaseMethod, salesTerritory: currentRow.salesTerritory, effectiveStartDate: this.datePipe.transform(currentRow.effectiveStartDate), effectiveEndDate: this.datePipe.transform(currentRow.effectiveEndDate),
        // purchaseMethodDeductionRate: this.currencyPipe.transform(currentRow.purchaseMethodDeductionRate), activeInd: currentRow.activeInd, purchaseMethodDeductionId: currentRow.purchaseMethodDeductionId,
        // Dilip Com-1138
        purchaseMethodDeductionRate: this.currencyPipe.transform(currentRow.purchaseMethodDeductionRate, "USD", true, "1.4-4"), activeInd: currentRow.activeInd, purchaseMethodDeductionId: currentRow.purchaseMethodDeductionId,
        salesTerritoryId: currentRow.salesTerritoryId, purchaseMethodId: currentRow.purchaseMethodId
      });
    }
    this.dataSource = new MatTableDataSource(tableArr);
    this.originalDataSource = tableArr;
    this.dataSource.sort = this.sort;
    this.dataSource.paginator = this.paginator;
  }

  searchForItem(): void {
    let filteredResults: Element[] = [];
    if (this.searchText == '') {
      this.dataSource = new MatTableDataSource(this.originalDataSource);
      this.dataSource.sort = this.sort;
      this.dataSource.paginator = this.paginator;
    } else {
      // filteredResults = this.originalDataSource.filter(option => option.salesTerritory.toLowerCase().includes(this.searchText));
      filteredResults = this.pipe.transform(this.originalDataSource, this.searchText);
      this.dataSource = new MatTableDataSource(filteredResults);
      this.dataSource.sort = this.sort;
      this.dataSource.paginator = this.paginator;
    }
  }
}

export interface Element {
  purchaseMethod: string,
  salesTerritory: string,
  effectiveStartDate: string,
  effectiveEndDate: string,
  purchaseMethodDeductionRate: string,
  activeInd: boolean,
  purchaseMethodDeductionId: number,
  purchaseMethodId: number,
  salesTerritoryId: number
}
