import { Component, Inject, OnInit } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';

@Component({
  selector: 'app-role-rate-dialog',
  templateUrl: './role-rate-dialog.component.html',
  styleUrls: ['./role-rate-dialog.component.css']
})
export class RoleRateDialogComponent implements OnInit {

  roleRateGroup: Element[] = [];
  roleSelectedGroup: any;
  
  constructor(public dialogRef: MatDialogRef<RoleRateDialogComponent>, @Inject(MAT_DIALOG_DATA) public data: any) { }

  ngOnInit() {
    this.roleRateGroup = this.data.roleRate; 
    this.roleRateGroup.sort((a, b) => {
      return <any>new Date(b[0].effectiveStartDate) - <any>new Date(a[0].effectiveStartDate);
    });
  }

  groupClick(group: any) {
    this.roleSelectedGroup = group;
  } 

}
