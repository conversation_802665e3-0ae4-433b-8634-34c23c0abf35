import { Component, OnInit, ElementRef, Inject, Input, <PERSON><PERSON><PERSON><PERSON>, SimpleChanges, Renderer2 } from '@angular/core';
import { FormBuilder, UntypedFormGroup } from "@angular/forms";
import { ApiService } from "../../../services/api.service";
import { DOCUMENT } from '@angular/common';
import { ToastrService } from 'ngx-toastr';
import { IRule } from 'src/app/model/rule.model';
import { Router } from '@angular/router';
import { DataIntegrationComponent } from '../../data-integration/data-integration.component';
import { IEmployeeIncentive } from 'src/app/model/employee-incentive.model';
import { UpdateContactEmployeeIncentiveForm } from 'src/app/model/update-contact-employee-incentive-form.model';
import { ContactPlanExclusion } from 'src/app/model/contact-plan-exclusion.model';
import { ICommission } from 'src/app/model/commission.model';
import { MatDialog } from '@angular/material/dialog';
import { MatTableDataSource } from '@angular/material/table';
import { MatTableModule } from '@angular/material/table';
import { DatePipe } from '@angular/common';
import { PlanInclusionHistoryComponent } from '../plan-inclusion-history/plan-inclusion-history.component';
declare var $: any;

@Component({
  selector: 'app-show-rule',
  templateUrl: './show-rule.component.html',
  styleUrls: ['./show-rule.component.css']
})
export class ShowRuleComponent implements OnInit, OnDestroy {
  @Input() viewRuleId: number;
  @Input() contactPlanId: number;
  @Input() contactId: number;
  @Input() inclusionRuleSelected: boolean;
  @Input() exclusionWithInclusion: boolean;
  rulesForm: IRule = <IRule>{};
  createRuleForm: UntypedFormGroup;
  showBasePayStructure: boolean = false;
  showBasePay: boolean = true;
  showBonusIncentive: boolean = false;
  showRateIncentive: boolean = false;
  showPaymentBook: boolean = false;
  showChildRuleTypes: boolean = false;
  employeeIncentive: boolean = false;
  childRuleTypes: any;
  count: number = 1;
  hElement: HTMLElement;
  ruleTypes: any[] = [];
  tableContentData: any;
  staticMetaData: []
  baseFormulasData: []
  isDisabled: boolean = true;
  isReadonly: boolean = true;
  invalidRule: boolean = false;
  criteriaHtml: any;
  creationTypes: any = ["Rule", "Base Formula"];
  activeCreationType: string = this.creationTypes[0];
  currentDraggedItemType: string;
  validInput: boolean = true;
  rule: any = {};
  stepsArray: any[] = [];
  start_date: any;
  end_date: any;
  commissionRuleShow: boolean = false;
  paymentRuleShow: boolean = false;
  commissionTriggerRuleShow: boolean = false;
  incentiveTypeId: number;
  effectiveStartEndDateInd: boolean;
  commissionRuleTypeId: number;
  existingActiveInd: boolean = false;
  activeInd: boolean = false;
  subcategoryName: string = "";
  // edit functionality variables
  edit: boolean = false;
  startDate: Date;
  endDate: Date;
  numberOfBonuses: number;
  offerAmount: number;
  commissionRuleTypes: any = [];
  commissionTriggerRuleTypes: any = [];
  paymentRuleTypes: any = []
  commissionType: string;
  paymentType: string;
  commissionTriggerType: string;
  contactPlanDateCriteria: string;
  oldNoOfBonuses: number;
  numberOfRemaining: number;
  oldRemaining: number;
  isClosed: boolean = false;
  recentCommissionsList: MatTableDataSource<ICommission> = new MatTableDataSource([]);
  recentCommissionColumns: string[] = ["commissionId", "opportunityName", "commissionAmount", "createdDate", "opportunityFinalized"];
  setInclusionEffectiveEndDate: string = "";

  constructor(public apiService: ApiService, private elementRef: ElementRef, private toastMsg: ToastrService, private router: Router, private datePipe: DatePipe, private dialog: MatDialog) {
    this.apiService.hideLoader = true;
    this.hElement = this.elementRef.nativeElement;
  }


  ngOnInit() {
    
  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes.viewRuleId || changes.inclusionRuleSelected) {
      this.getRuleForm();
    }
  }

  onEditChange() {
    this.edit = !this.edit;
  }

  calcBonus() {
    this.numberOfRemaining = this.numberOfBonuses - this.oldNoOfBonuses + this.oldRemaining;
  }

  SaveActiveInd() {

    let cpeForm = <ContactPlanExclusion>{
      contactPlanId: this.contactPlanId,
      ruleId: this.viewRuleId,
      activeInd: this.activeInd,
      commissionRuleTypeId: this.commissionRuleTypeId
    }

    this.apiService.post('rule/UpdateContactPlanExclusion', cpeForm)
      .subscribe(data => {
        if (data && data.statusCode == "201") {
          this.existingActiveInd = this.activeInd;
          if (this.activeInd) {
            this.toastMsg.success("Commission rule has been excluded from the plan successfully.");
          } else {
            this.toastMsg.success("Commission rule has been included into the plan successfully.");
          }
        }
      }, (err: any) => {
        this.toastMsg.error("Server Error!", "Error");
        return;
      });
  }


  onSubmitChange() {
    if (!this.contactPlanId) return;

    if ((this.startDate && this.endDate) && this.startDate > this.endDate) {
      this.toastMsg.warning("Start Date must be before End Date");
      return;
    }

    // if (this.startDate) {
    //   this.startDate = null;
    // }

    // if (this.endDate) {
    //   this.endDate = null;
    // }

    if (this.numberOfBonuses == null) {
      this.toastMsg.warning("Number Of Bonuses must be entered");
      return;
    }

    if (this.offerAmount == null) {
      this.toastMsg.warning("Offer Amount must be entered");
      return;
    }

    let updateForm = <UpdateContactEmployeeIncentiveForm>{
      contactPlanId: this.contactPlanId,
      ruleId: this.viewRuleId,
      effectiveStartDate: this.startDate,
      effectiveEndDate: this.endDate,
      numberOfBonuses: this.numberOfBonuses,
      numberOfRemaining: this.numberOfRemaining,
      offerAmount: this.offerAmount,
      isClosed: this.isClosed
    }

    this.apiService.post('EmployeeIncentives/Update', updateForm)
      .subscribe(data => {
        if (data && data.statusCode == "201") {
          this.toastMsg.success("Contact Employee Incentive Successfully Updated");
          this.edit = !this.edit;
        }
      }, (err: any) => {
        this.toastMsg.error("Server Error!", "Error");
        return;
      });
  }

  getRuleForm() {
    this.apiService.get(`Rule/${this.viewRuleId}/${0}/${this.contactPlanId}`)
      .subscribe(data => {
        if (data.statusCode === "200" || data.statusCode === "201") {
          this.rulesForm = <IRule>{
            ruleId: data.result.rule_id,
            ruleName: data.result.rule_name,
            description: data.result.description,
            ruleTypeId: data.result.rule_type_id,
            ruleTypeName: data.result.rule_type_name,
            effectiveStartDate: data.result.start_date,
            effectiveEndDate: data.result.end_date,
            noReclaim: data.result.noReclaim,
            promptAssignPlan: data.result.prompt_assign_plan,
            isInclusion: data.result.isInclusion,
            inclusionEffectiveStartDate: this.datePipe.transform(data.result.inclusionEffectiveStartDate, 'MM/dd/yyyy'),
            inclusionEffectiveEndDate: this.datePipe.transform(data.result.inclusionEffectiveEndDate, 'yyyy-MM-dd')
          };
          this.getPaymentTypes(data.result.payment_rule_type_id);
          this.getCommissionRuleTriggers(data.result.trigger_rule_type_id);
          this.getCommissionTypes(data.result.commission_rule_type_id);
          localStorage.setItem('LastRuleDetails', JSON.stringify(data.result))
          this.incentiveTypeId = data.result.rule_type_id;
          this.effectiveStartEndDateInd = this.rulesForm.effectiveStartDate != null || this.rulesForm.promptAssignPlan;


          if (data.result.parent_rule_type_name == "Bonus Incentive Goal" && !data.result.isInclusion) {
            this.rulesForm.ruleTypeId = data.result.parent_ruletype_id;
            this.rulesForm.ruleTypeName = "Bonus Incentive Goal";
            this.getRuleTypes(data.result.parent_ruletype_id);
          } else if (data.result.parent_rule_type_name == "Rate Incentive Goal" && !data.result.isInclusion) {
            this.rulesForm.ruleTypeId = data.result.parent_ruletype_id;
            this.rulesForm.ruleTypeName = "Rate Incentive Goal";
            this.getRuleTypes(data.result.parent_ruletype_id);
          }
          else {
            this.getRuleTypes(data.result.rule_type_id);
          }
          for (let i = 0; i < data.result.steps.length; i++) {
            this.stepsArray = data.result.steps;
          }

          if (this.rulesForm.ruleTypeName == "Employee Incentive") {
            // Get Contact Employee Incentive Values
            this.getContactEmployeeIncentive();
            this.getRecentCommissions();
          }
          if (this.contactPlanId != undefined) {
            this.getContactPlanExclusion();
          }
        } else {
          this.toastMsg.error("Server", 'Error!')
        }
      }, (err: any) => {
        this.toastMsg.error(err.message, 'Error!')
      });
  }

  inclusionEndDateSubmit(event: any){
    this.setInclusionEffectiveEndDate = event.target.value;
  }

  clearEndDate(date: HTMLInputElement) {
    date.value = "";
    this.setInclusionEffectiveEndDate = "";
  }

  addInclusionEndDate() {
    var sDate = new Date(this.rulesForm.inclusionEffectiveStartDate);
    var eDate = new Date(this.setInclusionEffectiveEndDate);
    eDate.setDate(eDate.getDate() + 1)
    if (sDate <= eDate) {
      this.rulesForm.inclusionEffectiveEndDate = this.setInclusionEffectiveEndDate;
      this.setInclusionEffectiveEndDate = "";
      console.log(this.rulesForm.inclusionEffectiveEndDate)
      var values = {
        ruleId: this.rulesForm.ruleId,
        ruleTypeId: this.rulesForm.ruleTypeId,
        contactPlanId: this.contactPlanId,
        inclusionEffectiveStartDate: this.rulesForm.inclusionEffectiveStartDate,
        inclusionEffectiveEndDate: this.rulesForm.inclusionEffectiveEndDate
      }
      //console.log(values)
      this.apiService.post("ContactPlanInclusion/AddInclusionEndDate", values).subscribe(data => {
        if(data && data.result) {
          this.toastMsg.success("Successfully added inclusion effective end date");
        }
        else {
          console.log(data)
        }
      }, (err: any) => {
        this.toastMsg.error(err || err.message, 'Error!')
      })
    }
    else {
      this.toastMsg.error('Invalid Date Range, End Date cannot be set before Start Date.');
    }
  }

  viewInclusionHistory() {
    this.apiService.get(`ContactPlanInclusion/GetInclusionHistory/${this.contactPlanId}/${this.rulesForm.ruleTypeId}/${this.viewRuleId}`)
      .subscribe(data => {
        if (data) {
            const historyRef = this.dialog.open(PlanInclusionHistoryComponent, {
              width: '80%',
              data:{
                inclusionHistory: data 
              }
            });
        }
        else {
          this.toastMsg.error("Error getting inclusion history");
        }
      }), (err: any) => {
        this.toastMsg.error("Server error: " + err || err.message)
      }
  }

  getRecentCommissions() {
    this.apiService.get(`Commissions/CommissionsByContactAndRule/${this.contactId}/${this.viewRuleId}`)
      .subscribe(data => {
        if (data["statusCode"] === "201" && data.result) {
          this.recentCommissionsList = new MatTableDataSource<ICommission>();

          Object.keys(data.result).forEach(row => {
            this.recentCommissionsList = new MatTableDataSource(data.result.map(row => <ICommission>{
              commissionId: row.commissionId,
              contactId: row.contactId,
              planName: row.planName,
              ruleName: row.ruleName,
              ruleId: row.ruleId,
              paidTo: row.paidTo,
              rateOnWattSold: row.rateOnWattSold,
              totalReductions: row.totalReductions,
              commissionAmount: row.commissionAmount,
              commissionablePPW: row.commissionablePPW,
              commissionWatts: row.commissionWatts,
              solarProDeduction: row.solarProDeduction,
              opportunityId: row.opportunityId,
              opportunityName: row.opportunityName,
              salesDivision: row.salesDivision,
              salesOffice: row.salesOffice,
              actualInstallDate: row.actualInstallDate,
              dateContractSigned: row.dateContractSigned,
              commissionFinalized: row.commissionFinalized,
              commissionOverridden: row.commissionOverridden,
              createdDate: row.createdDate,
              opportunityFinalized: row.opportunityFinalized
            }));
          });
        }

      }, (err: any) => {
        this.toastMsg.error(err.message, "Server Error!");
      });
  }

  /**
     * GetCommissionTypes
     */
  getCommissionTypes(id) {
    this.apiService.get('GetData/GetCommissionTypes')
      .subscribe(data => {
        if (data["statusCode"] === "201" && data.result) {
          this.commissionRuleTypes = data.result;
          this.commissionRuleShow = true;
          if (data.result.length > 0) {
            this.commissionRuleTypes.filter(item => {
              if (item.id == id) {
                this.commissionType = item.name;
              }
            });
          }
        }
      }, (err: any) => {
        this.toastMsg.error(err.message, 'Server Error!')
      });
  }

  /**
   * GetPaymentTypes
   */
  getPaymentTypes(id) {
    this.apiService.get('GetData/GetPaymentTypes')
      .subscribe(data => {
        if (data["statusCode"] === "201" && data.result) {
          this.paymentRuleTypes = data.result;
          this.paymentRuleShow = true;
          if (data.result.length > 0) {
            this.paymentRuleTypes.filter(item => {
              if (item.id == id) {
                this.paymentType = item.name;
              }
            });
          }
        }
      }, (err: any) => {
        this.toastMsg.error(err.message, 'Server Error!')
      });
  }

  /**
   * GetCommissionRuleTriggers
   */
  getCommissionRuleTriggers(id) {
    this.apiService.get('GetData/GetCommissionRuleTriggers')
      .subscribe(data => {
        if (data["statusCode"] === "201" && data.result) {
          this.commissionTriggerRuleTypes = data.result;
          this.commissionTriggerRuleShow = true;
          if (data.result.length > 0) {
            this.commissionTriggerRuleTypes.filter(item => {
              if (item.id == id) {
                this.commissionTriggerType = item.name;
                this.contactPlanDateCriteria = item.contactPlanDateCriteriaName;
              }
            });
          }
        }
      }, (err: any) => {
        this.toastMsg.error(err.message, 'Server Error!')
      });
  }

  getContactEmployeeIncentive() {
    this.apiService.get(`EmployeeIncentives/GetCurrent/${this.contactPlanId}`)
      .subscribe(
        data => {
          if (data && data.result) {
            var currentEmployeeIncentives = data.result.map(x => {
              return <IEmployeeIncentive>{
                ruleId: x.commissionRuleId,
                ruleName: x.commissionRuleName,
                effectiveStartDate: x.effectiveStartDate,
                effectiveEndDate: x.effectiveEndDate,
                numberOfBonuses: x.numberOfBonuses,
                oldNoOfBonuses: x.numberOfBonuses,
                numberOfRemaining: x.numberOfRemaining,
                oldRemaining: x.numberOfRemaining,
                offerAmount: x.offerAmount
              };
            });

            let arr = currentEmployeeIncentives.filter(cei => cei.ruleId == this.viewRuleId);

            // console.log("Contact Employee Incentive", arr);

            if (arr.length > 0) {
              this.startDate = arr[0].effectiveStartDate;
              this.endDate = arr[0].effectiveEndDate;
              this.numberOfBonuses = arr[0].numberOfBonuses;
              this.oldNoOfBonuses = arr[0].numberOfBonuses;
              this.numberOfRemaining =  arr[0].numberOfRemaining;
              this.oldRemaining = arr[0].numberOfRemaining,
              this.offerAmount = arr[0].offerAmount;
              data.result.forEach(element => {
                if (element.commissionRuleId == this.viewRuleId)
                  this.isClosed = element.isClosed;
              });
            }
          }
        },
        (err: any) => {
          this.toastMsg.error(err.message, "Server Error!");
        }
      );
  }

  getContactPlanExclusion() {
    this.apiService.get(`rule/ContactPlanExclusion/${this.viewRuleId}/${this.contactPlanId}`)
      .subscribe(
        data => {
          if (data.statusCode === "200" || data.statusCode === "201") {
            if (data.result != null) {
              this.commissionRuleTypeId = data.result.commissionRuleTypeId;
              this.activeInd = data.result.activeInd;
              this.existingActiveInd = data.result.activeInd;
            } else {
              this.commissionRuleTypeId = 0;
              this.activeInd = false;
              this.existingActiveInd = false;
            }
          }
        }
        ,
        (err: any) => {
          this.toastMsg.error(err.message, "Server Error!");
        }
      );
  }

  getValueType(data) {
    if (data != "1" && data != "2") {
      return "3";
    }
  }

  onChangeRuleType(rule) {
    if (rule == null || rule.length == 0) return;
    let rule_type = rule[0].ruleCd;
    let payment_types_data = $('#mySelect option:selected').attr('payment_types_data')
    let commission_types_data = $('#mySelect option:selected').attr('commission_types_data')
    let trigger_types_data = $('#mySelect option:selected').attr('trigger_types_data')
    let ruleId = this.rulesForm.ruleTypeId;
    this.employeeIncentive = false;
    switch (rule_type.trim()) {
      case "Base Pay Structure": {
        this.showBasePay = false;
        this.showBasePayStructure = true;
        this.showBonusIncentive = false;
        this.showRateIncentive = false;
        this.showPaymentBook = false;
        $('*').removeClass('w-72');
        $('#trinityTreeContainer').removeClass('disabled');
        $('.app-basepay-structure').addClass('w-72');


        break;
      }
      case "Bonus Incentive Goal": {
        this.showBasePay = true;
        this.showBasePayStructure = false;
        this.showBonusIncentive = true;
        this.showRateIncentive = false;
        this.showPaymentBook = false;
        $('*').removeClass('w-72');
        $('#trinityTreeContainer').removeClass('disabled');
        $('.showBasePay').addClass('w-72');


        break;
      }
      case "Rate Incentive Goal": {
        this.showBasePay = true;
        this.showBasePayStructure = false;
        this.showBonusIncentive = false;
        this.showRateIncentive = true;
        this.showPaymentBook = false;
        $('*').removeClass('w-72');
        $('#trinityTreeContainer').removeClass('disabled');
        $('.showBasePay').addClass('w-72');


        break;
      }
      case "Bonus": {
        this.showBasePay = true;
        this.showBasePayStructure = false;
        this.showBonusIncentive = false;
        this.showRateIncentive = false;
        this.showPaymentBook = false;
        $('*').removeClass('w-72');
        $('#trinityTreeContainer').removeClass('disabled');
        $('.showBasePay').addClass('w-72');


        break;
      }
      case "Payment Book": {
        this.showBasePay = false;
        this.showBasePayStructure = false;
        this.showBonusIncentive = false;
        this.showRateIncentive = false;
        this.showPaymentBook = true;
        $('*').removeClass('w-72');
        $('#trinityTreeContainer').addClass('disabled');
        $('.app-payment-book').addClass('w-72');


        break;
      }
      case "Employee Incentive": {
        this.showBasePay = true;
        this.showBasePayStructure = false;
        this.showBonusIncentive = false;
        this.showRateIncentive = false;
        this.showPaymentBook = false;
        this.employeeIncentive = true;
        $('*').removeClass('w-72');
        $('#trinityTreeContainer').removeClass('disabled');
        $('.showBasePay').addClass('w-72');
        break;
      }
      default: {
        this.showBasePay = true;
        this.showBasePayStructure = false;
        this.showBonusIncentive = false;
        this.showRateIncentive = false;
        this.showPaymentBook = false;
        $('*').removeClass('w-72');
        $('#trinityTreeContainer').removeClass('disabled');
        $('.showBasePay').addClass('w-72');


        break;
      }
    }

    //Get child rule types
    if (this.showBonusIncentive == true || this.showRateIncentive == true) {
      this.getChildRuleTypes(ruleId);
    }
    else {
      this.showChildRuleTypes = false;
      this.subcategoryName = "rule";
    }

    if (payment_types_data == 'true')
      this.paymentRuleShow = true
    else
      this.paymentRuleShow = false

    if (commission_types_data == 'true')
      this.commissionRuleShow = true
    else
      this.commissionRuleShow = false

    if (trigger_types_data == 'true')
      this.commissionTriggerRuleShow = true
    else
      this.commissionTriggerRuleShow = false

  }

  /**
   * Get Rule Types
   */
  getRuleTypes(id?: any) {
    this.apiService.get('getdata/GetRuleType')
      .subscribe(data => {
        if (data["statusCode"] === "201" && data.result) {
          this.ruleTypes = data.result;
          let ruleType = this.ruleTypes.filter(item => item.ruleTypeId == id);
          this.onChangeRuleType(ruleType);
        }
      }, (err: any) => {
        this.toastMsg.error(err.message, 'Server Error!')
      });
  }

  getChildRuleTypes(ruleId) {
    //   commissionRuleShow: boolean = false
    // paymentRuleShow: boolean = false
    // commissionTriggerRuleShow: boolean = false
    if (ruleId) {
      this.showChildRuleTypes = true
      this.apiService.get('getdata/GetRuleType/' + ruleId)
        .subscribe(data => {
          if (data["statusCode"] === "201" && data.result) {
            this.childRuleTypes = data.result;
            let arr = this.childRuleTypes.filter(item => item.ruleTypeId == this.incentiveTypeId);
            if (arr.length > 0) {
              this.subcategoryName = arr[0].ruleCd;
            }
          }
        }, (err: any) => {
          this.toastMsg.error(err.message, 'Server Error!')
        });
    }
  }

  checkCanViewOpp() {
    return this.apiService.checkPermission('ViewOpportunityDetail');
  }

  ngOnDestroy() {
    this.elementRef.nativeElement.remove();
  }

}
