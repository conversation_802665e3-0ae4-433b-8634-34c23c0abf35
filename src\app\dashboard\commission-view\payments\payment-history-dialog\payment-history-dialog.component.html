<div class="dailog-title-bg">
    <div class="dailog-title"><i class="fas fa-dollar-sign"></i> Payment History<button class="dailog-close"
            [mat-dialog-close]><span>X</span></button>
    </div>
</div>

<table mat-table [dataSource]="paymentElements" matSort class="my-table w-100">
    <ng-container matColumnDef="paymentType">
        <th mat-header-cell *matHeaderCellDef mat-sort-header> Payment Type </th>
        <td mat-cell data-td-head="Payment Type" *matCellDef="let element"> {{element.paymentType}} </td>
    </ng-container>

    <ng-container matColumnDef="paymentNumber">
        <th mat-header-cell *matHeaderCellDef mat-sort-header> Payment Number</th>
        <td mat-cell data-td-head="Payment Number" *matCellDef="let element"> {{element.paymentNumber}} </td>
    </ng-container>

    <ng-container matColumnDef="amount">
        <th mat-header-cell *matHeaderCellDef mat-sort-header> Amount </th>
        <td mat-cell data-td-head="Amount" *matCellDef="let element"> {{element.amount | currency}} </td>
    </ng-container>

    <ng-container matColumnDef="paymentStatus">
        <th mat-header-cell *matHeaderCellDef mat-sort-header> Payment Status </th>
        <td mat-cell data-td-head="Payment Status" *matCellDef="let element"> {{element.paymentStatus}} </td>
    </ng-container>

    <ng-container matColumnDef="paymentDueDate">
        <th mat-header-cell *matHeaderCellDef mat-sort-header> Payment Due Date </th>
        <td mat-cell data-td-head="Payment Due Date" *matCellDef="let element"> {{element.paymentDueDate | date}} </td>
    </ng-container>

    <ng-container matColumnDef="paymentNote">
        <th class="mat-column-25" mat-header-cell *matHeaderCellDef mat-sort-header> Payment Note </th>
        <td mat-cell data-td-head="Payment Note" *matCellDef="let element"> 
            {{element.paymentNote}} 
        </td>
    </ng-container>

    <tr mat-header-row *matHeaderRowDef="paymentColumns"></tr>
    <tr mat-row *matRowDef="let row; columns: paymentColumns;" (click)="openDialog(row)"></tr>
</table>
<mat-paginator [pageSizeOptions]="pageSizeOptionsPh">
</mat-paginator>
