<div class="page-title col-md-12 ">
  <h1 class="">Dynamic Report</h1>
  <div class="breadcrumbs">
    <a href="#">Home</a>/<span>Dynamic Report</span>
  </div>
</div>

<div class=" w-100">
  <div class="content">
    <div class="card">
      <div class="card-header-info outreachopportunities">
        <h4 class="card-title">Dynamic Report</h4>

      </div>
      <div class="card-body">
        <div class="row">
          <div class="col-md-4  gray-bg">
            <div class="row">
              <div class="form-group col-md-8">
                <label class="bmd-label-floating">Select Product</label>
                <div class="input-group">
                  <select (change)="onChangeProduct($event)" class="custom-select" id="selectProductId">
                    <option [value]="">
                      Select Product
                    </option>
                    <option *ngFor="let name of productNames" [value]="name">
                      {{name}}
                    </option>
                  </select>
                </div>
              </div>
            </div>
          </div>
          <div class="col-md-4  gray-bg">
            <div class="row">
              <div class="form-group col-md-8">
                <label class="bmd-label-floating">Select Report Category</label>
                <div class="input-group">
                  <select (change)="onChangeCategory($event)" class="custom-select" id="selectReportCategoryId" [(ngModel)]="selectReportCategoryId">
                    <option [value]="0">
                      Select Report Category
                    </option>
                    <option *ngFor="let type of filteredcategoryNames" [value]="type.dynamicReportCategoryId">
                      {{type.categoryName}}
                    </option>
                  </select>
                </div>
              </div>
            </div>
          </div>
          <div class="col-md-4  gray-bg">
            <div class="row">
              <div class="form-group col-md-8">
                <label class="bmd-label-floating">Select Report</label>
                <div class="input-group">
                  <select (change)="onChangeReport($event)" class="custom-select" id="selectReportId">
                    <option [value]="">
                      Select Report 
                    </option>
                    <option *ngFor="let type of filteredReportNames" [value]="type.dynamicReportId">
                      {{type.reportName}} 
                    </option>
                  </select>
                </div>
              </div>
            </div>
          </div>
          <div class="col-md-4  gray-bg">
          </div>
        </div>
        
        <ng-container *ngIf="filter">
          <div class="row">
            <div class="col-md-12  gray-bg">

              <!-- dynamic row -->
              <div *ngFor="let f of filterFields; let i = index;">
                <span *ngIf="i % 3 === 0">
                  <div class="row">
                    <div class="form-group col-md-4" *ngIf="i+1<=filterFields.length">
                      <label class="bmd-label-floating">{{filterFields[i].name}}</label> <br>
                      <span *ngIf="filterFields[i].type == 'string'">
                        <input type="text" class="custom-input" id="f{{i+1}}">
                      </span>
                      <span *ngIf="filterFields[i].type == 'date'">

                        <div class="input-group date-picker">
                          <input #datepickerInput1 type="date" class="custom-input" [min]="selectedReportName && selectedReportName =='PPA Bonus Savings' ? '2024-11-18':''" id="f{{i+1}}" (change)="onChangeFilter()">
                          <span *ngIf="datepickerInput1.value.length > 0" class="mat-icon cal-reset"  (click)="clearDate(datepickerInput1)"><i class="far fa-calendar-times"></i></span> 
                          <span *ngIf="datepickerInput1.value.length <= 0" class="mat-icon cal-open"><i class="far fa-calendar-alt"></i></span> 
                          

                        </div>
                      </span>
                      <span *ngIf="filterFields[i].type == 'monthYear'">
                        <div class="input-group date-picker">
                          <input #monthPicker1 class="custom-input" type="month" id="f{{i+1}}" (change)="onChangeFilter()" />
                          <span *ngIf="monthPicker1.value.length > 0" class="mat-icon cal-reset"  (click)="clearDate(monthPicker1)"><i class="far fa-calendar-times"></i></span> 
                          <span *ngIf="monthPicker1.value.length <= 0" class="mat-icon cal-open"><i class="far fa-calendar-alt"></i></span> 
                        </div>
                      </span>
                    </div>
                    <div class="form-group col-md-4" *ngIf="i+2<=filterFields.length">
                      <label class="bmd-label-floating">{{filterFields[i+1].name}}</label> <br>
                      <span *ngIf="filterFields[i+1].type == 'string'">
                        <input type="text" class="custom-input" id="f{{i+2}}">
                      </span>
                      <span *ngIf="filterFields[i+1].type == 'date'">
                        <div class="input-group date-picker">
                          <input #datepickerInput2 type="date" class="custom-input" id="f{{i+2}}" (change)="onChangeFilter()">
                          <span *ngIf="datepickerInput2.value.length > 0" class="mat-icon cal-reset"  (click)="clearDate(datepickerInput2)"><i class="far fa-calendar-times"></i></span> 
                          <span *ngIf="datepickerInput2.value.length <= 0" class="mat-icon cal-open"><i class="far fa-calendar-alt"></i></span>
                        </div>
                      </span>
                      <span *ngIf="filterFields[i+1].type == 'monthYear'">
                        <div class="input-group date-picker">
                          <input #monthPicker2 class="custom-input" type="month" id="f{{i+2}}" (change)="onChangeFilter()" />
                          <span *ngIf="monthPicker1.value.length > 0" class="mat-icon cal-reset"  (click)="clearDate(monthPicker1)"><i class="far fa-calendar-times"></i></span> 
                          <span *ngIf="monthPicker1.value.length <= 0" class="mat-icon cal-open"><i class="far fa-calendar-alt"></i></span> 
                        </div>
                      </span>

                    </div>
                    <div class="form-group col-md-4" *ngIf="i+3<=filterFields.length">
                      <label class="bmd-label-floating">{{filterFields[i+2].name}}</label> <br>
                      <span *ngIf="filterFields[i+2].type == 'string'">
                        <input type="text" class="custom-input" id="f{{i+3}}">
                      </span>
                      <span *ngIf="filterFields[i+2].type == 'date'">
                        <div class="input-group date-picker">
                          <input #datepickerInput3 type="date" class="custom-input" id="f{{i+3}}" (change)="onChangeFilter()">
                          <span *ngIf="datepickerInput3.value.length > 0" class="mat-icon cal-reset"  (click)="clearDate(datepickerInput3)"><i class="far fa-calendar-times"></i></span> 
                          <span *ngIf="datepickerInput3.value.length <= 0" class="mat-icon cal-open"><i class="far fa-calendar-alt"></i></span> 
                        </div>
                      </span>
                      <span *ngIf="filterFields[i+2].type == 'monthYear'">
                        <div class="input-group date-picker">
                          <input #monthPicker3 class="custom-input" type="month" id="f{{i+3}}" (change)="onChangeFilter()" />
                          <span *ngIf="monthPicker1.value.length > 0" class="mat-icon cal-reset"  (click)="clearDate(monthPicker1)"><i class="far fa-calendar-times"></i></span> 
                          <span *ngIf="monthPicker1.value.length <= 0" class="mat-icon cal-open"><i class="far fa-calendar-alt"></i></span> 
                        </div>
                      </span>
                    </div>
                    
                    <div class="form-group" style="margin-top: 43px;" *ngIf="selectedReportName && selectedReportName =='PPA Bonus Savings'">
                      <button *ngIf="filter" class="btn btn-primary" (click)="generate()" [disabled]="!fromDate">
                          <i class="material-icons">settings</i>
                          Generate
                        </button>
                    </div>
                  </div>
                </span>
              </div>
              <!-- dynamic row end-->
            </div>
          </div>
        </ng-container>

        <div class="row">
          <div class="form-group col-md-12 gray-bg">
            <label class="bmd-label-floating">Report Description</label> <br>
            {{description}}
          </div>
        </div>

        <div class="row">
          <div class="col-md-12">
            <div class="float-right ">
              <button *ngIf="filter" class="btn btn-primary" (click)="onSearchClick()">
                <i class="material-icons">filter_list</i>
                Search
              </button>
              <button *ngIf="filter" class="btn btn-primary" (click)="getExcelWorksheet()">
                <i class="material-icons">save_alt</i>
                Download
              </button>
            </div>
          </div>
        </div>
        <div class="overflow_scroll">
          <table mat-table [dataSource]="dynamicReport" matSort class="my-table" style="width: 1500px" *ngIf="result && columns.length > 0">

            <ng-container *ngFor="let column of columns; let i = index" [cdkColumnDef]="column.columnDef">
              <th class="mat-column-width" mat-header-cell *matHeaderCellDef (mouseenter)="column.isHovered = true"
              (mouseleave)="column.isHovered = false" (click)="onSort(column.header,i)">
                <div class="header-content" style="display: flex; justify-content: space-between; align-items: center;">
                  <span [innerHTML]=" toTitleCase(column.header.replace('_', ' '))">
                  </span>
                  <div class="sort-header">
                    <button mat-icon-button class="icon-button" aria-label="Example icon-button with a menu">
                      <ng-container *ngIf="column.isHovered">
                        <ng-container>
                          <ng-container *ngIf="column.sortOrder === 'desc'">                                                    
                            <i class="fa fa-arrow-down" aria-hidden="true"></i> 
                          </ng-container>                        
                          <ng-container *ngIf="column.sortOrder === 'asc'">
                            <i class="fa fa-arrow-up" aria-hidden="true"></i>                           
                          </ng-container>                        
                        </ng-container>                      
                      </ng-container>
                    </button>
              
                  </div>
                </div>
              </th>
              <td data-td-head="stage" mat-cell *matCellDef="let row" class="hover-approval">
                <span [innerHTML]=" getValue(column.cell(row))"></span>
              </td>
            </ng-container>
  
            <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
            <tr mat-row *matRowDef="let row; columns displayedColumns;"></tr>
          </table>
        </div>
        <mat-paginator [length]="totalCount" [pageSize]="pageSize" (page)="onPageChange($event)" [pageIndex]="pageNumber-1" [pageSizeOptions]="pageSizeOptions" showFirstLastButtons>
        </mat-paginator>

      </div>
    </div>
  </div>
</div>