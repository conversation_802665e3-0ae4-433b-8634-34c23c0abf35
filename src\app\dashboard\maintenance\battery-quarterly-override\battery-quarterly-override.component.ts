import { Component, OnInit } from '@angular/core';
import { UntypedFormBuilder, FormGroup, Validators } from "@angular/forms";
import { ApiService } from "../../../services/api.service";
import { ToastrService } from 'ngx-toastr';
import { TableFilterPipe } from '../../../pipe/table-filter.pipe';
import { DatePipe, PercentPipe, CurrencyPipe } from '@angular/common';
import { MatDialog } from '@angular/material/dialog';

@Component({
  selector: 'app-battery-quarterly-override',
  templateUrl: './battery-quarterly-override.component.html',
  styleUrls: ['./battery-quarterly-override.component.css']
})
export class BatteryQuarterlyOverrideComponent implements OnInit {
  selectedYear: number;
  quarters = [{
      id: "Q1",
      value: 1
      },
      {
      id: "Q2",
      value: 2
      }, 
      {
      id: "Q3",
      value: 3
      },
      {
      id: "Q4",
      value: 4
      }];
  selectedQuarter: number;
  constructor(public apiService: ApiService, private toastMsg: ToastrService, private formBuilder: UntypedFormBuilder, private pipe: TableFilterPipe,
    private datePipe: DatePipe, private percentPipe: PercentPipe, private currencyPipe: CurrencyPipe, private dialog: MatDialog) { }

  ngOnInit() {
    if (!this.apiService.checkPermission('ViewRateTables')) {
        this.apiService.goBack();
        this.toastMsg.error("Insufficient Permissions. Please make sure your account can access this information.", "Permissions");
    }
}


onChangeQuarter(quarter: any) {
this.selectedQuarter = quarter.target.value;
}

onSubmit() {
    if (!(this.selectedYear > 2000 && this.selectedYear < 2099)) {
        this.toastMsg.error('Please enter valid year number between 2000 and 2099');
        return;
    }

    if(this.selectedQuarter === undefined){
        this.toastMsg.error('Please select a Quarter from the dropdown.');
        return;
    }

    var body = {
        Quarter: this.selectedQuarter, 
        Year: this.selectedYear
    }

    this.apiService.post('EmployeeOverride/BatteryQuarterlyOverrides', body)
        .subscribe(data => {
            this.toastMsg.success('Battery quarterly override snapshot taken successfully. Please check dynamic report to see the snapshot.');
            }, (err: any) => {
            this.toastMsg.error(err.message, 'Server Error!');
        });
}

}
