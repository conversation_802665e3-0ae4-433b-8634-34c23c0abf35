import { Component, Inject, OnInit, ViewChild } from '@angular/core';
import { ICommissionRuleDTO } from "../../../../model/ICommissionRuleDTO";
import { IInclusionRules } from 'src/app/model/IInclusionRules';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { UntypedFormBuilder, UntypedFormGroup, Validators } from "@angular/forms";
import { ToastrService } from 'ngx-toastr';
import { IRule } from 'src/app/model/rule.model';
import { AddOnRule } from 'src/app/dashboard/change-plan/employee-incentive-dialog/employee-incentive-dialog.component';
import { BasePayStructurePromptComponent } from 'src/app/dashboard/change-plan/base-pay-structure-prompt/base-pay-structure-prompt.component';
import { IPlanDetail } from 'src/app/model/plan.model';
import { ApiService } from 'src/app/services/api.service';
import { IPrompt, Prompt } from 'src/app/model/prompt.model';
import { IRulePrompt } from 'src/app/model/rule-prompt.model';
import { IRuleItem } from 'src/app/model/rule-item.model';

@Component({
  selector: 'app-add-plan-inclusion-dialog',
  templateUrl: './add-plan-inclusion-dialog.component.html',
  styleUrls: ['./add-plan-inclusion-dialog.component.css']
})
export class AddPlanInclusionDialogComponent implements OnInit {
planInclusionRules: IInclusionRules;
planInclusionForm: UntypedFormGroup;
planInclusionRuleTypes = [{
  id: 0,
  value: "Bonus"
 },
 {
  id: 1,
  value: "Bonus Incentive Goal"
 },
 {
  id: 2,
  value: "Rate Incentive Goal"
 },
 {
  id: 3,
  value: "Base Pay"
 }];
 activeRules: any[];

@ViewChild(BasePayStructurePromptComponent) basePayStructurePrompt: BasePayStructurePromptComponent;
prompts: IRule;
promptsValues: object = {};
addOn: AddOnRule;
basePayStructureRules: IPlanDetail[];
enableBpForm = false;
metadataPrompts: Prompt[] = [];

  constructor(@Inject(MAT_DIALOG_DATA) public data: any, private formBuilder: UntypedFormBuilder, private toastMsg: ToastrService, public apiService: ApiService, private dialogRef: MatDialogRef<AddPlanInclusionDialogComponent>) { this.planInclusionRules = data.planInclusionRules; }

  ngOnInit() {
    this.activeRules = this.planInclusionRules.bonuses;
    this.planInclusionForm = this.formBuilder.group({
      selectedInclusionRuleType: [0, Validators.required],
      selectedInclusionRuleId: [this.activeRules[0].commissionRuleId],
      selectedInclusionRuleTypeId: [0],
      effectiveStartDate: ["", Validators.required],
      effectiveEndDate: [""],
      selectedRuleId: [null],
      selectedBasePayRuleId: [null]
    });
    //console.log(this.planInclusionForm.get("selectedInclusionRuleType"))
    if (this.data.component == "repConfig") {
      this.addBasePayStructure();
    }
    this.onChanges();
    this.planInclusionForm.controls.effectiveEndDate;
  }

  onChanges() {
    this.planInclusionForm.get("selectedInclusionRuleType").valueChanges.subscribe(val => {
      this.activeRules = []
      this.activeRules = val == 0 ? this.planInclusionRules.bonuses : (val == 1 ? this.planInclusionRules.bonusIncentiveGoals : ( val == 2 ? this.planInclusionRules.rateIncentiveGoals : this.planInclusionRules.basePay))
      // console.log(this.activeRules)
      if (val == 0 || val == 3) {
        this.planInclusionForm.controls['selectedInclusionRuleId'].setValue(this.activeRules[0].commissionRuleId);
        this.planInclusionForm.controls['selectedInclusionRuleTypeId'].setValue(0);
      }
      else {
        this.planInclusionForm.controls['selectedInclusionRuleId'].setValue(null);
        this.planInclusionForm.controls['selectedInclusionRuleTypeId'].setValue(this.activeRules[0].commissionRuleTypeId);
      }

      if(val == 3 && this.data.component === 'repConfig'){
        this.getRulePrompts();
        this.planInclusionForm.controls['selectedBasePayRuleId'].setValue(this.activeRules.find(x => x.commissionRuleId == this.planInclusionForm.get("selectedInclusionRuleId").value).commissionRuleId);
        //console.log(this.planInclusionForm.get("selectedRuleId").value)
        this.enableBpForm = true;
      }
      else {
        this.enableBpForm = false;
      }
    });

    this.planInclusionForm.get("selectedInclusionRuleId").valueChanges.subscribe(val => {
      if (this.planInclusionForm.get("selectedInclusionRuleType").value == 3) {
        this.planInclusionForm.controls['selectedBasePayRuleId'].setValue(this.activeRules.find(x => x.commissionRuleId == this.planInclusionForm.get("selectedInclusionRuleId").value).commissionRuleId);
      }
    });
  }

  submit() {
    if(this.addOn) {
      //console.log(this.addOn)
      var addOnPromptsValues = this.addOn.promptValues;
      
      if (this.addOn.basePayRuleId > 0) {
        (addOnPromptsValues as any).basePayCommissionRuleId = this.getSelectedBasePayRule().commissionRuleId;
      }

      if (addOnPromptsValues == null)
        addOnPromptsValues = {
          commissionRuleId: this.addOn.ruleId,
          ruleTypeName: this.addOn.ruleTypeName,
          basePayCommissionRuleId: 0
        };
      if ((addOnPromptsValues as any).commissionRuleId == null) {
        (addOnPromptsValues as any).commissionRuleId = this.addOn.ruleId;
        (addOnPromptsValues as any).ruleTypeName = this.addOn.ruleTypeName;
      }
      if ((addOnPromptsValues as any).ruleItems == null)
        (addOnPromptsValues as any).ruleItems = [];

      if (addOnPromptsValues != null)
        (addOnPromptsValues as any).ruleTypeName = this.addOn.ruleTypeName;

      this.promptsValues = addOnPromptsValues;
    }

    let values = {
      commissionRuleTypeName: this.planInclusionRuleTypes.find(x => x.id == this.planInclusionForm.controls.selectedInclusionRuleType.value).value,
      commissionRuleId: this.planInclusionForm.controls.selectedInclusionRuleId.value,
      effectiveStartDate: this.planInclusionForm.controls.effectiveStartDate.value,
      effectiveEndDate: this.planInclusionForm.controls.effectiveEndDate.value,
      commissionRuleTypeId: this.planInclusionForm.controls.selectedInclusionRuleTypeId.value,
      planRule: this.promptsValues
    }

    if(values.effectiveEndDate){
      var eDate = new Date(values.effectiveEndDate);
      var sDate = new Date(values.effectiveStartDate);
      if (sDate > eDate) {
        this.toastMsg.error('Invalid Date Range, End Date cannot be set before Start Date.');
      }
    }
    else {
      values.effectiveEndDate = null;
    }

    var datesValid = true;
    if (Object.keys(this.promptsValues).length > 0) {

      (this.promptsValues as any).ruleItems.forEach((y: IRuleItem) => {
  
        // Validate Dates
  
        let startDateInputs = y.ruleItemInputs.filter(input => input.columnName == 'Start_Date');
        let endDateInputs = y.ruleItemInputs.filter(input => input.columnName == 'End_Date');
  
        if (startDateInputs.length > 0 && endDateInputs.length > 0) {
          let startDate = new Date(startDateInputs[0].columnValue);
          let endDate = new Date(endDateInputs[0].columnValue);
  
          if (startDate > endDate) datesValid = false;
        }
      });
    }

    if (!datesValid){
      this.toastMsg.warning("Start Dates cannot be greater than End Dates");
    }
    else {
      //console.log(values);
      this.dialogRef.close(values);
    }
  }

  clearStartDate(date: HTMLInputElement) {
    date.value = "";
    this.planInclusionForm.controls.effectiveStartDate.setValue('');
  }

  clearEndDate(date: HTMLInputElement) {
    date.value = "";
    this.planInclusionForm.controls.effectiveEndDate.setValue('');
  }

  checkAllPromptsEntered() {
    if (this.basePayStructurePrompt && this.basePayStructurePrompt.ruleItems && this.basePayStructurePrompt.ruleItems.find(r => r.ruleItemInputs.find(rii => rii.columnValue == null || rii.columnValue == "") != null) != null) {
      return false;
    } else if (!this.basePayStructurePrompt || !this.basePayStructurePrompt.ruleItems) {
      return false;
    } else {
      return true;
    }
  }

  checkValidDates() {
    var valid = true;

    this.basePayStructurePrompt.ruleItems.forEach((item, i) => {
      var next = this.basePayStructurePrompt.ruleItems[i + 1];

      if (next == null || next.ruleItemInputs == null || next.ruleItemInputs.find(x => x.columnName == "Start_Date") == null) return;

      if (item.ruleItemInputs && item.ruleItemInputs.find(x => x.columnName == "Start_Date") != null && item.ruleItemInputs.find(x => x.columnName == "Start_Date").columnValue >= next.ruleItemInputs.find(x => x.columnName == "Start_Date").columnValue) {
        valid = false;
      }
    });

    return valid;
  }

  addBasePayStructure() {
    this.apiService.get('GetData/basePayStructureRules')
      .subscribe(data => {
        if (data && data.result) {
          // console.log(data);
          this.basePayStructureRules = data.result.map(x => { return <IPlanDetail>x });
          // var existingRules = this.addOns.map(x => { return x.ruleId });
          // basePayStructureRules[0].rules = basePayStructureRules[0].rules.filter(x => !existingRules.includes(x.ruleId));
        }
      }, err => {
        this.toastMsg.error(err.message, "Error");
      });
  }

  getSelectedBasePayRule(){
    return this.activeRules.find(x => x.commissionRuleId == this.planInclusionForm.get("selectedInclusionRuleId").value);
  }

  existingPlanBpInclusion() {
    if (this.planInclusionForm.get("selectedInclusionRuleType").value == 3 && this.data.component == 'repConfig') {
      return !this.checkAllPromptsEntered() || !this.checkValidDates();
    }
    return false;
  }

  getRulePrompts(): void {
    if (!this.data.planId) {
      return;
    }

    this.apiService.get(`Rule/Prompts/${this.data.planId}`)
      .subscribe(data => {
        if (data && data.statusCode == "201") {
          this.metadataPrompts = data.result.map(prompt => {
            return <IPrompt>{
              ruleId: prompt.ruleId,
              schema: prompt.schema,
              tableName: prompt.tableName,
              columnName: prompt.columnName,
              displayName: prompt.displayName
            }
          }).map(iPrompt => {
            return <Prompt>{ ...iPrompt }
          });

          //console.log("Metadata Prompts", this.metadataPrompts);
        }
      }, (err: any) => {
        // this.toastMsg.error("Server Error", "Error");
        return;
      });
  }

  onSelect() {
    this.prompts = null;
    this.addOn = null;
    var rulePrompts = this.basePayStructureRules[0].rules.filter(p => p.ruleId == this.planInclusionForm.get("selectedRuleId").value)[0];
    // console.log(rulePrompts);
    if (rulePrompts) {
      this.prompts = rulePrompts;
      this.addOn = <AddOnRule>rulePrompts;
      this.addOn.promptValues = {};
      this.addOn.basePayRuleId = this.planInclusionForm.get("selectedBasePayRuleId").value;
      this.addOn.basePayRuleName = this.activeRules.find(x => x.commissionRuleId == this.planInclusionForm.get("selectedBasePayRuleId").value).commissionRuleName

      //console.log(this.addOn);
    }
  }

  onRulePromptChange(prompt: IRulePrompt) {
    this.addOn.promptValues = prompt;
    // console.log("Prompts:", this.promptsValues);
  }

}
