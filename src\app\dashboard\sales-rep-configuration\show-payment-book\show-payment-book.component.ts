import { Component, OnInit, Input, Output, EventEmitter } from '@angular/core';
import { ApiService } from "../../../services/api.service";
import { ToastrService } from 'ngx-toastr';
import { MatDialog } from '@angular/material/dialog';
import { ConfirmationModalComponent } from 'src/app/confirmation-modal/confirmation-modal.component';

@Component({
  selector: 'app-show-payment-book',
  templateUrl: './show-payment-book.component.html',
  styleUrls: ['./show-payment-book.component.css']
})
export class ShowPaymentBookComponent implements OnInit {
  @Input() contactId: number;
  @Input() payPlanId: number;
  @Input() contactPlanId: number;
  @Output() updated = new EventEmitter<boolean>();

  // setter for paymentBookId to run getPaymentBook() on input
  private _paymentBookId;
  @Input()
  set paymentBookId(paymentBookId: number) {
    this._paymentBookId = paymentBookId;
    this.getPaymentBook(this.paymentBookId);
  }
  get paymentBookId(): number { return this._paymentBookId; }
  // setter for paymentBookId to run getPaymentBook() on input
  private _previewPaymentBookId;
  @Input()
  set previewPaymentBookId(previewPaymentBookId: number) {
    this._previewPaymentBookId = previewPaymentBookId;
    this.getPaymentBook(this.previewPaymentBookId);
  }
  get previewPaymentBookId(): number { return this._previewPaymentBookId; }

  showPreview: boolean = false;
  paymentBook: any;
  previewPaymentBook: any;
  paymentBookSchedule: [];
  paymentBooks: [];
  constructor(private apiService: ApiService, private toastMsg: ToastrService, private dialog: MatDialog) { }

  ngOnInit() {
    this.getPaymentBooks();
    this.getPaymentBookSchedules(this.contactPlanId);
  }

  getPaymentBook(id) {
    this.apiService.get(`paymentbook/${id}`)
      .subscribe(data => {
        if (data["statusCode"] === "201" && data.result) {
          if (!this.paymentBook) {
            this.paymentBook = data.result;
          } else {
            this.previewPaymentBook = data.result;
          }
        }
      }, (err: any) => {
        // console.log(err)
        this.toastMsg.error(err.message, 'Server Error!')
      });
  }

  getPaymentBooks() {
    this.apiService.get(`paymentbook`)
      .subscribe(data => {
        if (data["statusCode"] === "201" && data.result) {
          this.paymentBooks = data.result;
        }
      }, (err: any) => {
        // console.log(err)
        this.toastMsg.error(err.message, 'Server Error!')
      });
  }

  getPaymentBookSchedules(contactPlanId) {
    this.apiService.get(`paymentbook/GetContactPaymentBookSchedule/${contactPlanId}`)
      .subscribe(data => {
        if (data["statusCode"] === "201" && data.result) {
          this.paymentBookSchedule = data.result;
        }
      }, (err: any) => {
        // console.log(err)
        this.toastMsg.error(err.message, 'Server Error!')
      });
  }

  assignPaymentBook(confirmed: boolean) {
    if (confirmed) {
      var payload = {
        contactId: this.contactId,
        ruleId: this.previewPaymentBookId,
        planId: this.payPlanId
      }

      this.apiService.post('paymentbook', payload)
        .subscribe(data => {
          this.updated.emit(true);
        }, (err: any) => {
          // console.log(err)
          this.toastMsg.error(err.message, 'Server Error!')
        });

      this.paymentBook = null;
      this.paymentBookId = this.previewPaymentBookId;
      this.previewPaymentBook = null;
      this.showPreview = false;
    }
  }

  confirm() {
    let dialogRef = this.dialog.open(ConfirmationModalComponent, {
      data: {
        message: "Are you sure you want to assign a new Payment Book?"
      }
    });

    dialogRef.afterClosed().subscribe((res: boolean) => {
      if (res) {
        this.assignPaymentBook(res);
      } else {

      }
    })
  }

  allowChange() {
    this.showPreview = !this.showPreview;
  }
}
