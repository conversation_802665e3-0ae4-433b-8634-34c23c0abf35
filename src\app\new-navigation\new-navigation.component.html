<mat-drawer-container *ngIf="this.apiService.showMenu()" class="example-container mat-typography"
  [className]="isExpanded ? 'sidenav-expanded' : 'sidenav-collapsed'" autosize>
  <mat-drawer #drawer mode="side" disableClose="true" opened="true" class="drawer-shadow" *ngIf="apiService.getRole() != 'Sales Reps'">
    <div class="main-head">
      <div class="logo-cont">
        <a class="trinity-logo-sidebar"><img src="/assets/images/onePAY_Logo.png" style="transform:scale(0.5);" alt=""></a>
      </div>
      <div class="menu-cont">
        <ng-container *ngIf="isExpanded; else notExpanded">
          <button mat-mini-fab (click)="leftArrowClick()" color="primary" class="menu-toggle">
            <mat-icon aria-label="Menu" style="color: #fff;">menu_open</mat-icon>
          </button>


        </ng-container>
        <ng-template #notExpanded>
          <button mat-mini-fab (click)="rightArrowClick()" color="primary" class="menu-open">
            <mat-icon aria-label="Menu" style="color: #fff;">menu</mat-icon>
          </button>
        </ng-template>
      </div>
    </div>
    

    <mat-nav-list>
      <mat-list-item class="sidebar-custom" [routerLink]="['/ui/usermanagement']">
        <ng-container
          *ngIf="apiService.checkPermission('CreateNewUser') || apiService.checkPermission('AssignRolesToUser')">
          <i data-toggle="tooltip" data-placement="right" title="User Management" class="fas fa-user-tie"
            [routerLink]="['/ui/usermanagement']"></i>
          <a mat-line [routerLink]="['/ui/usermanagement']">User Management</a>
        </ng-container>
      </mat-list-item>
      <mat-list-item class="sidebar-custom" [routerLink]="['/ui/commissions/data-integration']">
        <ng-container *ngIf="apiService.checkPermission('DataIntegration')">

          <i class="fas fa-server" data-toggle="tooltip" data-placement="right" title="Data Integration"
            [routerLink]="['/ui/commissions/data-integration']"></i>
          <a mat-line [routerLink]="['/ui/commissions/data-integration']">Data Integration</a>
        </ng-container>
      </mat-list-item>
      <mat-list-item class="sidebar-custom" [routerLink]="['/ui/commissions/plan-execution']">
        <ng-container *ngIf="apiService.checkPermission('DataIntegration')">

          <i class="fas fa-tasks" data-toggle="tooltip" data-placement="right" title="Plan Execution"
            [routerLink]="['/ui/commissions/plan-execution']"></i>
          <a mat-line [routerLink]="['/ui/commissions/plan-execution']">Plan Execution</a>
        </ng-container>
      </mat-list-item>
      <mat-list-item class="sidebar-custom" [routerLink]="['/ui/commissions/rule/create']">
        <ng-container *ngIf="apiService.checkPermission('CreateRule')">
          <i class="fas fa-pencil-ruler" data-toggle="tooltip" data-placement="right" title="Rule Builder"
            [routerLink]="['/ui/commissions/rule/create']"></i>
          <a mat-line [routerLink]="['/ui/commissions/rule/create']">Rule Builder</a>
        </ng-container>
      </mat-list-item>
      <mat-list-item class="sidebar-custom" [routerLink]="['/ui/commissions/createplan']">
        <ng-container *ngIf="apiService.checkPermission('CreatePlan')">
          <i data-toggle="tooltip" data-placement="right" title="Plan Builder"
            [routerLink]="['/ui/commissions/createplan']" class="fas fa-file-signature"></i>
          <a mat-line [routerLink]="['/ui/commissions/createplan']">Plan Builder</a>
        </ng-container>
      </mat-list-item>
      <mat-list-item class="sidebar-custom">
        <ng-container *ngIf=" apiService.checkPermission('ApprovePayments')">
          <span class="material-icons" data-toggle="tooltip" data-placement="right" title="Payments"
            (click)="iconClickPayment()">
            monetization_on
          </span>

          <a mat-line (click)="paymentTextPayment()"
            *ngIf="apiService.checkPermission('ApprovePayments') || apiService.checkPermission('RejectPayments') || apiService.checkPermission('ViewPaymentBooks')">Payments
            <ng-container *ngIf="!subPaymentTables; else subPaymentTablesArrow">
              <mat-icon mat-list-icon (click)="downArrowPayment()">add</mat-icon>
            </ng-container>
            <ng-template #subPaymentTablesArrow>
              <mat-icon mat-list-icon (click)="upArrowPayment()">remove</mat-icon>
            </ng-template>
          </a>

        </ng-container>
      </mat-list-item>
      <ng-container *ngIf=" subPaymentTables">
        <div class="sub-menu">
          <mat-list-item
            *ngIf="apiService.checkPermission('ApprovePayments') || apiService.checkPermission('RejectPayments')">
            <a mat-line class="sidebar-custom-inner" *ngIf="isExpanded"
              [routerLink]="['/ui/commissions/payments']">Payment
              Approvals</a>
          </mat-list-item>
          <mat-list-item *ngIf="apiService.checkPermission('ViewPaymentBooks')">
            <a mat-line class="sidebar-custom-inner" *ngIf="isExpanded"
              [routerLink]="['/ui/commissions/paymentwithdrawals']">Payment Withdrawals</a>
          </mat-list-item>
        </div>
      </ng-container>


      <mat-list-item class="sidebar-custom">
        <ng-container *ngIf=" apiService.checkPermission('ViewRateTables')">
          <span class="material-icons" data-toggle="tooltip" data-placement="right" title="Maintanace"
            (click)="iconClickmaintanence()">
            settings_applications
          </span>

          <a mat-line (click)="maintenanceTextMaintanance()" *ngIf=" apiService.checkPermission('ViewRateTables')">
            Maintenance
            <ng-container *ngIf="!submaintananceTables; else subPaymentTablesArrow">
              <mat-icon mat-list-icon (click)="downArrowMaintanance()">add</mat-icon>
            </ng-container>
            <ng-template #subPaymentTablesArrow>
              <mat-icon mat-list-icon (click)="upArrowMaintanance()">remove</mat-icon>
            </ng-template>
          </a>

        </ng-container>
      </mat-list-item>

      <ng-container *ngIf=" submaintananceTables">
        <div class="sub-menu">

          <mat-list-item *ngIf="apiService.checkPermission('ViewRateTables')">
            <a mat-line class="sidebar-custom-inner" *ngIf="isExpanded"
              [routerLink]="['/ui/maintenance/batteryquarterlyoverridesnapshot']">
              Battery Quarterly Override Snapshot
            </a>
          </mat-list-item>

          <mat-list-item *ngIf="apiService.checkPermission('ViewRateTables')">
            <a mat-line class="sidebar-custom-inner" *ngIf="isExpanded"
              [routerLink]="['/ui/maintenance/commissionabledivisioncontactinclusion']">
              Commissionable Division Contact Inclusions
            </a>
          </mat-list-item>

          <mat-list-item *ngIf="apiService.checkPermission('ViewRateTables')">
            <a mat-line class="sidebar-custom-inner" *ngIf="isExpanded"
              [routerLink]="['/ui/maintenance/employeeoverrideroofing']">
              Employee Override Roofing Snapshot
            </a>
          </mat-list-item>

          <mat-list-item *ngIf="apiService.checkPermission('ViewRateTables')">
            <a mat-line class="sidebar-custom-inner" *ngIf="isExpanded"
              [routerLink]="['/ui/maintenance/employeeoverride']">
              Employee Override Snapshot
            </a>
          </mat-list-item>

          <mat-list-item *ngIf="apiService.checkPermission('ViewRateTables')">
            <a mat-line class="sidebar-custom-inner" *ngIf="isExpanded"
              [routerLink]="['/ui/maintenance/homedepotoutreachsnapshot']">
              Home Depot FM Outreach Snapshot
            </a>
          </mat-list-item>

          <mat-list-item *ngIf="apiService.checkPermission('ViewRateTables')">
            <a mat-line class="sidebar-custom-inner" *ngIf="isExpanded"
              [routerLink]="['/ui/maintenance/leadsourcecommissionsmodifier']">
              Lead Source Qualifiers
            </a>
          </mat-list-item>

          <mat-list-item *ngIf="apiService.checkPermission('ViewRateTables')">
            <a mat-line class="sidebar-custom-inner" *ngIf="isExpanded"
              [routerLink]="['/ui/maintenance/monthlyroofingoverridesnapshot']">
              Monthly Roofing Override Snapshot
            </a>
          </mat-list-item>

          <mat-list-item *ngIf="apiService.checkPermission('ViewRateTables')">
            <a mat-line class="sidebar-custom-inner" *ngIf="isExpanded"
              [routerLink]="['/ui/maintenance/quarterlyroofingoverridesnapshot']">
              Quarterly Direct Roofing Override Snapshot
            </a>
          </mat-list-item>

          

        </div>
      </ng-container>

      
      <mat-list-item class="sidebar-custom" [routerLink]="['/ui/commissions/salesrepdashboard', apiService.getContactId()]">
        <ng-container *ngIf="apiService.checkPermission('ViewSalesRepDashboard') && (apiService.getRole() == 'Sales Manager - Direct' || apiService.getRole() == 'Sales Manager - Outreach' || apiService.getRole() == 'Sales Manager - Traditional')">
          <i data-toggle="tooltip" data-placement="right" title="Dashboard" class="fas fa-chart-line"
            [routerLink]="['/ui/commissions/salesrepdashboard', apiService.getContactId()]"></i>
          <a mat-line [routerLink]="['/ui/commissions/salesrepdashboard', apiService.getContactId()]">Dashboard</a>
        </ng-container>
      </mat-list-item>

        <mat-list-item class="sidebar-custom" [routerLink]="['/ui/commissions/dynamicreports']">
          <ng-container *ngIf="apiService.checkPermission('DynamicReports') && (apiService.getRole() == 'Sales Manager - Direct' || apiService.getRole() == 'Sales Manager - Outreach' || apiService.getRole() == 'Sales Manager - Traditional')">
            <i data-toggle="tooltip" data-placement="right" title="Dynamic Reports" class="fas fa-file-alt"
              [routerLink]="['/ui/commissions/dynamicreports']"></i>
            <a mat-line [routerLink]="['/ui/commissions/dynamicreports']">Dynamic Reports</a>
          </ng-container>
        </mat-list-item>
      

      <mat-list-item class="sidebar-custom">
        <ng-container *ngIf="apiService.checkPermission('PaymentBookReports')">
          <span class="material-icons far fa-file-alt" style="font-size:16px" data-toggle="tooltip"
            data-placement="right" title="Reports" (click)="iconClickReport()"> </span>

          <a mat-line (click)="paymentTextReport()" *ngIf="apiService.checkPermission('PaymentBookReports')">Reports
            <ng-container *ngIf="!subReportTables; else subReportTablesArrow">
              <mat-icon mat-list-icon (click)="downArrowReport()">add</mat-icon>
            </ng-container>
            <ng-template #subReportTablesArrow>
              <mat-icon mat-list-icon (click)="upArrowReport()">remove</mat-icon>
            </ng-template>
          </a>

        </ng-container>
      </mat-list-item>
      <ng-container *ngIf="subReportTables">
        <div class="sub-menu">
          <mat-list-item *ngIf="apiService.checkPermission('PaymentBookReports')">
            <a mat-line class="sidebar-custom-inner" *ngIf="isExpanded" [routerLink]="['/ui/commissions/report']">
              Paycom
              Exports
            </a>
          </mat-list-item>
          <mat-list-item *ngIf="apiService.checkPermission('DynamicReports')">
            <a mat-line class="sidebar-custom-inner" *ngIf="isExpanded" [routerLink]="['/ui/commissions/dynamicreports']">Dynamic
              reports</a>
          </mat-list-item>
        <mat-list-item *ngIf="apiService.checkPermission('ViewRateTables')">
          <a mat-line class="sidebar-custom-inner" *ngIf="isExpanded" [routerLink]="['/ui/maintenance/leadsource']">
            Lead Sources
          </a>
        </mat-list-item>

        </div>
      </ng-container>
      <mat-list-item class="sidebar-custom" [routerLink]="['/ui/reports/custom-report']">
        <ng-container *ngIf="apiService.checkPermission('CustomReports')">
          <i data-toggle="tooltip" data-placement="right" title="Custom Report"
            [routerLink]="['/ui/reports/custom-report']" class="fas fa-chart-pie"></i>
          <a mat-line [routerLink]="['/ui/reports/custom-report']">Custom Report</a>
        </ng-container>
      </mat-list-item>

      <mat-list-item class="sidebar-custom">
        <ng-container *ngIf="apiService.checkPermission('ViewRateTables')">
          <i class="fas fa-table" data-toggle="tooltip" data-placement="right" title="Rate Tables"
            (click)="iconClickRate()"></i>
          <a mat-line (click)="paymentTextRate()">Rate Tables
            <ng-container *ngIf="!subRateTables; else showRateTable">
              <mat-icon mat-list-icon (click)="downArrowRate()">add</mat-icon>
            </ng-container>
            <ng-template #showRateTable>
              <mat-icon mat-list-icon (click)="upArrowRate()">remove</mat-icon>
            </ng-template>

          </a>

        </ng-container>
      </mat-list-item>

      <ng-container *ngIf="subRateTables">
          <div class="sub-menu">
            <mat-list-item>
              <a mat-line [routerLink]="['/ui/commissions/ratetables/BatteryCommissionRate']"> Battery Commission Rates
                </a>
            </mat-list-item>

            <mat-list-item>
              <ng-container>
                <a mat-line class="font-custom" (click)="onClickBatteryOverrides()">Battery Overrides
                  <ng-container *ngIf="!isBatteryOverrides; else showBatteryOverrides">
                    <mat-icon mat-list-icon class="icon-left-roof-override" (click)="downArrowBatteryOverrides()">add</mat-icon>
                  </ng-container>
                  <ng-template #showBatteryOverrides>
                    <mat-icon mat-list-icon class="icon-left-roof-override" (click)="upArrowBatteryOverrides()">remove</mat-icon>
                  </ng-template>
                </a>
              </ng-container>
            </mat-list-item>
            
            <ng-container *ngIf="isBatteryOverrides">
              <div class="nested-sub-menu">
                <mat-list-item>
                  <a mat-line [routerLink]="['/ui/commissions/ratetables/batteryemplyeerole']">Employee Role</a>
                </mat-list-item>
  
                <mat-list-item>
                  <a mat-line [routerLink]="['/ui/commissions/ratetables/rolerate']">Role Rates</a>
                </mat-list-item>
  
                <mat-list-item>
                  <a mat-line [routerLink]="['/ui/commissions/ratetables/tieredamount']">Tiered Amounts</a>
                </mat-list-item>
              </div>
            </ng-container>

            <mat-list-item>
              <a mat-line [routerLink]="['/ui/commissions/ratetables/batteryretrofitcommissionsrate']">Battery Retrofit Commission Rates</a>
            </mat-list-item>

            <mat-list-item>
              <a mat-line [routerLink]="['/ui/commissions/ratetables/employeeoverriderates']">Employee Override Rates</a>
            </mat-list-item>


            <mat-list-item>
              <a mat-line [routerLink]="['/ui/commissions/ratetables/employeeoverrideratestier']">Employee Override Rates Tier</a>
            </mat-list-item>

            <mat-list-item>
              <a mat-line [routerLink]="['/ui/commissions/ratetables/financepartnerdeduction']">Finance
                Partner Deductions</a>
            </mat-list-item>

            <mat-list-item>
              <a mat-line [routerLink]="['/ui/commissions/ratetables/ppabonusflatrate']">Flat Rate PPA Bonus</a>
            </mat-list-item>

            <mat-list-item>
              <a mat-line *ngIf="isExpanded"
                [routerLink]="['/ui/commissions/ratetables/installationtypededuction']">Installation Type Deductions</a>
            </mat-list-item>

            <mat-list-item>
              <a mat-line [routerLink]="['/ui/commissions/ratetables/inverterdeduction']">Inverter
                Deductions</a>
            </mat-list-item>

            <mat-list-item>
              <a mat-line [routerLink]="['/ui/commissions/ratetables/modulededuction']">Module
                Deductions</a>
            </mat-list-item>

            <mat-list-item>
              <a mat-line [routerLink]="['/ui/commissions/ratetables/outreachconfig']">Outreach
                Configuration</a>
            </mat-list-item>

            <mat-list-item>
              <a mat-line [routerLink]="['/ui/commissions/ratetables/permitdeduction']">Permit Deductions</a>
            </mat-list-item>

            <mat-list-item>
              <a mat-line [routerLink]="['/ui/commissions/ratetables/ppabonusrate']">PPA Bonus
                Rates</a>
            </mat-list-item>

            <mat-list-item>
              <a mat-line [routerLink]="['/ui/commissions/ratetables/ppwbonusrate']">PPW Bonus
                Rates</a>
            </mat-list-item>

            <mat-list-item>
              <a mat-line [routerLink]="['/ui/commissions/ratetables/batterypurchasemethoddeduction']">Purchase
                Method Deductions(Battery)</a>
            </mat-list-item> 

            <mat-list-item>
              <a mat-line [routerLink]="['/ui/commissions/ratetables/productpurchasemethoddeduction']">Purchase
                Method Deductions(Roof)</a>
            </mat-list-item> 

            <mat-list-item>
              <a mat-line [routerLink]="['/ui/commissions/ratetables/purchasemethoddeduction']">Purchase
                Method Deductions(Solar)</a>
            </mat-list-item>

            <mat-list-item>
              <a mat-line [routerLink]="['/ui/commissions/ratetables/roofcommissionrate']">Roof Commission Rates</a>
            </mat-list-item>

            <mat-list-item>
              <ng-container>
                <a mat-line class="font-custom" (click)="onClickRoofOverrides()">Roofing Overrides
                  <ng-container *ngIf="!isRoofOverrides; else showRoofOverrides">
                    <mat-icon mat-list-icon class="icon-left-roof-override" (click)="downArrowRoofOverrides()">add</mat-icon>
                  </ng-container>
                  <ng-template #showRoofOverrides>
                    <mat-icon mat-list-icon class="icon-left-roof-override" (click)="upArrowRoofOverrides()">remove</mat-icon>
                  </ng-template>
                </a>
              </ng-container>
            </mat-list-item>
            
            <ng-container *ngIf="isRoofOverrides">
              <div class="nested-sub-menu">
                <mat-list-item>
                  <a mat-line [routerLink]="['/ui/commissions/ratetables/employeeoverrideroofrates']">Employee Override Rates</a>
                </mat-list-item>
  
                <mat-list-item>
                  <a mat-line [routerLink]="['/ui/commissions/ratetables/employeerole']">Employee Role</a>
                </mat-list-item>

                <mat-list-item>
                  <a mat-line [routerLink]="['/ui/commissions/ratetables/roofingmonthlyoverride']">Roofing Monthly Override</a>
                </mat-list-item>
              </div>
            </ng-container>

            <mat-list-item>
              <a mat-line [routerLink]="['/ui/commissions/ratetables/splitcommissionrates']">Split Commission Rates</a>
            </mat-list-item>  

            <mat-list-item>
              <a mat-line [routerLink]="['/ui/commissions/ratetables/territoryrate']">Territory
                Rates</a>
            </mat-list-item>  

            <mat-list-item>
              <a mat-line [routerLink]="['/ui/commissions/ratetables/tieredoverage']">Tiered Overage</a>
            </mat-list-item>
          </div>
        
      </ng-container>

      <mat-list-item class="sidebar-custom"
        *ngIf="apiService.checkPermission('ViewFinanceDashboard') || apiService.checkPermission('ViewLegalDashboard') || apiService.checkPermission('ViewSalesAdminDashboard')">
        <ng-container>
          <span class="material-icons" data-toggle="tooltip" data-placement="right" title="Dashboards"
            (click)="iconClickDashboard()">
            analytics
          </span>

          <a mat-line (click)="dashboardTextDashboard()">Dashboards
            <ng-container *ngIf="!subDashboardTables; else subDashboardTablesArrow">
              <mat-icon mat-list-icon (click)="downArrowDashboard()">add</mat-icon>
            </ng-container>
            <ng-template #subDashboardTablesArrow>
              <mat-icon mat-list-icon (click)="upArrowDashboard()">remove</mat-icon>
            </ng-template>
          </a>

        </ng-container>
      </mat-list-item>
      <ng-container *ngIf=" subDashboardTables">
        <div class="sub-menu">
          <mat-list-item *ngIf="apiService.checkPermission('ViewFinanceDashboard')">
            <a mat-line class="sidebar-custom-inner" *ngIf="isExpanded"
              [routerLink]="['/ui/dashboard/finance']">Finance</a>
          </mat-list-item>
          <mat-list-item *ngIf="apiService.checkPermission('ViewLegalDashboard')">
            <a mat-line class="sidebar-custom-inner" *ngIf="isExpanded" [routerLink]="['/ui/dashboard/legal']">Plans</a>
          </mat-list-item>
          <mat-list-item *ngIf="apiService.checkPermission('ViewSalesAdminDashboard')">
            <a mat-line class="sidebar-custom-inner" *ngIf="isExpanded" [routerLink]="['/ui/dashboard/sales']">Sales</a>
          </mat-list-item>
          <mat-list-item *ngIf="apiService.checkPermission('ViewBusinessDashboard')">
            <a mat-line class="sidebar-custom-inner" *ngIf="isExpanded" [routerLink]="['/business']">Business</a>
          </mat-list-item>
        </div>
      </ng-container>
    </mat-nav-list>
  </mat-drawer>

  <!-- <div class="example-sidenav-content">
    You cards and screen Contents goes here..
    Will be pushed towards right on expanding side navbar.
  </div> -->
  <mat-drawer-content *ngIf="checkShowMenu()" [className]="apiService.getRole() != 'Sales Reps' ? 'mat-drawer-content' : 'mat-drawer-content-salesRep'">
    <app-header></app-header>
    <router-outlet></router-outlet>
  </mat-drawer-content>
</mat-drawer-container>
