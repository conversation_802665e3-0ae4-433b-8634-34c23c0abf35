import { Component, OnInit, Input, Output, EventEmitter } from '@angular/core';
import { ApiService } from "../../../services/api.service";
import { ToastrService } from 'ngx-toastr';
import { MatDialog } from '@angular/material/dialog';
import { ContactPlanExclusion } from 'src/app/model/contact-plan-exclusion.model';

@Component({
  selector: 'app-show-rule-type',
  templateUrl: './show-rule-type.component.html',
  styleUrls: ['./show-rule-type.component.css']
})
export class ShowRuleTypeComponent implements OnInit {
  @Input() ruleName: string;
  @Input() contactPlanId: number;
  activeInd: boolean = false;
  existingActiveInd: boolean = false;
  viewRuleType: boolean = true;
  isInclusion: boolean = false;

  constructor(private apiService: ApiService, private toastMsg: ToastrService, private dialog: MatDialog) { }

  ngOnInit() {
    this.getContactPlanExclusion();
    this.getContactPlanInclusion();
  }

  getContactPlanExclusion() {
    this.apiService.get(`rule/ContactPlanExclusionForRuleType/${this.contactPlanId}/${this.ruleName}`)
      .subscribe(
        data => {
          if (data.statusCode === "200" || data.statusCode === "201") {
            if (data.result != null) {
              this.activeInd = data.result.activeInd;
              this.existingActiveInd = data.result.activeInd;
            } else {
              this.activeInd = false;
              this.existingActiveInd = false;
            }
          }
        }
        ,
        (err: any) => {
          this.toastMsg.error(err.message, "Server Error!");
        }
      );
  }

  getContactPlanInclusion() {
    this.apiService.get(`ContactPlanInclusion/IsInclusionRuleType/${this.contactPlanId}/${this.ruleName}`)
    .subscribe(
      data => {
        if (data) {
           this.isInclusion = data.result;
        }
      },(err: any) => {
        this.toastMsg.error(err || err.message, "Server Error!");
      }
    )
  }

  SaveActiveInd() {

    let cpeForm = <ContactPlanExclusion>{
      contactPlanId: this.contactPlanId,
      activeInd: this.activeInd,
      commissionRuleTypeName: this.ruleName
    }

    this.apiService.post('rule/UpdateContactPlanExclusion', cpeForm)
      .subscribe(data => {
        if (data && data.statusCode == "201") {
          this.existingActiveInd = this.activeInd;
          if (this.activeInd) {
            this.toastMsg.success("Commission rule type has been excluded from the plan successfully.");
          } else {
            this.toastMsg.success("Commission rule type has been included into the plan successfully.");
          }
        }
      }, (err: any) => {
        this.toastMsg.error("Server Error!", "Error");
        return;
      });
  }
}
