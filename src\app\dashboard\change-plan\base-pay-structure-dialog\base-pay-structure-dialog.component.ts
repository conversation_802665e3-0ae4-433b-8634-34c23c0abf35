import { Component, OnInit, Inject, ViewChild } from '@angular/core';
import { IRule } from 'src/app/model/rule.model';
import { AddOnRule } from '../employee-incentive-dialog/employee-incentive-dialog.component';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { ApiService } from 'src/app/services/api.service';
import { ToastrService } from 'ngx-toastr';
import { IRulePrompt } from 'src/app/model/rule-prompt.model';
import { IPlanDetail } from 'src/app/model/plan.model';
import { IPrompt } from 'src/app/model/prompt.model';
import { BasePayStructurePromptComponent } from '../base-pay-structure-prompt/base-pay-structure-prompt.component';

@Component({
  selector: 'app-base-pay-structure-dialog',
  templateUrl: './base-pay-structure-dialog.component.html',
  styleUrls: ['./base-pay-structure-dialog.component.css']
})
export class BasePayStructureDialogComponent implements OnInit {
  @ViewChild(BasePayStructurePromptComponent) basePayStructurePrompt: BasePayStructurePromptComponent;
  selectedRuleId: number = null;
  selectedBasePayRuleId: number = null;
  prompts: IRule;
  promptsValues: object = {};
  addOn: AddOnRule;
  
  constructor(public dialogRef: MatDialogRef<BasePayStructureDialogComponent>, @Inject(MAT_DIALOG_DATA) public data: IBasePayStructureDialogData, private apiService: ApiService, private toastMsg: ToastrService) { }

  ngOnInit() {
    var bpRules = this.data.basePayRules.map(x => x.ruleId.toString());
    var duplicates = bpRules.filter((item, index) => bpRules.indexOf(item) !== index);
    if (duplicates && duplicates.length > 0) {
      this.data.basePayRules = this.data.basePayRules.filter(x => x.isInclusion);
    }
    this.selectedBasePayRuleId = this.data.basePayRules.length == 1 ? this.data.basePayRules[0].ruleId : null;
  }

  onSelect() {
    // console.log(this.selectedRuleId);
    this.prompts = null;
    this.addOn = null;
    var rulePrompts = this.data.basePayStructureRules[0].rules.filter(p => p.ruleId == this.selectedRuleId)[0];
    // console.log(rulePrompts);
    if (rulePrompts) {
      this.prompts = rulePrompts;
      this.addOn = <AddOnRule>rulePrompts;
      this.addOn.promptValues = {};
      this.addOn.basePayRuleId = this.selectedBasePayRuleId;
      this.addOn.basePayRuleName = this.data.basePayRules.find(x => x.ruleId == this.selectedBasePayRuleId).ruleName
    }
  }

  onRulePromptChange(prompt: IRulePrompt) {
    this.addOn.promptValues[prompt.commissionRuleId] = prompt;
    console.log("Prompts:", this.promptsValues);
  }

  checkAllPromptsEntered() {
    if (this.basePayStructurePrompt && this.basePayStructurePrompt.ruleItems && this.basePayStructurePrompt.ruleItems.find(r => r.ruleItemInputs.find(rii => rii.columnValue == null || rii.columnValue == "") != null) != null) {
      return false;
    } else if (!this.basePayStructurePrompt || !this.basePayStructurePrompt.ruleItems) {
      return false;
    } else {
      return true;
    }
  }

  checkValidDates() {
    var valid = true;

    this.basePayStructurePrompt.ruleItems.forEach((item, i) => {
      var next = this.basePayStructurePrompt.ruleItems[i + 1];

      if (next == null || next.ruleItemInputs == null || next.ruleItemInputs.find(x => x.columnName == "Start_Date") == null) return;

      if (item.ruleItemInputs && item.ruleItemInputs.find(x => x.columnName == "Start_Date") != null && item.ruleItemInputs.find(x => x.columnName == "Start_Date").columnValue >= next.ruleItemInputs.find(x => x.columnName == "Start_Date").columnValue) {
        valid = false;
      }
    });

    return valid;
  }

  checkBasePayRuleMappingProvided() {
    return this.selectedBasePayRuleId && this.selectedBasePayRuleId > 0;
  }
}

export interface IBasePayStructureDialogData {
  basePayStructureRules: IPlanDetail[];
  basePayRules: any[];
  prompts: IPrompt[];
}