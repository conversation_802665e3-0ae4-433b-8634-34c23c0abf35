import { Component, Output, EventEmitter } from '@angular/core';
import { BreakpointObserver, Breakpoints } from '@angular/cdk/layout';
import { Observable } from 'rxjs';
import { map, shareReplay } from 'rxjs/operators';
import { Router, NavigationEnd } from "@angular/router";
import { ApiService } from '../services/api.service';
import { TooltipPosition } from '@angular/material/tooltip';


@Component({
  selector: 'app-new-navigation',
  templateUrl: './new-navigation.component.html',
  styleUrls: ['./new-navigation.component.css']
})
export class NewNavigationComponent {

  isHandset$: Observable<boolean> = this.breakpointObserver.observe(Breakpoints.Handset)
    .pipe(
      map(result => result.matches),
      shareReplay()
    );
  isExpanded: boolean = false;
  subRateTables: boolean;
  subPaymentTables: boolean;
  subReportTables: boolean;
  subDashboardTables: boolean;
  submaintananceTables: boolean;
  isRoofOverrides:boolean;
  isBatteryOverrides:boolean;
  

  constructor(public apiService: ApiService, private router: Router, private breakpointObserver: BreakpointObserver) { }



  toggleRateTableDropdown() {
    this.isExpanded = !this.subRateTables;
    this.subRateTables = !this.subRateTables;
  }
  togglePaymentDropdown() {

    this.isExpanded = !this.subPaymentTables;
    this.subPaymentTables = !this.subPaymentTables;
  }
  toggleReportDropdown() {
    this.isExpanded = !this.subReportTables;
    this.subReportTables = !this.subReportTables;
  }

  downArrowMaintanance() {
    this.isExpanded = true;
    this.subPaymentTables = true;
  }
  downArrowPayment() {
    this.isExpanded = true;
    this.subPaymentTables = true;
  }
  downArrowReport() {
    this.isExpanded = true;
    this.subReportTables = true;
  }

 
  upArrowPayment() {
    this.subPaymentTables = false;
  }
  upArrowMaintanance() {
    this.submaintananceTables = false;
  }

  upArrowReport() {
    this.subReportTables = false;
  }
  

  iconClickPayment() {
    if (!this.isExpanded) {
      this.isExpanded = true;
      this.subPaymentTables = true;
    } else {
      this.subPaymentTables = !this.subPaymentTables;
    }
  }
  iconClickmaintanence() {
    if (!this.isExpanded) {
      this.isExpanded = true;
      this.submaintananceTables = true;
    } else {
      this.submaintananceTables = !this.submaintananceTables;
    }
  }
  iconClickReport() {
    if (!this.isExpanded) {
      this.isExpanded = true;
      this.subReportTables = true;
    } else {
      this.subReportTables = !this.subReportTables;
    }
  }

  
  paymentTextPayment() {
    if (!this.isExpanded) {
      this.isExpanded = true;
      this.subPaymentTables = true;
    }
    else {
      this.subPaymentTables = !this.subPaymentTables;
    }
  }
  maintenanceTextMaintanance() {
    if (!this.isExpanded) {
      this.isExpanded = true;
      this.submaintananceTables = true;
    }
    else {
      this.submaintananceTables = !this.submaintananceTables;
    }
  }
  paymentTextReport() {
    if (!this.isExpanded) {
      this.isExpanded = true;
      this.subReportTables = true;
    }
    else {
      this.subReportTables = !this.subReportTables;
    }
  }

  downArrowRate() {
    this.isExpanded = true;
    this.subRateTables = true;
  }
  upArrowRate() {
    this.subRateTables = false;
  }
  downArrowRoofOverrides() {
    this.isRoofOverrides = true;
  }
  upArrowRoofOverrides(){
    this.isRoofOverrides = false;
  }
  downArrowBatteryOverrides() {
    this.isBatteryOverrides = true;
  }
  upArrowBatteryOverrides(){
    this.isBatteryOverrides = false;
  }
  onClickRoofOverrides(){
 if (!this.isExpanded) {
      this.isExpanded = true;
      this.isRoofOverrides = true;
    } else {
      this.isRoofOverrides = !this.isRoofOverrides;
    }
  }
  onClickBatteryOverrides(){
 if (!this.isExpanded) {
      this.isExpanded = true;
      this.isBatteryOverrides = true;
    } else {
      this.isBatteryOverrides = !this.isBatteryOverrides;
    }
  }

  iconClickRate() {
    if (!this.isExpanded) {
      this.isExpanded = true;
      this.subRateTables = true;
    } else {
      this.subRateTables = !this.subRateTables;
    }
  }
  paymentTextRate() {
    if (!this.isExpanded) {
      this.isExpanded = true;
      this.subRateTables = true;
    } else {
      this.subRateTables = !this.subRateTables;
    }
  }

  downArrowDashboard() {
    this.isExpanded = true;
    this.subDashboardTables = true;
  }

  upArrowDashboard() {
    this.subDashboardTables = false;
  }

  iconClickDashboard() {
    if (!this.isExpanded) {
      this.isExpanded = true;
      this.subDashboardTables = true;
    } else {
      this.subDashboardTables = !this.subDashboardTables;
    }
  }
  dashboardTextDashboard() {
    if (!this.isExpanded) {
      this.isExpanded = true;
      this.subDashboardTables = true;
    }
    else {
      this.subDashboardTables = !this.subDashboardTables;
    }
  }

  menuToggle() {
    if (!this.isExpanded) {
      this.isExpanded = true;
    } else {
      this.isExpanded = false;

    }


  }


  leftArrowClick() {
    this.isExpanded = false;
    this.subRateTables = false;
    this.subPaymentTables = false;
    this.submaintananceTables = false;
    this.subDashboardTables = false;
  }
  rightArrowClick() {
    this.isExpanded = true;
  }

  checkShowMenu() {
    return this.apiService.showMenu();
  }
}
