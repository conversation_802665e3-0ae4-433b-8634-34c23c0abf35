import { CommonModule, DatePipe } from '@angular/common';
import { Component, Inject, OnInit } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';

@Component({
  selector: 'app-grid-filter',
  standalone: true,
  imports: [CommonModule,FormsModule],
  templateUrl: './grid-filter.component.html',
  styleUrls: ['./grid-filter.component.css']
})
export class GridFilterComponent implements OnInit {
  filterValue:string= "";
  filterOperator:string =this.data.data3.dataType !='Date' ? "contains":"equals";
  inputDate:string;
  constructor(public dialogRef: MatDialogRef<GridFilterComponent>,
    @Inject(MAT_DIALOG_DATA) public data: any,private datePipe: DatePipe) { }

  ngOnInit() {
    let setValue = this.data.data1.filter(s=> s.columnId === this.data.data2);
    if(setValue && setValue.length > 0){
      if(this.data.data3.dataType !='Date'){
        this.filterValue = setValue[0].filterValue;
      }
      else{
        this.inputDate = this.datePipe.transform(setValue[0].filterValue, 'yyyy-MM-dd') || '';
        this.filterValue = this.datePipe.transform(setValue[0].filterValue, 'MM dd, yyyy') || '';
      }
      this.filterOperator = setValue[0].filterOperator;
    }
    this.dialogRef.backdropClick().subscribe(() => {
      this.getResult();
    });
  }
  OnChangeFilterValue(event:KeyboardEvent){
    this.getResult();
  }
  getResult(){
    const resultData = {'filterOperator':this.filterOperator, 'filterValue':this.filterValue,'columnId':this.data.data2};
    this.dialogRef.close(resultData);
  }
  onChangeDate(event:any){
    this.filterValue = this.datePipe.transform(this.inputDate, 'MMM d, yyyy') || '';
    this.getResult();
  }
}
