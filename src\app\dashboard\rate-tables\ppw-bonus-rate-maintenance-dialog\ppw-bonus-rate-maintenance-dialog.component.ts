import { Component, OnInit, Inject } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { ApiService } from '../../../services/api.service';
import { ToastrService } from 'ngx-toastr';

@Component({
  selector: 'app-ppw-bonus-rate-maintenance-dialog',
  templateUrl: './ppw-bonus-rate-maintenance-dialog.component.html',
  styleUrls: ['./ppw-bonus-rate-maintenance-dialog.component.css']
})
export class PpwBonusRateMaintenanceDialogComponent implements OnInit {
  ppwBonusRateGroup: Element[] = [];
  ppwBonusRateSelectedGroup: any;
  
  constructor(public dialogRef: MatDialogRef<PpwBonusRateMaintenanceDialogComponent>,
    private apiService: ApiService, private toastMsg: ToastrService, @Inject(MAT_DIALOG_DATA) public data: any) { }


  ngOnInit() {
    this.ppwBonusRateGroup = this.data.ppwBonusRate; 
    this.ppwBonusRateGroup.sort((a, b) => {
      return <any>new Date(b[0].effectiveStartDate) - <any>new Date(a[0].effectiveStartDate);
    });
  }

  groupClick(group: any) {
    this.ppwBonusRateSelectedGroup = group;
  }

}

export interface Element {
  effectiveStartDate: string,
  effectiveEndDate: string
}

