import { Component, OnInit, ViewChild } from '@angular/core';
import { UntypedFormBuilder, FormGroup, Validators } from "@angular/forms";
import { ApiService } from '../../services/api.service';
import { Router, ActivatedRoute } from '@angular/router';
import { InfoBox } from './model/infobox.model';
import { ToastrService } from 'ngx-toastr';
// import { stringify } from 'querystring';
import { MatSort } from '@angular/material/sort';
import { MatTableDataSource } from '@angular/material/table';
import { DataSource } from '@angular/cdk/table';
import { MatPaginator } from '@angular/material/paginator';
import { TableFilterPipe } from '../../pipe/table-filter.pipe';
import { DatePipe, PercentPipe, CurrencyPipe } from '@angular/common';
import { SidenavService } from '../../services/sidenav.service';
import { SharedSidebarService } from '../../shared/sidebar-icon';
import { MatDrawer } from '@angular/material/sidenav';
import { ExportService } from 'src/app/services/export.service';

@Component({
  selector: 'app-financeAdmin-dashboard',
  templateUrl: './financeAdmin-dashboard.component.html',
  styleUrls: ['./financeAdmin-dashboard.component.css']
})
export class FinanceAdminDashboardComponent implements OnInit {
  @ViewChild(MatDrawer) public sidenav: MatDrawer;
  @ViewChild('table1', { read: MatSort, static: true }) sort1: MatSort;
  @ViewChild('table2', { read: MatSort, static: true }) sort2: MatSort;
  @ViewChild('table3', { read: MatSort, static: true }) sort3: MatSort;
  @ViewChild('table4', { read: MatSort, static: true }) sort4: MatSort;

  @ViewChild('paginator1', { static: true }) paginator1: MatPaginator;
  @ViewChild('paginator2', { static: true }) paginator2: MatPaginator;
  @ViewChild('paginator3', { static: true }) paginator3: MatPaginator;
  @ViewChild('paginator4', { static: true }) paginator4: MatPaginator;
  infoBox: InfoBox = {
    blueInfo: { name: "", number: 0 },
    greenInfo: { name: "", number: 0 },
    greyInfo: { name: "", number: 0 }
  };
  tabPosition: number = 0;
  tabName: string = "Payments";
  getPayments: any;
  getActiveSalesReps: any;
  getOpenIncentiveBonuses: any;
  originalDataSourcePayments: any;
  originalDataSourceActiveSalesReps: any;
  originalDataSourceOpenIncentiveBonuses: any;
  dataSourcePayments: MatTableDataSource<ElementPayment>;
  dataSourceActiveSalesReps: MatTableDataSource<ElementAlt>;
  dataSourceOpenIncentiveBonuses: MatTableDataSource<Element>;
  displayedColumnsPayments = [];
  displayedColumnsActiveSalesReps = [];
  displayedColumnsOpenIncentiveBonuses = [];
  itempPerPage: number = 5;
  totalNumberOfPages: number = 2;
  totalNumberofItems: number = 100;
  pageIndex: number = 1;
  pageNumber: number = 1;
  isHovered: boolean = false;
  sortCoulmn: string ='date_Processed';
  sortOrder: string ='desc';
  sortField: string = 'Date_Processed';  
  paymentsDownloadData:any[] = [];

  columnNamesPayments = [{
    id: "Contact_Name",
    value: "Contact Name",
    isHovered:false
  }, {
    id: "Opportunity_Name",
    value: "Opportunity Name",
    isHovered:false
  }, {
    id: "Commission_Rule_Name",
    value: "Rule Name",
    isHovered:false
  }, {
    id: "Payment_Type_Name",
    value: "Payment Type Name",
    isHovered:false
  }, {
    id: "Payment_Status_Name",
    value: "Payment Status",
    isHovered:false
  }, {
    id: "Date_Processed",
    value: "Date Processed",
    isHovered:false
  },
  {
    id: "System_Size_kWdc",
    value: "System Size kWdc",
    isHovered:false
  },
  {
    id: "Amount",
    value: "Amount",
    isHovered:false
  }];

  columnNamesActive = [{
    id: "Contact_Name",
    value: "Contact Name"
  },
  {
    id: "Sales_Office",
    value: "Sales Office"
  },
  {
    id: "Sales_Division",
    value: "Sales Division"
  },
  {
    id: "Start_Date",
    value: "Start Date"
  },
  {
    id: "Contact_Phone",
    value: "Contact Phone"
  },
  {
    id: "Contact_Email",
    value: "Contact Email"
  }];
  columnNamesOpen = [{
    id: "Contact_Name",
    value: "Contact Name"
  },
  {
    id: "Sales_Division",
    value: "Sales Division"
  },
  {
    id: "Commission_Rule_Name",
    value: "Commission Rule Name"
  },
  {
    id: "Number_Of_Bonuses",
    value: "Number of Bonuses"
  },
  {
    id: "Number_Of_Remaining",
    value: "Number of Remaining"
  }];


  constructor(public apiService: ApiService, private toastMsg: ToastrService, private router: Router, private activatedRoute: ActivatedRoute, private formBuilder: UntypedFormBuilder, private pipe: TableFilterPipe,
    private datePipe: DatePipe, private percentPipe: PercentPipe, private currencyPipe: CurrencyPipe, private sidenavService: SidenavService, private sharedSidebarService: SharedSidebarService, private exportService: ExportService) {
    //this.sharedSidebarService.sidebarValue = "open";
    this.apiService.hideLoader = true;
    localStorage.setItem('href', window.location.href);

    this.infoBox = {
      blueInfo: { name: "", number: 0 },
      greenInfo: { name: "", number: 0 },
      greyInfo: { name: "", number: 0 }
    };
  }

  ngOnInit() {
    if (!this.apiService.checkPermission('ViewFinanceDashboard')) {
      this.router.navigate(['/unauthorized']);
    }
    // this.sharedSidebarService.updateComp2Val("open");
    // this.sharedSidebarService.updateComp1Val("close");
    this.getData();
    this.getStaticBoxes();
  }

  getStaticBoxes() {
    this.apiService.get('Dashboard/GetPaymentsCount')
      .subscribe(data => {
        this.infoBox.blueInfo = { name: "Payments", number: data };
      }, (err: any) => {
        this.toastMsg.error(err.message, 'Server Error!');
      });
    this.apiService.get('Dashboard/GetActiveSalesRepCount')
      .subscribe(data => {
        this.infoBox.greenInfo = { name: "Active Sales Reps", number: data };
      }, (err: any) => {
        this.toastMsg.error(err.message, 'Server Error!');
      });
    this.apiService.get('Dashboard/GetOpenIncentiveBonusCount')
      .subscribe(data => {
        this.infoBox.greyInfo = { name: "Sales Reps With Open Incentive Bonuses", number: data };
      }, (err: any) => {
        this.toastMsg.error(err.message, 'Server Error!');
      });
  }
  getData() {
    this.getPaymentsApi();

    this.apiService.get('Dashboard/GetActiveSalesReps')
      .subscribe(data => {
        console.log("Active", data);
        this.getActiveSalesReps = data.result;
        this.displayedColumnsActiveSalesReps = this.columnNamesActive.map(x => x.id);
        this.createTableActiveSalesReps();
      }, (err: any) => {
        this.toastMsg.error(err.message, 'Server Error!');
      });
    this.apiService.get('Dashboard/GetOpenIncentiveBonus')
      .subscribe(data => {
        console.log("Open", data);
        this.getOpenIncentiveBonuses = data.result;
        this.displayedColumnsOpenIncentiveBonuses = this.columnNamesOpen.map(x => x.id);
        this.createTableOpenIncentiveBonuses();
      }, (err: any) => {
        this.toastMsg.error(err.message, 'Server Error!');
      });
  }
  getexportTable(tab:string){
    this.apiService.get('Dashboard/GetDownloadPayments').subscribe(data => {
        this.paymentsDownloadData = data.result;  
        this.createDownloadTable();   
        this.exportTable(tab);         
      })
  }
  createDownloadTable(){
    let tableArr: ElementPayment[] = [];
    for (let i: number = 0; i <= this.paymentsDownloadData.length - 1; i++) {
      let currentRow = this.paymentsDownloadData[i];
      tableArr.push({
        Contact_Id: currentRow.contact_Id, Contact_Name: currentRow.contact_Name, Opportunity_Name: currentRow.opportunity_Name, Opportunity_Id: currentRow.opportunity_Id, Commission_Rule_Name: currentRow.commission_Rule_Name, Commission_Rule_Id: currentRow.commission_Rule_Id, Payment_Type_Name: currentRow.payment_Type_Name, Payment_Status_Name: currentRow.payment_Status_Name,
        Date_Processed: this.datePipe.transform(currentRow.date_Processed), System_Size_kWdc: currentRow.system_Size_kWdc, Amount: this.currencyPipe.transform(currentRow.amount)
      });
    }
    this.paymentsDownloadData = tableArr;
    
  }
  createTable() {
    let tableArr: ElementPayment[] = [];
    for (let i: number = 0; i <= this.getPayments.length - 1; i++) {
      let currentRow = this.getPayments[i];
      tableArr.push({
        Contact_Id: currentRow.contact_Id, Contact_Name: currentRow.contact_Name, Opportunity_Name: currentRow.opportunity_Name, Opportunity_Id: currentRow.opportunity_Id, Commission_Rule_Name: currentRow.commission_Rule_Name, Commission_Rule_Id: currentRow.commission_Rule_Id, Payment_Type_Name: currentRow.payment_Type_Name, Payment_Status_Name: currentRow.payment_Status_Name,
        Date_Processed: this.datePipe.transform(currentRow.date_Processed), System_Size_kWdc: currentRow.system_Size_kWdc, Amount: this.currencyPipe.transform(currentRow.amount)
      });
    }
    this.dataSourcePayments = new MatTableDataSource(tableArr);
    this.originalDataSourcePayments = tableArr;
    this.dataSourcePayments.sort = this.sort1;

  }
  createTableActiveSalesReps() {
    let tableArr: ElementAlt[] = [];
    for (let i: number = 0; i <= this.getActiveSalesReps.length - 1; i++) {
      let currentRow = this.getActiveSalesReps[i];
      tableArr.push({
        Contact_Id: currentRow.contact_Id, Contact_Name: currentRow.contact_Name, Sales_Office: currentRow.sales_Office, Sales_Division: currentRow.sales_Division,
        Start_Date: this.datePipe.transform(currentRow.start_Date), Contact_Phone: currentRow.contact_Phone, Contact_Email: currentRow.contact_Email
      });
    }
    this.dataSourceActiveSalesReps = new MatTableDataSource(tableArr);
    this.originalDataSourceActiveSalesReps = tableArr;
    this.dataSourceActiveSalesReps.sort = this.sort2;
    this.dataSourceActiveSalesReps.paginator = this.paginator2;

  }
  createTableOpenIncentiveBonuses() {
    let tableArr: Element[] = [];
    for (let i: number = 0; i <= this.getOpenIncentiveBonuses.length - 1; i++) {
      let currentRow = this.getOpenIncentiveBonuses[i];
      tableArr.push({
        Contact_Id: currentRow.contact_Id, Contact_Name: currentRow.contact_Name, Sales_Division: currentRow.sales_Division, Commission_Rule_Name: currentRow.commission_Rule_Name, Commission_Rule_Id: currentRow.commission_Rule_Id,
        Number_Of_Bonuses: currentRow.number_Of_Bonuses, Number_Of_Remaining: currentRow.number_Of_Remaining
      });
    }
    this.dataSourceOpenIncentiveBonuses = new MatTableDataSource(tableArr);
    this.originalDataSourceOpenIncentiveBonuses = tableArr;
    this.dataSourceOpenIncentiveBonuses.sort = this.sort3;
    this.dataSourceOpenIncentiveBonuses.paginator = this.paginator3;

  }

  getPaymentsApi(){
    this.apiService.get('Dashboard/GetPayments?pageNumber='+this.pageNumber+'&pageSize='+this.itempPerPage+'&sortfield='+this.sortCoulmn+'&sortorder='+this.sortOrder)
      .subscribe(data => {
        console.log("Payments", data);
        this.getPayments = data.result.data;
        this.pageNumber = data.result.pageNumber;
        this.itempPerPage = data.result.pageSize;
        this.totalNumberofItems = data.result.totalCount;
        this.displayedColumnsPayments = this.columnNamesPayments.map(x => x.id);
        this.createTable();
      }, (err: any) => {
        this.toastMsg.error(err.message, 'Server Error!');
      });
  }

  pageEvent(event:any){    
    this.pageNumber=event.pageIndex+1;
    this.itempPerPage=event.pageSize;
    this.getPaymentsApi();      
  }

  onSort(item:string){
    this.sortField = item;
    switch(item){
      case 'Contact_Name':{
        this.sortCoulmn='contact_Name';
        break;
      }
      case 'Opportunity_Name':{
        this.sortCoulmn='opportunity_Name';
        break;
      }
      case 'Commission_Rule_Name':{
        this.sortCoulmn='commission_Rule_Name';
        break;
      }
      case 'Payment_Type_Name':{
        this.sortCoulmn='payment_Type_Name';
        break;
      }      
      case 'Payment_Status_Name':{
        this.sortCoulmn='payment_Status_Name';
        break;
      }
      case 'Date_Processed':{
        this.sortCoulmn='date_Processed';
        break;
      }
      case 'System_Size_kWdc':{
        this.sortCoulmn='system_Size_kWdc';
        break;
      }
      case 'Amount':{
        this.sortCoulmn='amount';
        break;
      }
    }  
    if(this.sortOrder === 'asc'){
      this.sortOrder = 'desc';
    }else{
      this.sortOrder = 'asc';
    }    
    this.getPaymentsApi();
  }
  moveToSelectedTab(tabName: string) {
    this.tabName = tabName;
    for (let i = 0; i < document.querySelectorAll('.mat-tab-label-content').length; i++) {
      if ((<HTMLElement>document.querySelectorAll('.mat-tab-label-content')[i]).innerText == tabName) {
        (<HTMLElement>document.querySelectorAll('.mat-tab-label')[i]).click();
      }
    }
  }

  exportTable(tabName: string) {
    var data : any[][];
    var headers: string[];
    switch (tabName) {
      case "Payments":
        headers = [];
        data = [];
        this.columnNamesPayments.forEach(x => {
          headers.push(x.value);

        });
        headers = this.columnNamesPayments.map(x => x.value);
        data = this.paymentsDownloadData.map(x => {
          var keys = this.columnNamesPayments.map(y => y.id);
          var values = keys.map(y => {return x[y]});
          return values;
        });
        break;
      
      case "Active Sales Reps":
        headers = this.columnNamesActive.map(x => x.value);
        data = this.dataSourceActiveSalesReps.filteredData.map(x => {
          var keys = this.columnNamesActive.map(y => y.id);
          var values = keys.map(y => {return x[y]});
          return values;
        });
        break;

      case "Open Incentive Bonuses":
        headers = this.columnNamesOpen.map(x => x.value);
        data = this.dataSourceOpenIncentiveBonuses.filteredData.map(x => {
          var keys = this.columnNamesOpen.map(y => y.id);
          var values = keys.map(y => {return x[y]});
          return values;
        });
        break;
    }

    // export
    this.exportService.excel(headers, data, tabName);
  }
}

export interface Element {
  Contact_Id: string,
  Contact_Name: string,
  Sales_Division: string,
  Commission_Rule_Name: string,
  Commission_Rule_Id: string,
  Number_Of_Bonuses: string,
  Number_Of_Remaining: string
}

export interface ElementAlt {
  Contact_Id: string,
  Contact_Name: string,
  Sales_Office: string,
  Sales_Division: string,
  Start_Date: string,
  Contact_Phone: string,
  Contact_Email: string
}

export interface ElementPayment {
  Contact_Id: string,
  Contact_Name: string,
  Opportunity_Name: string,
  Opportunity_Id: string,
  Payment_Type_Name: string,
  Payment_Status_Name: string,
  Date_Processed: string,
  System_Size_kWdc: string,
  Amount: string,
  Commission_Rule_Name: string,
  Commission_Rule_Id: string
}