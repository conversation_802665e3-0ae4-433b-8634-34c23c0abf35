import { Component, OnInit, ViewChild } from '@angular/core';
import { UntypedFormBuilder, UntypedFormGroup, Validators } from "@angular/forms";
import { ApiService } from "../../../services/api.service";
import { ToastrService } from 'ngx-toastr';
import { dateLessThanDate, maxPermitDeductionDate } from '../../../shared/validators';
import {MatSort} from '@angular/material/sort';
import {MatTableDataSource} from '@angular/material/table';
import { DataSource } from '@angular/cdk/table';
import {MatPaginator} from '@angular/material/paginator';
import { TableFilterPipe } from '../../../pipe/table-filter.pipe';
import { DatePipe, PercentPipe, CurrencyPipe } from '@angular/common';
import { PermitDeductionMaintenanceDialogComponent } from '../permit-deduction-maintenance-dialog/permit-deduction-maintenance-dialog.component';
import { MatDialog } from '@angular/material/dialog';

@Component({
  selector: 'app-permit-deduction-maintenance',
  templateUrl: './permit-deduction-maintenance.component.html',
  styleUrls: ['./permit-deduction-maintenance.component.css']
})
export class PermitDeductionMaintenanceComponent implements OnInit {
  allPermitDeductions: any;
  activePermitDeductions: any;
  permitDeductionGroup: any;
  dropdowns: any;
  permitDeductionForm: UntypedFormGroup;
  addInd: boolean = false;
  salesTerritoryDefault: number = 1;
  financePartnerDefault: number = 1;
  purchaseMethodDefault: number = 1;
  utilityCompanyDefault: number = 1;
  minimumPpwDefault: number = 0;
  form: any;
  isReloading :boolean = false ;
  p: number = 1;
  tableArr: Element[] = [];
  permitDeductionRate1; 
   ispermitDeductionRateSelected : boolean = false;
  searchText: string = "";
  public date: Date;
  originalDataSource;
  dataSource;
  displayedColumns = [];
  @ViewChild(MatSort, {static: true}) sort: MatSort;
  @ViewChild(MatPaginator, {static: true}) paginator: MatPaginator;

  columnNames = [{
    id: "salesTerritory",
    value: "Sales Territory"
  },
  {
    id: "financePartner",
    value: "Finance Partner"
  },
  {
    id: "purchaseMethod",
    value: "Purchase Method"
  },
{
  id: "utilityCompany",
  value: "Utility Company"
},
{
  id: "effectiveStartDate",
  value: "Start Date"
},
{
  id: "effectiveEndDate",
  value: "End Date"
},
{
  id: "minimumPpw",
  value: "Minimum PPW"
},
{
  id: "permitDeductionRate",
  value: "Permit Deduction Rate"
}];

  constructor(public apiService: ApiService, private toastMsg: ToastrService, private formBuilder: UntypedFormBuilder, private pipe: TableFilterPipe, 
    private datePipe: DatePipe, private percentPipe: PercentPipe, private currencyPipe: CurrencyPipe, private dialog: MatDialog) {

  }

  ngOnInit() {
    if (!this.apiService.checkPermission('ViewRateTables')) {
      // this.router.navigate(['/ui/dashboard'])
      this.apiService.goBack();
      this.toastMsg.error("Insufficient Permissions. Please make sure your account can access this information.", "Permissions");
    }
    this.getDropdowns();

    this.permitDeductionForm = this.formBuilder.group({
      salesTerritory: [this.salesTerritoryDefault, [Validators.required]],
      financePartner: [this.financePartnerDefault, [Validators.required]],
      purchaseMethod: [this.purchaseMethodDefault, [Validators.required]],
      utilityCompany: [this.utilityCompanyDefault, [Validators.required]],
      effectiveStartDate: ['', [Validators.required]],
      permitDeductionRate: [0, [Validators.required, Validators.max(20)]],
      minimumPpw: [this.minimumPpwDefault, [Validators.required]],
    });

    this.onChanges();
  }

  onChanges() {
    this.permitDeductionForm.valueChanges.subscribe(val => {
      // console.log(this.permitDeductionForm.errors);
    });
  }

  clearDate(date: HTMLInputElement) {
    date.value = "";
    this.date = null;
    this.permitDeductionForm.controls.effectiveStartDate.setValue('');
    event.stopPropagation();
  }
  
  onSubmit() {
    if (!this.permitDeductionForm.invalid) {
      var values = {
        salesTerritoryId: this.permitDeductionForm.controls.salesTerritory.value,
        financePartnerId: this.permitDeductionForm.controls.financePartner.value,
        purchaseMethodId: this.permitDeductionForm.controls.purchaseMethod.value,
        utilityCompanyId: this.permitDeductionForm.controls.utilityCompany.value,
        effectiveStartDate: this.permitDeductionForm.controls.effectiveStartDate.value,
        permitDeductionRate: this.permitDeductionForm.controls.permitDeductionRate.value,
        minimumPpw: this.permitDeductionForm.controls.minimumPpw.value
      }
      // console.log("Body  110 => "+JSON.stringify(body));
  
      var body = {
        newPermitDeduction: values
      }

      this.apiService.post('PermitDeductionMaintenance', body)
        .subscribe(data => {
          this.toastMsg.success('Permit Deduction Successfully Added');
          this.isReloading = true ;
          this.getAllPermitDeductions();
          this.getActivePermitDeductions();
          this.addInd = !this.addInd;
        }, (err: any) => {
          if (err?.err?.message)
            this.toastMsg.error(err.message, "Server Error!");
          if (typeof err === "string") {
            let isJsonObject = false;            
            let parsedErr: any;
            try {
              parsedErr = JSON.parse(err);
              isJsonObject =parsedErr && typeof parsedErr === "object" &&!Array.isArray(parsedErr);
            } catch (e) {
              isJsonObject = false;
            }
            if (isJsonObject) {
              this.toastMsg.error(parsedErr.result, "Error!");
            } else this.toastMsg.error(err, "Error!");
          }
          if (err?.result) {
            this.toastMsg.error(err.result, "Error!");
          }
        });
    }
  }

  getAllPermitDeductions() {
    this.apiService.get('PermitDeductionMaintenance/retrieveall')
      .subscribe(data => {
        this.allPermitDeductions = data;
        if (!this.isReloading) {
          this.permitDeductionForm.setValidators([maxPermitDeductionDate(this.allPermitDeductions)]);
        } else {
          this.permitDeductionForm.clearValidators();
        }
        if (this.permitDeductionGroup) this.getPermitDeductionGroup(this.permitDeductionGroup[0]);
      }, (err: any) => {
        this.toastMsg.error(err.message, 'Server Error!');
      });
  }

  getActivePermitDeductions() {
    this.apiService.get('PermitDeductionMaintenance/retrieveactive')
      .subscribe(data => {
        // console.log('active', data); 
        this.activePermitDeductions = data;
        this.displayedColumns = this.columnNames.map(x => x.id);
        this.createTable();
      }, (err: any) => {
        this.toastMsg.error(err.message, 'Server Error!');
      });
  }

  getDropdowns() {
    this.apiService.get('PermitDeductionMaintenance/dropdowns')
      .subscribe(data => {
        this.dropdowns = data;
        this.getAllPermitDeductions();
        this.getActivePermitDeductions();
      }, (err: any) => {
        this.toastMsg.error(err.message, 'Server Error!');
      });
  }

  getPermitDeductionGroup(permitDeduction: any) {
    var permitDeductions = this.allPermitDeductions.filter(x => x.financePartnerId === permitDeduction.financePartnerId && x.purchaseMethodId === permitDeduction.purchaseMethodId && x.salesTerritoryId === permitDeduction.salesTerritoryId);

    this.permitDeductionGroup = permitDeductions;
  }

  get permitDeductionRate() { return this.permitDeductionForm.get('permitDeductionRate'); }

  rowClick(permitDeduction: any) {
    var permitDeductions = this.allPermitDeductions.filter(x => x.financePartnerId === permitDeduction.financePartnerId && x.purchaseMethodId === permitDeduction.purchaseMethodId && x.salesTerritoryId === permitDeduction.salesTerritoryId && 
      x.utilityCompanyId === permitDeduction.utilityCompanyId && x.minimumPpw === permitDeduction.minimumPpw);
    this.permitDeductionRate1 = permitDeductions;
    const dialogRef = this.dialog.open(PermitDeductionMaintenanceDialogComponent, {
      width: '80%', data: { permitDeductions }
    });
    // console.log("permitDeductionRate1 = >" + JSON.stringify(this.permitDeductionRate1));
    // console.log("this.permitDeductionRate1  = >" + JSON.stringify(this.permitDeductionRate1));

    this.permitDeductionForm.controls['salesTerritory'].setValue(this.permitDeductionRate1[0].salesTerritoryId);
    this.permitDeductionForm.controls['financePartner'].setValue(this.permitDeductionRate1[0].financePartnerId);
    this.permitDeductionForm.controls['purchaseMethod'].setValue(this.permitDeductionRate1[0].purchaseMethodId);
    this.permitDeductionForm.controls['utilityCompany'].setValue(this.permitDeductionRate1[0].utilityCompanyId);
    this.permitDeductionForm.controls['permitDeductionRate'].setValue(this.permitDeductionRate1[0].permitDeductionRate);
   
    dialogRef.afterClosed().subscribe(result => {
      // console.log(result);
    });
  }


  Add(){
    this.permitDeductionRate1 = this.tableArr;
    this.addInd = !this.addInd;
    this.isReloading = true;
    if(!this.isReloading)
    {
     this.permitDeductionForm.setValidators([maxPermitDeductionDate(this.allPermitDeductions)]);
    } else {
      this.permitDeductionForm.clearValidators();
    }
    this.permitDeductionForm.controls['salesTerritory'].setValue(this.permitDeductionRate1[0].salesTerritoryId);
    this.permitDeductionForm.controls['financePartner'].setValue(this.permitDeductionRate1[0].financePartnerId);
    this.permitDeductionForm.controls['purchaseMethod'].setValue(this.permitDeductionRate1[0].purchaseMethodId);
    this.permitDeductionForm.controls['utilityCompany'].setValue(this.permitDeductionRate1[0].utilityCompanyId);
    this.permitDeductionForm.controls['permitDeductionRate'].setValue(this.permitDeductionRate1[0].permitDeductionRate);
    this.permitDeductionForm.controls['minimumPpw'].setValue(this.permitDeductionRate1[0].minimumPpw);
  }

  
  createTable() {
    let tableArr: Element[] = [];
    for(let i:number = 0; i <= this.activePermitDeductions.length - 1; i++) {
      let currentRow = this.activePermitDeductions[i];
      if(i==0)
      {
        this.tableArr[0] =this.activePermitDeductions[0];
        // console.log(" this.tableArr = 301 > "+ JSON.stringify(this.tableArr));
      }
      tableArr.push({financePartner: currentRow.financePartner, effectiveStartDate: this.datePipe.transform(currentRow.effectiveStartDate), effectiveEndDate: this.datePipe.transform(currentRow.effectiveEndDate),
        // permitDeductionRate: this.currencyPipe.transform(currentRow.permitDeductionRate), activeInd: currentRow.activeInd, permitDeductionId: currentRow.permitDeductionId,
        // Dilip Rate table changes
        permitDeductionRate: this.currencyPipe.transform(currentRow.permitDeductionRate,"USD",true,"1.3-3"), activeInd: currentRow.activeInd, permitDeductionId: currentRow.permitDeductionId,
      financePartnerId: currentRow.financePartnerId, purchaseMethod: currentRow.purchaseMethod, purchaseMethodId: currentRow.purchaseMethodId, salesTerritoryId: currentRow.salesTerritoryId,
    salesTerritory: currentRow.salesTerritory, utilityCompany: currentRow.utilityCompany, utilityCompanyId: currentRow.utilityCompanyId, minimumPpw: currentRow.minimumPpw});
    }
    this.dataSource = new MatTableDataSource(tableArr);
    this.originalDataSource = tableArr;
    this.dataSource.sort = this.sort;
    this.dataSource.paginator = this.paginator;
  }

  searchForItem():void {
    let filteredResults: Element[] = [];
    if (this.searchText == '') {
      this.dataSource = new MatTableDataSource(this.originalDataSource);
      this.dataSource.sort = this.sort;
    this.dataSource.paginator = this.paginator;
    } else {
      // filteredResults = this.originalDataSource.filter(option => option.salesTerritory.toLowerCase().includes(this.searchText));
      filteredResults = this.pipe.transform(this.originalDataSource, this.searchText);
      this.dataSource = new MatTableDataSource(filteredResults);
      this.dataSource.sort = this.sort;
      this.dataSource.paginator = this.paginator;
    }
  }
}

export interface Element {
  financePartner: string,
  effectiveStartDate: string,
  effectiveEndDate: string,
  permitDeductionRate: string,
  activeInd: boolean,
  permitDeductionId: number,
  financePartnerId: number,
  purchaseMethod: string,
  purchaseMethodId: number,
  salesTerritoryId: number,
  salesTerritory: string,
  utilityCompany: string,
  utilityCompanyId: number,
  minimumPpw: number
}
