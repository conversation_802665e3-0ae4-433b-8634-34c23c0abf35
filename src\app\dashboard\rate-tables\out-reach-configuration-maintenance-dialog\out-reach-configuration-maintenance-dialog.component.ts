import { Component, OnInit, Inject } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { ApiService } from '../../../services/api.service';
import { ToastrService } from 'ngx-toastr';


@Component({
  selector: 'app-out-reach-configuration-maintenance-dialog',
  templateUrl: './out-reach-configuration-maintenance-dialog.component.html',
  styleUrls: ['./out-reach-configuration-maintenance-dialog.component.css']
})
export class OutReachConfigurationMaintenanceDialogComponent implements OnInit {
  OutreachPayConfigurationTypeGroup: Element[] = [];
  constructor(public dialogRef: MatDialogRef<OutReachConfigurationMaintenanceDialogComponent>,
    private apiService: ApiService, private toastMsg: ToastrService, @Inject(MAT_DIALOG_DATA) public data: any) { }


  ngOnInit() {
    this.OutreachPayConfigurationTypeGroup = this.data.outreachPayConfigurationType;
    this.OutreachPayConfigurationTypeGroup.sort((a, b) => {
      return <any>new Date(b.effectiveStartDate) - <any>new Date(a.effectiveStartDate);
    });
  }

}

export interface Element {
  payAfterThresholdInd: boolean,
  payBasedOn: string,
  configurationAmount: string,
  configurationThreshold: string, 
  effectiveStartDate: string,
  effectiveEndDate: string

}
  