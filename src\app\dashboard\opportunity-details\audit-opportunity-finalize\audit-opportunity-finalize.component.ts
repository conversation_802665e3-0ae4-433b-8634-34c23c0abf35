import { Component, OnInit, Input, SimpleChanges, ViewChild } from '@angular/core';
import { ApiService } from 'src/app/services/api.service';
import { ToastrService } from 'ngx-toastr';
import {MatSort} from '@angular/material/sort';
import {MatTableDataSource} from '@angular/material/table';
import {MatPaginator} from '@angular/material/paginator';
import { CurrencyPipe } from '@angular/common';
import { IAuditOpportunityFinalize } from 'src/app/model/audit-opportunity-finalize.model';

@Component({
    selector: 'app-opportunityaudit',
    templateUrl: './audit-opportunity-finalize.component.html',
    styleUrls: ['./audit-opportunity-finalize.component.css']
})
export class AuditOpportunityFinalizeComponent implements OnInit {
  @Input() opportunityId: number;
  //commissions: ICommission[] = [];
  opportunityAudits: IAuditOpportunityFinalize[] = [];
  p: number = 1;

  originalDataSource;
  dataSource;
  displayedColumns = [];
  @ViewChild(MatSort, {static: true}) sort: MatSort;
  @ViewChild(MatPaginator, {static: true}) paginator: MatPaginator;

  columnNames = [{
    id: "opportunityId",
    value: "Opportunity ID"
  },
  {
    id: "finalized",
    value: "Finalized"
  },
 {
  id: "reasonForChange",
  value: "Reason For Change"
 },
 {
  id: "userCreatedTimestamp",
  value: "User Created Timestamp"
 },
 {
  id: "userCreatedId",
  value: "User Crated ID"
 }];

  constructor(private apiService: ApiService, private toastMsg: ToastrService, private currencyPipe: CurrencyPipe) { }

  ngOnInit() {
    //this.displayedColumns = this.columnNames.map(x => x.id);
    this.getOpportunityAudit(this.opportunityId);
  }

  getOpportunityAudit(opportunityId: number){
    this.apiService.get(`AuditOpportunityFinalize/ByOpportunity/${opportunityId}`)
    .subscribe(data => {
      if (data) {
          this.opportunityAudits = data.result.map(audit => {return <IAuditOpportunityFinalize>audit});
          this.displayedColumns = this.columnNames.map(x => x.id);
          this.createTable();
      }
  }, err => {
    this.toastMsg.error(err.message, "Error!"); 
  })
}

  createTable() {
    let tableArr: Element[] = [];
    for(let i:number = 0; i <= this.opportunityAudits.length - 1; i++) {
      let currentRow = this.opportunityAudits[i];
      tableArr.push({opportunityId: currentRow.opportunityId, finalized: currentRow.finalized, reasonForChange: currentRow.reasonForChange, 
      userCreatedTimestamp: currentRow.userCreatedTimestamp, userCreatedId: currentRow.userCreatedId});
    }
    this.dataSource = new MatTableDataSource(tableArr);
    this.originalDataSource = tableArr;
    this.dataSource.sort = this.sort;
    this.dataSource.paginator = this.paginator;
  }

}
export interface Element {
  opportunityId: number,
  finalized: boolean,
  reasonForChange: string,
  userCreatedTimestamp: Date,
  userCreatedId: string
}
