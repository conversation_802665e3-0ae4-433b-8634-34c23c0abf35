<div class="card">
  <div class="card-header-info">

    <h4 class="card-title">Payment Approvals</h4>


  </div>
  <div class="card-body">
    <div class="row">
      <div class="col-md-12">
        <div class="float-right">
          <button class="btn btn-primary" (click)="filter = !filter"><i class="material-icons">filter_list</i>
            Filter</button>
          <button class="btn btn-primary" (click)="getPaymentsWorksheet()"><i class="material-icons">save_alt</i>
            Download</button>
        </div>
        <div class="form-group  col-sm-3 input-group  float-right pr-0 mr-2">
          <input class="custom-input" type="text" id="searchTextId" [(ngModel)]="searchText"
            (keyup)="applyFilter($event)" name="searchText" placeholder="Search">
          <span class="input-group-icon">
            <i class="fas fa-search"></i>
          </span>
        </div>
      </div>
    </div>
    <ng-container *ngIf="filter">
      <div class="gray-bg row">
        <div class="col-md-12 pt-3 pb-3 gray-bg">
          <div class="row filter-row">
            <div class="form-group col-md-3">
              <label>Payment Status</label>
              <select class="custom-select" [(ngModel)]="selectedStatusId" (change)="onChangeStatus()">
                <ng-container *ngIf="paymentStatuses && paymentStatuses.length > 0">
                  <option *ngFor="let status of paymentStatuses" [value]="status.paymentStatusId">
                    {{status.paymentStatusName}}
                  </option>
                </ng-container>
              </select>
            </div>
            <div class="form-group col-md-3">
              <label>Sales Division</label>
              <select class="custom-select" multiple [(ngModel)]="salesDivision" (change)="onChangeFilter()">
                <option [value]="null">--NO FILTER--</option>
                <option *ngFor="let division of salesDivisions" [value]="division">
                  {{division}}
                </option>
              </select>
            </div>
            <div class="form-group col-md-3">
              <label>Sales Office</label>
              <select class="custom-select" multiple [(ngModel)]="salesOffice" (change)="onChangeFilter()">
                <option [value]="null">--NO FILTER--</option>
                <option *ngFor="let office of salesOffices" [value]="office">
                  {{office}}
                </option>
              </select>
            </div>
            <div class="form-group col-md-3">
              <label>Commission Type</label>
              <select class="custom-select" multiple [(ngModel)]="commissionTypeId" (change)="onChangeFilter()">
                <option [value]="null">--NO FILTER--</option>
                <option *ngFor="let type of commissionTypes" [value]="type.commissionTypeId">
                  {{type.commissionTypeName}}
                </option>
              </select>
            </div>
            <div class="form-group col-md-3">
              <label>Payment Type</label>
              <select class="custom-select" multiple [(ngModel)]="paymentTypeId" (change)="onChangeFilter()">
                <option [value]="null">--NO FILTER--</option>
                <option *ngFor="let type of paymentTypes" [value]="type.paymentTypeId">
                  {{type.paymentTypeName}}
                </option>
              </select>
            </div>

            <div class="form-group col-md-3">
              <label>Stages</label>
              <select class="custom-select" [(ngModel)]="selectedStage" (change)="filterStages()">
                <option [value]=0>--NO FILTER--</option>
                <option *ngFor="let type of stageNames" [value]="type">
                  {{type}} +
                </option>
              </select>
            </div>

            <div class="form-group col-md-3">
              <label>Purchase Method</label>            
              <input type="text" class="custom-input" [(ngModel)]="purchaseMethod" (change)="onChangeFilter()">
            </div>
            <div class="form-group col-md-3">
              <label>Employee Status</label>

              <input type="text" class="custom-input" [(ngModel)]="employeeStatus" (change)="onChangeFilter()">
            </div>    
            <div class="form-group col-md-3">
              <label>Solar Actual Install Start Date - From</label>
              <div class="input-group date-picker">
                  <input #datepickerInput type="date"  class="custom-input" [(ngModel)]="actualInstallStartDateRangeStart"
                  (change)="onChangeFilter()">    
                  <span *ngIf="datepickerInput.value.length > 0" class="mat-icon cal-reset" (click)="this.actualInstallStartDateRangeStart = null; onChangeFilter();"><i class="far fa-calendar-times"></i></span> 
                  <span *ngIf="datepickerInput.value.length <= 0" class="mat-icon cal-open"><i class="far fa-calendar-alt"></i></span> 
              </div>
            </div>
            <div class="form-group col-md-3">
              <label>Solar Actual Install Start Date - To</label>
              <div class="input-group date-picker">
                 <input #ActualStartDate type="date" class="custom-input" [(ngModel)]="actualInstallStartDateRangeEnd"
                  (change)="onChangeFilter()"> 
                  <span *ngIf="ActualStartDate.value.length > 0" class="mat-icon cal-reset"  (click)="this.actualInstallStartDateRangeEnd = null; onChangeFilter();"><i class="far fa-calendar-times"></i></span> 
                  <span *ngIf="ActualStartDate.value.length <= 0" class="mat-icon cal-open"><i class="far fa-calendar-alt"></i></span>
 
              </div>
            </div>            
            <div class="form-group col-md-3">
              <label>Roof Actual Install Start Date - From</label>
              <div class="input-group date-picker">
                 <input #RoofStartDate type="date" class="custom-input" [(ngModel)]="roofInstallStartDateRangeStart"
                  (change)="onChangeFilter()"> 
                  <span *ngIf="RoofStartDate.value.length > 0" class="mat-icon cal-reset"  (click)="this.roofInstallStartDateRangeStart = null; onChangeFilter();"><i class="far fa-calendar-times"></i></span> 
                  <span *ngIf="RoofStartDate.value.length <= 0" class="mat-icon cal-open"><i class="far fa-calendar-alt"></i></span>                 
              </div>
            </div>
            <div class="form-group col-md-3">
              <label>Roof Actual Install Start Date - To</label>
              <div class="input-group date-picker">
                 <input #RoofEndtDate type="date" class="custom-input" [(ngModel)]="roofInstallStartDateRangeEnd"
                  (change)="onChangeFilter()"> 
                  <span *ngIf="RoofEndtDate.value.length > 0" class="mat-icon cal-reset"  (click)="this.roofInstallStartDateRangeEnd = null; onChangeFilter();"><i class="far fa-calendar-times"></i></span> 
                  <span *ngIf="RoofEndtDate.value.length <= 0" class="mat-icon cal-open"><i class="far fa-calendar-alt"></i></span>                 
              </div>
            </div>
            <div class="form-group col-md-3">
              <label>Battery Actual Install Start Date - From</label>
              <div class="input-group date-picker">
                 <input #BatteryStarttDate type="date" class="custom-input" [(ngModel)]="batteryInstallStartDateRangeStart"
                  (change)="onChangeFilter()">  
                  <span *ngIf="BatteryStarttDate.value.length > 0" class="mat-icon cal-reset"  (click)="this.batteryInstallStartDateRangeStart = null; onChangeFilter();"><i class="far fa-calendar-times"></i></span> 
                  <span *ngIf="BatteryStarttDate.value.length <= 0" class="mat-icon cal-open"><i class="far fa-calendar-alt"></i></span>  
              </div>
            </div>
            <div class="form-group col-md-3">
              <label>Battery Actual Install Start Date - To</label>
              <div class="input-group date-picker">
                 <input #BatteryEndDate type="date" class="custom-input" [(ngModel)]="batteryInstallStartDateRangeEnd"
                  (change)="onChangeFilter()">  
                  <span *ngIf="BatteryEndDate.value.length > 0" class="mat-icon cal-reset"  (click)="this.batteryInstallStartDateRangeEnd = null; onChangeFilter();"><i class="far fa-calendar-times"></i></span> 
                  <span *ngIf="BatteryEndDate.value.length <= 0" class="mat-icon cal-open"><i class="far fa-calendar-alt"></i></span>      
              </div>
            </div>
            <div class="form-group col-md-3">
              <label>Date Contract Signed - From</label>
              <div class="input-group date-picker">
                  <input #DateContractSignedGreater type="date" class="custom-input" [(ngModel)]="dateContractSignedRangeStart"
                  (change)="onChangeFilter()">
                  <span *ngIf="DateContractSignedGreater.value.length > 0" class="mat-icon cal-reset"  (click)="this.dateContractSignedRangeStart = null; onChangeFilter();"><i class="far fa-calendar-times"></i></span> 
                  <span *ngIf="DateContractSignedGreater.value.length <= 0" class="mat-icon cal-open"><i class="far fa-calendar-alt"></i></span>
               
              </div>
            </div>
            <div class="form-group col-md-3">
              <label>Date Contract Signed - To</label>
              <div class="input-group date-picker">
                 <input #DaterContractSignedLess type="date" class="custom-input" [(ngModel)]="dateContractSignedRangeEnd"
                  (change)="onChangeFilter()">
                  <span *ngIf="DaterContractSignedLess.value.length > 0" class="mat-icon cal-reset"  (click)="this.dateContractSignedRangeEnd = null; onChangeFilter();"><i class="far fa-calendar-times"></i></span> 
                  <span *ngIf="DaterContractSignedLess.value.length <= 0" class="mat-icon cal-open"><i class="far fa-calendar-alt"></i></span>
 
              </div>
            </div>
            <!-- Payment Created Filter -->
            <div class="form-group col-md-3">
              <label>Payment Created - From</label>
              <div class="input-group date-picker">
                 <input #paymentCreatedRangeStartGreater type="date" class="custom-input" [(ngModel)]="paymentCreatedRangeStart"
                  (change)="onChangeFilter()">
                  <span *ngIf="paymentCreatedRangeStartGreater.value.length > 0" class="mat-icon cal-reset"  (click)="this.paymentCreatedRangeStart = null; onChangeFilter();"><i class="far fa-calendar-times"></i></span> 
                  <span *ngIf="paymentCreatedRangeStartGreater.value.length <= 0" class="mat-icon cal-open"><i class="far fa-calendar-alt"></i></span> 
                
              </div>
            </div>
            <div class="form-group col-md-3">
              <label>Payment Created - To</label>
              <div class="input-group date-picker">
               <input #paymentCreatedRangeEndLess type="date" class="custom-input" [(ngModel)]="paymentCreatedRangeEnd"
                  (change)="onChangeFilter()">
                 
                <span *ngIf="paymentCreatedRangeEndLess.value.length > 0" class="mat-icon cal-reset"  (click)="this.paymentCreatedRangeEnd = null; onChangeFilter();"><i class="far fa-calendar-times"></i></span> 
                <span *ngIf="paymentCreatedRangeEndLess.value.length <= 0" class="mat-icon cal-open"><i class="far fa-calendar-alt"></i></span> 
                 
              </div>
            </div>
            <!-- Payment Due Date Filter -->
            <div class="form-group col-md-3">
              <label>Payment Due Date - From</label>
              <div class="input-group date-picker">
                  <input #paymentDueDateRangeStartGreater type="date" class="custom-input" [(ngModel)]="paymentDueDateRangeStart"
                  (change)="onChangeFilter()">
                 
                <span *ngIf="paymentDueDateRangeStartGreater.value.length > 0" class="mat-icon cal-reset"  (click)="this.paymentDueDateRangeStart = null; onChangeFilter();"><i class="far fa-calendar-times"></i></span> 
                <span *ngIf="paymentDueDateRangeStartGreater.value.length <= 0" class="mat-icon cal-open"><i class="far fa-calendar-alt"></i></span>
                                  
              </div>
            </div>
            <div class="form-group col-md-3">
              <label>Payment Due Date - To</label>
              <div class="input-group date-picker">
                 <input #paymentDueDateRangeEndLess type="date" class="custom-input" [(ngModel)]="paymentDueDateRangeEnd"
                  (change)="onChangeFilter()">
                 
                <span *ngIf="paymentDueDateRangeEndLess.value.length > 0" class="mat-icon cal-reset"  (click)="this.paymentDueDateRangeEnd = null; onChangeFilter();"><i class="far fa-calendar-times"></i></span> 
                <span *ngIf="paymentDueDateRangeEndLess.value.length <= 0" class="mat-icon cal-open"><i class="far fa-calendar-alt"></i></span>
                
              </div>
            </div>
            
            <div class="form-group col-md-3">
              <input type="checkbox" id="exclude0Payment" [(ngModel)]="exclude0Payment" [checked]="exclude0Payment" (change)="onChangeFilter()">
              <label class="form-check-label" style="padding-left: 5px;">Exclude $0 payments?</label>
            </div>

          </div>
        </div>
      </div>
    </ng-container>

    <table mat-table [dataSource]="payments" matSort class="my-table mt-3" style="width: 100%">
      <ng-container matColumnDef="selected">
        <th mat-header-cell *matHeaderCellDef>
          <!-- <section class="checkbox-section"> -->
          <mat-checkbox [(ngModel)]="allSelected" (change)="onBulkSelectionChange()" [disabled]="payments?.filteredData.length === 0">
          </mat-checkbox>
          <!-- </section> -->
        </th>
        <td mat-cell *matCellDef="let element">
          <section class="checkbox-section">
            <mat-checkbox [(ngModel)]="element.selected" (change)="onSelectionChange()">
            </mat-checkbox>
          </section>
        </td>
      </ng-container>

      <ng-container matColumnDef="contactName">
        <th class="mat-column-width" mat-header-cell *matHeaderCellDef mat-sort-header> Contact </th>
        <td data-td-head="Contact" mat-cell *matCellDef="let element" class="hover-approval">
          <a [routerLink]="['/ui/commissions/salesrep', element.contactId ? element.contactId : 0]">{{element.contactName
            ? element.contactName : "-"}} </a>
        </td>
      </ng-container>
      
      <ng-container matColumnDef="opportunityName">
        <th class="mat-column-width" mat-header-cell *matHeaderCellDef mat-sort-header> Opportunity </th>
        <td data-td-head="Opportunity" mat-cell *matCellDef="let element" class="hover-approval">
          <a [routerLink]="['/ui/commissions/opportunitydetails', element.opportunityId ? element.opportunityId : 0]">{{element.opportunityName
            ? element.opportunityName : "-"}} </a>
        </td>
      </ng-container>

      <ng-container matColumnDef="stageName">
        <th class="mat-column-width" mat-header-cell *matHeaderCellDef mat-sort-header> Stage </th>
        <td  data-td-head="StageName" mat-cell *matCellDef="let element"> {{element.fullStageName}} </td>
      </ng-container>

      <ng-container matColumnDef="paymentTypeName">
        <th class="mat-column-width" mat-header-cell *matHeaderCellDef mat-sort-header> Payment Type </th>
        <td  data-td-head=" Payment Type" mat-cell *matCellDef="let element"> {{element.paymentTypeName}} </td>
      </ng-container>

      <ng-container matColumnDef="commissionTypeName">
        <th class="mat-column-width" mat-header-cell *matHeaderCellDef mat-sort-header> Commission Type </th>
        <td  data-td-head="Commission Type"  mat-cell *matCellDef="let element"> {{element.commissionTypeName}} </td>
      </ng-container>

      <ng-container matColumnDef="commissionRuleName">
        <th class="mat-column-width" mat-header-cell *matHeaderCellDef mat-sort-header> Commission Rule </th>
        <td  data-td-head="Commission Rule"  mat-cell *matCellDef="let element"> {{element.commissionRuleName}} </td>
      </ng-container>

      <ng-container matColumnDef="amount">
        <th class="mat-column-width" mat-header-cell *matHeaderCellDef mat-sort-header> Amount </th>
        <td  data-td-head="Amount"  mat-cell *matCellDef="let element"> {{element.amount | currency}} </td>
      </ng-container>

      <ng-container matColumnDef="paymentStatusName">
        <th class="mat-column-width" mat-header-cell *matHeaderCellDef mat-sort-header> Status </th>
        <td  data-td-head="Status"  mat-cell *matCellDef="let element"> {{element.paymentStatus.paymentStatusName}} </td>
      </ng-container>

      <ng-container matColumnDef="salesDivision">
        <th class="mat-column-width" mat-header-cell *matHeaderCellDef mat-sort-header> Sales Division </th>
        <td  data-td-head="Sales Division "  mat-cell *matCellDef="let element"> {{element.salesDivision}} </td>
      </ng-container>

      <ng-container matColumnDef="salesOffice">
        <th class="mat-column-width" mat-header-cell *matHeaderCellDef mat-sort-header> Sales Office </th>
        <td  data-td-head="Sales Office"  mat-cell *matCellDef="let element"> {{element.salesOffice}} </td>
      </ng-container>

      <ng-container matColumnDef="planName">
        <th class="mat-column-width" mat-header-cell *matHeaderCellDef mat-sort-header> Contact Plan </th>
        <td  data-td-head="Contact Plan"  mat-cell *matCellDef="let element"> {{element.planName}} </td>
      </ng-container>

      <ng-container matColumnDef="appointmentSetting">
        <th class="mat-column-width" mat-header-cell *matHeaderCellDef mat-sort-header> Appointment Setting </th>
        <td  data-td-head="Appointment Setting"  mat-cell *matCellDef="let element"> {{element.appointmentSetting}} </td>
      </ng-container>

      <ng-container matColumnDef="appointmentConfirmed">
        <th class="mat-column-width" mat-header-cell *matHeaderCellDef mat-sort-header> Appointment Confirmed </th>
        <td data-td-head="Appointment Confirmed" mat-cell *matCellDef="let element"> {{element.appointmentConfirmed ? 'Yes' : 'No'}} </td>
      </ng-container>

      <ng-container matColumnDef="actualInstallDate">
        <th class="mat-column-width" mat-header-cell *matHeaderCellDef mat-sort-header> Solar Install Date </th>
        <td  data-td-head="Actual Install Date"  mat-cell *matCellDef="let element"> {{element.actualInstallDate | date}} </td>
      </ng-container>

      <ng-container matColumnDef="roofActualInstallDate">
        <th class="mat-column-width" mat-header-cell *matHeaderCellDef mat-sort-header> Roof Install Date </th>
        <td  data-td-head="Roof Actual Install Date"  mat-cell *matCellDef="let element"> {{element.roofActualInstallDate | date}} </td>
      </ng-container>

      <ng-container matColumnDef="batteryActualInstallDate">
        <th class="mat-column-width" mat-header-cell *matHeaderCellDef mat-sort-header> Battery Install Date </th>
        <td  data-td-head="Battery Actual Install Date"  mat-cell *matCellDef="let element"> {{element.batteryActualInstallDate | date}} </td>
      </ng-container>

      <ng-container matColumnDef="dateContractSigned">
        <th class="mat-column-width" mat-header-cell *matHeaderCellDef mat-sort-header> Date Contract Signed </th>
        <td  data-td-head="Date Contract Signed"  mat-cell *matCellDef="let element"> {{element.dateContractSigned | date}} </td>
      </ng-container>

      <ng-container matColumnDef="processedDate">
        <th class="mat-column-width" mat-header-cell *matHeaderCellDef mat-sort-header> Date Created/Processed </th>
        <td  data-td-head="Payment Due Date"  mat-cell *matCellDef="let element"> {{element.processedDate ? (element.processedDate | date) : ""}} </td>
      </ng-container>

      <ng-container matColumnDef="paymentDueDate">
        <th class="mat-column-width" mat-header-cell *matHeaderCellDef mat-sort-header> Payment Due Date </th>
        <td  data-td-head="Payment Due Date"  mat-cell *matCellDef="let element"> {{element.paymentDueDate | date}} </td>
      </ng-container>

      <ng-container matColumnDef="purchaseMethod">
        <th class="mat-column-width" mat-header-cell *matHeaderCellDef mat-sort-header> Purchase Method </th>
        <td  data-td-head="Purchase Method "  mat-cell *matCellDef="let element"> {{element.purchaseMethod}} </td>
      </ng-container>
      <ng-container matColumnDef="employeeStatus">
        <th class="mat-column-width" mat-header-cell *matHeaderCellDef mat-sort-header> Employee Status </th>
        <td  data-td-head="Employee Status "  mat-cell *matCellDef="let element"> {{element.employeeStatus}} </td>
      </ng-container>
      <ng-container matColumnDef="dateProjectPaidinFull">
        <th class="mat-column-width" mat-header-cell *matHeaderCellDef mat-sort-header> Date Project Paid In Full </th>
        <td  data-td-head="Date Project Paid In Full "  mat-cell *matCellDef="let element"> {{element.dateProjectPaidinFull  ? (element.dateProjectPaidinFull | date) : ""}} </td>
      </ng-container>

      <tr mat-header-row *matHeaderRowDef="paymentColumns"></tr>
      <tr mat-row *matRowDef="let row; columns paymentColumns;"></tr>
    </table>
    <div style="display: flex; justify-content: space-between; align-items: center">
      <p style="font-size: 15px; margin-top: 10px"><b>Total Selected Amount: {{getSelectedSum() | currency}}</b></p>
      <mat-paginator [pageSize]="pageSize" [pageSizeOptions]="pageSizeOptions">
      </mat-paginator>
    </div>

    <!-- <table class="table table-striped table-borderless table-hover table-active">
      <thead>
        <tr class="">
          <th scope="col" *ngIf="checkReviewableStatus()">
            <div class="row">
              <section class="checkbox-section">
                <mat-checkbox class="checkbox-all-margin" [(ngModel)]="allSelected" (change)="onBulkSelectionChange()">
                </mat-checkbox>
              </section>
              Selected
            </div>
          </th>
          <th scope="col" class="no-hover-effect">Contact</th>
          <th scope="col" class="no-hover-effect">Opportunity</th>
          <th scope="col" class="no-hover-effect">Payment Type</th>
          <th scope="col" class="no-hover-effect">Commission Type</th>
          <th scope="col" class="no-hover-effect">Amount</th>
          <th scope="col" class="no-hover-effect">Status</th>
          <th scope="col" class="no-hover-effect">Sales Division</th>
          <th scope="col" class="no-hover-effect">Sales Office</th>
          <th scope="col" class="no-hover-effect">Actual Install Date</th>
          <th scope="col" class="no-hover-effect">Date Contract Signed</th>
          <th scope="col" class="no-hover-effect">Payment Due Date</th>
        </tr>
      </thead>
      <tbody *ngIf="payments">
        <tr
          *ngFor="let pmnt of (payments | tableFilter: searchText) | paginate: { id: 'payments', itemsPerPage: pageSize, currentPage: reviewPage }">
          <td *ngIf="checkReviewableStatus()">
            <section class="checkbox-section">
              <mat-checkbox class="checkbox-margin" [(ngModel)]="pmnt.selected" (change)="onSelectionChange()">
              </mat-checkbox>
            </section>
          </td>
          <td class="no-hover-effect">{{pmnt.contactName}}</td>
          <td class="no-hover-effect">{{pmnt.opportunityName}}</td>
          <td class="no-hover-effect">{{pmnt.paymentTypeName}}</td>
          <td class="no-hover-effect">{{pmnt.commissionTypeName}}</td>
          <td class="no-hover-effect">{{pmnt.amount | currency}}</td>
          <td class="no-hover-effect">{{pmnt.paymentStatus.paymentStatusName}}</td>
          <td class="no-hover-effect">{{pmnt.salesDivision}}</td>
          <td class="no-hover-effect">{{pmnt.salesOffice}}</td>
          <td class="no-hover-effect">{{pmnt.actualInstallDate | date}}</td>
          <td class="no-hover-effect">{{pmnt.dateContractSigned | date}}</td>
          <td class="no-hover-effect">{{pmnt.paymentDueDate | date}}</td>
        </tr>
      </tbody>
    </table> -->
    <!-- <div class="row">
      <div class="col-md-10">
        <pagination-controls id="payments" (pageChange)="reviewPage = $event"></pagination-controls>
      </div>
      <div class="col-md-2" style="display: flex; justify-content: space-evenly;">
        <mat-form-field>
          <mat-select [(ngModel)]="pageSize" name="pageSize">
            <mat-option value="10">10 items</mat-option>
            <mat-option value="50">50 items</mat-option>
            <mat-option value="100">100 items</mat-option>
            <mat-option value="300">300 items</mat-option>
            <mat-option value="500">500 items</mat-option>
          </mat-select>
        </mat-form-field>
      </div>
    </div> -->

    <div class="form-group col-md-6 m-auto">
      <div class="row"><div class="col-md-4">
      <label>Process Date</label></div>
      <div class="col-md-8">
      <div class="input-group date-picker">
         <input #ProcessDateCont type="date" class="custom-input" [ngModel]="processDate | date: 'yyyy-MM-dd'" (ngModelChange)="processDate = $event">
         <span *ngIf="ProcessDateCont.value.length > 0" class="mat-icon cal-reset"  (click)="clearDate(ProcessDateCont);"><i class="far fa-calendar-times"></i></span> 
         <span *ngIf="ProcessDateCont.value.length <= 0" class="mat-icon cal-open"><i class="far fa-calendar-alt"></i></span>
                 
        <!-- <mat-form-field> 
          <input #ProcessDateCont  [ngModel]="processDate | date: 'yyyy-MM-dd'" (ngModelChange)="processDate = $event" class="mat-datepicker-input"  matInput [matDatepicker]="ProcessDateContEndPicker" [(ngModel)]="date" placeholder="mm/dd/yyyy" (focus)="ProcessDateContEndPicker.open()">
          <mat-icon matDatepickerToggleIcon *ngIf="ProcessDateCont.value.length > 0"   (click)="clearDate(ProcessDateCont);">X</mat-icon>
          <mat-icon matDatepickerToggleIcon *ngIf="ProcessDateCont.value.length <= 0"   (click)="ProcessDateContEndPicker.open(); "><span class="material-icons">
            event
            </span></mat-icon>        
          <mat-datepicker #ProcessDateContEndPicker></mat-datepicker>
        </mat-form-field>  -->
      </div>
      </div>     
    </div>
    </div>
<div class="form-group col-md-6 m-auto">
  <div class="row">
    <div class="col-md-4">
      <label>Notes</label>
    </div>
    <div class="col-md-8">
      <input class="custom-input" id="notes" [(ngModel)]="notes" placeholder="Payment Approvals Notes">
      <!--  Dilip  COM-1198 -->
    </div>
  </div>

</div>

<div class="form-group payments-button-group" *ngIf="checkReviewableStatus()">
  <button [hidden]="!IsOnHoldSelected()" class="btn btn-primary" (click)="bulkReopen(onHoldStatusId())"
    [disabled]="!checkSelected()">
    <i class="material-icons">done</i>Reopen ({{getNumberSelected()}})
    Selected
  </button>
  <button class="btn btn-primary" (click)="bulkUpdate(approvedStatusId())" [disabled]="!checkSelected()">
    <i class="material-icons">done</i>Approve ({{getNumberSelected()}})
    Selected
  </button>
  <button class="btn btn-primary" (click)="bulkUpdate(onHoldStatusId())" [disabled]="!checkSelected()" [hidden]="IsApprovedOnHoldSelected()">
    <i class="material-icons">hourglass_full</i>Hold ({{getNumberSelected()}})
    Selected
  </button>
  <button class="btn btn-primary" (click)="bulkUpdate(rejectedStatusId())" [disabled]="!checkSelected()" [hidden]="IsApprovedOnHoldSelected()">
    <i class="material-icons">clear</i>Reject ({{getNumberSelected()}})
    Selected
  </button>
  <button [hidden]="IsApprovedOnHoldSelected()" class="btn btn-primary" (click)="bulkUpdate(approvedOnHoldStatusId())" [disabled]="!checkSelected()">
    <i class="material-icons">clear</i>Approved-On Hold  ({{getNumberSelected()}})
    Selected
  </button>
</div>
<div class="col-md-12">
  <!-- Dilip code goes here-->
  <button class="btn btn-primary float-right" [routerLink]="['/ui/commissions/paymentwithdrawals']"><i
      class="fas fa-money-check-alt"></i> Payment Withdrawls</button>

</div>
</div>
</div>
