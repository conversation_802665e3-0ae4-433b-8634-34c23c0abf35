import { Component, OnInit, ViewChild } from '@angular/core';
import { UntypedFormBuilder, UntypedFormGroup, Validators } from "@angular/forms";
import { ApiService } from "../../../services/api.service";
import { ToastrService } from 'ngx-toastr';
import { DatePipe, DecimalPipe } from '@angular/common';
import { TableFilterPipe } from '../../../pipe/table-filter.pipe';
import { MatPaginator } from '@angular/material/paginator';
import { MatSort } from '@angular/material/sort';
import { MatTableDataSource } from '@angular/material/table';

@Component({
  selector: 'app-employee-override-rate-tier',
  templateUrl: './employee-override-rate-tier.component.html',
  styleUrls: ['./employee-override-rate-tier.component.css']
})
export class EmployeeOverrideRateTierComponent implements OnInit {
  columnNames = [{
    id: "fromToCount",
    value: "From - To Count"
  },
{
  id: "effectiveStartDate",
  value: "Effective Start Date"
},
{
  id: "effectiveEndDate",
  value: "Effective End Date"
},
{
  id: "rate",
  value: "Rate"
},
{
  id: "rateType",
  value: "Rate Type"
}
];
displayedColumns = [];
tiers: any;
tableArr: any[] = [];
dataSource: any;
originalDataSource: any;
addRow: boolean = false;
defaultOverrideTier: any;
tierAddForm: UntypedFormGroup;
fromToCountValues = [
  { id: "0",
    value: "0-99",
    fromCount: "0",
    toCount: "99"
  },
  { 
  id: "1",
  value: "100-199",
  fromCount: "100",
  toCount: "199"
  },
  {
    id: "2",
    value: "200-299",
    fromCount: "200",
    toCount: "299"
  },
  {
    id: "3",
    value: "300-10000",
    fromCount: "300",
    toCount: "10000"
}];
rateTypesData =["Per Demo","Fixed"];


@ViewChild(MatSort, { static: true }) sort: MatSort;
@ViewChild(MatPaginator, { static: true }) paginator: MatPaginator;

  constructor(private formBuilder: UntypedFormBuilder, public apiService: ApiService, private toastMsg: ToastrService, private datePipe: DatePipe,
    private decimalPipe: DecimalPipe, private pipe: TableFilterPipe) { }

  ngOnInit() {
    this.displayedColumns = this.columnNames.map(x => x.id);
    this.getTiers();
    this.initializeForm();
  }


  getTiers() {
    this.apiService.get('EmployeeOverrideRateTier')
      .subscribe(data => {
          this.tiers = data;
          this.createTable();
      }, err => {
        this.toastMsg.error(err.message, "Error!");
      })
  }

  createTable() {
    let tableArr: any[] = [];
    for(let i:number = 0; i <= this.tiers.length - 1; i++) {
      let currentRow = this.tiers[i];
      if(i==0)
      {
        this.tableArr[0] =this.tiers[0];
      }
      tableArr.push({employeeOverrideRateTierId: currentRow.employeeOverrideRateTierId, fromToCount: currentRow.fromCount + '-' + currentRow.toCount, effectiveStartDate: this.datePipe.transform(currentRow.effectiveStartDate), effectiveEndDate: this.datePipe.transform(currentRow.effectiveEndDate),
        rate: this.decimalPipe.transform(currentRow.rate, '1.2-2'),rateType:currentRow.rateType});
    }
    this.dataSource = new MatTableDataSource(tableArr);
    this.originalDataSource = tableArr;
    this.dataSource.sort = this.sort;
    this.dataSource.paginator = this.paginator;
  }


  Add() {
    this.defaultOverrideTier = this.tableArr;
    this.addRow = !this.addRow;

  }

  onAddSubmit() {
    var values = {
      fromCount: this.fromToCountValues[this.tierAddForm.controls.fromToCount.value].fromCount,
      toCount: this.fromToCountValues[this.tierAddForm.controls.fromToCount.value].toCount,
      effectiveStartDate: this.tierAddForm.controls.effectiveStartDate.value,
      effectiveEndDate: this.tierAddForm.controls.effectiveEndDate.value,
      rate: this.tierAddForm.controls.rate.value == null ? 0.00 : this.tierAddForm.controls.rate.value,
      rateType:this.tierAddForm.controls.rateType.value
    }
    var sDate = new Date(values.effectiveStartDate)
    sDate.setDate(sDate.getDate() + 1)
    var fom = new Date(sDate.getFullYear(), sDate.getMonth(), 1)
    var convertedEOM = null;

    var valid = true;
    if(values.effectiveEndDate){
      var eDate = new Date(values.effectiveEndDate);
      var eom = new Date(eDate.getFullYear(), eDate.getMonth()+1, 0);
      convertedEOM = this.datePipe.transform(eom, 'yyyy-MM-dd');
      valid = values.effectiveStartDate < values.effectiveEndDate ? true : false;
    }

    if(values.effectiveStartDate == this.datePipe.transform(fom,  'yyyy-MM-dd') && (values.effectiveEndDate == null || values.effectiveEndDate == convertedEOM) && valid){
      this.apiService.post('EmployeeOverrideRateTier/AddOverrideOutreachTier', values)
      .subscribe(data => {
        this.toastMsg.success('Employee Override Rate Tier Added Successfully');
        this.getTiers();
        this.addRow = !this.addRow;
        this.initializeForm();
      }, (err: any) => {
          if (err?.err?.message)
            this.toastMsg.error(err.message, "Server Error!");
          if (typeof err === "string") {
            let isJsonObject = false;            
            let parsedErr: any;
            try {
              parsedErr = JSON.parse(err);
              isJsonObject =parsedErr && typeof parsedErr === "object" &&!Array.isArray(parsedErr);
            } catch (e) {
              isJsonObject = false;
            }
            if (isJsonObject) {
              this.toastMsg.error(parsedErr.result, "Error!");
            } else this.toastMsg.error(err, "Error!");
          }
          if (err?.result) {
            this.toastMsg.error(err.result, "Error!");
          }
        });
    }
    else{
      this.toastMsg.error('Invalid Date Range, Submission must be first and last day of the month with end date effective after start date.');
    }
}

clearAddStartDate(date: HTMLInputElement) {
  date.value = "";
  this.tierAddForm.controls.effectiveStartDate.setValue('');
}

clearAddEndDate(date: HTMLInputElement) {
  date.value = null;
  this.tierAddForm.controls.effectiveEndDate.setValue(null);
}

initializeForm(){
  this.tierAddForm = this.formBuilder.group({
    employeeOverrideRateTierId: [0],
    fromToCount: [0, [Validators.required]],
    fromCount: [0, [Validators.required]],
    toCount: [0, [Validators.required]],
    effectiveStartDate: ["", [Validators.required]],
    effectiveEndDate: [null],
    rate: ["", [Validators.required]],
    rateType:[""]
  });
}

}

export interface OverrideRatesTierModel{
  employeeOverrideRateTierId: number,
  fromCount: number,
  toCount: number,
  effectiveEndDate: string,
  effectiveStartDate: string,
  rate: string
}