import { Component, OnInit, Inject } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { ApiService } from '../../../services/api.service';
import { ToastrService } from 'ngx-toastr';

@Component({
  selector: 'app-battery-retrofit-commission-rate-maintenance-dialog',
  templateUrl: './battery-retrofit-commission-rate-maintenance-dialog.component.html',
  styleUrls: ['./battery-retrofit-commission-rate-maintenance-dialog.component.css']
})
export class BatteryRetrofitCommissionRateMaintenanceDialogComponent implements OnInit {
  batteryRetrofitCommissionRatesGroup: Element[] = [];
  selectedRateGroup: any;
  constructor(public dialogRef: MatDialogRef<BatteryRetrofitCommissionRateMaintenanceDialogComponent>,
    private apiService: ApiService, private toastMsg: ToastrService, @Inject(MAT_DIALOG_DATA) public data: any) { }

  ngOnInit() {
    this.batteryRetrofitCommissionRatesGroup = this.data.batteryRetrofitCommissionRate;
    this.batteryRetrofitCommissionRatesGroup.sort((a, b) => {
      // return <any>new (b[0].batteryCommissionMappingId) - <any>new (a[0].batteryCommissionMappingId);  
      return <any>(b[0].batteryRetrofitCommissionMappingId) - <any>(a[0].batteryRetrofitCommissionMappingId);
    });
  }

  isObject(val) {
    if (val === null) { return false; }
    return ((typeof val === 'function') || (typeof val === 'object'));
  }

  isObjectEmpty(obj) {
    return Object.keys(obj).length === 0;
  }

  groupClick(group: any) {
    var batteryRetrofitCommissionList = this.batteryRetrofitCommissionRatesGroup.forEach(element => {
      var selectedGroup = element.filter(x => x.batteryRetrofitCommissionMappingId === group)
      if (typeof selectedGroup === 'object') {
        if (!this.isObjectEmpty(selectedGroup)) {
          this.selectedRateGroup = selectedGroup;
        }
      }
    });
  }
}

export interface Element {
  [x: string]: any;
  effectiveStartDate: string,
  effectiveEndDate: string,
  batteryRetrofitCommissionMappingId: number;
}


