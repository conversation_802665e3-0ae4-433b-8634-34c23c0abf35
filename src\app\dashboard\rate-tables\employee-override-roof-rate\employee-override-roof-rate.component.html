<div class="page-title col-md-12">
  <h1>Employee Override Rates</h1>
  <div class="breadcrumbs">
    <a href="#">Home</a>/<span>Employee Override Rates</span>
  </div>
</div>

<div class="content">
  <div class="card">
    <div class="card-header-info">
      <h4 class="card-title no-hover-effect">Employee Override Rates</h4>
    </div>

    <div class="card-body">
      <div class="row">
        <div class="card-body">
          <div class="row">
            <div class="col-md-12">
              <div class="input-group float-right table-search">
                <input class="custom-input" type="text" id="searchTextId" [(ngModel)]="searchText" name="searchText" placeholder="Search" (input)="searchForItem()"/>
                <span class="input-group-icon"><i class="fas fa-search"></i></span>
              </div>
            </div>
          </div>

          <mat-table #table [dataSource]="dataSource" matSort>
            <ng-container matColumnDef="{{ column.id }}" *ngFor="let column of columnNames">
              <mat-header-cell *matHeaderCellDef mat-sort-header class="table-header"> {{ column.value }} </mat-header-cell>
              <mat-cell [attr.data-td-head]="column.value" *matCellDef="let element">{{ element[column.id] }}</mat-cell>
            </ng-container>
            <mat-header-row *matHeaderRowDef="displayedColumns"></mat-header-row>
            <mat-row *matRowDef="let row; columns: displayedColumns" class="pointer table-content" (click)="rowClick(row)"></mat-row>
          </mat-table>

          <mat-paginator [pageSizeOptions]="[5, 10, 20, 50]"showFirstLastButtons></mat-paginator>
          <div>
            <a class="btn btn-primary float-right" *ngIf="!addRow" (click)="Add()"><i class="material-icons pointer">add_circle</i> Add</a>
            <a class="btn btn-primary float-right" *ngIf="addRow" (click)="addRow = !addRow"><i class="material-icons pointer">remove_circle</i> Hide</a>
            <a type="submit" class="btn btn-primary float-right" (click)="upload()">Upload</a>
            <label for="file-select" class="btn btn-primary float-right">Select File</label>
            <input type="file" id="file-select" style="display: none" (change)="handleFileInput($event)" value="" accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"/>
          </div>
        </div>
      </div>

      <div class="card" *ngIf="addRow">
        <div class="card-header-info">
          <h4 class="card-title no-hover-effect">
            <i class="fas fa-plus"></i> Add Employee Override Rate
          </h4>
        </div>

        <div class="card-body">
          <div>
            <form [formGroup]="employeeOverrideFormAdd" (ngSubmit)="onAddSubmit()" class="w-100">
              <div class="row">
                <div class="form-group col-md-4">
                  <div class="row">
                    <label class="col-sm-5">Role Name</label>
                    <div class="col-sm-7">
                      <select class="custom-select" name="rolename_add_dropdown" formControlName="roleName" data-style="btn btn-link" id="rolename_add_dropdown">
                        <option *ngFor="let rn of this.roleNames" [value]="rn"> {{ rn }} </option>
                      </select>
                    </div>
                  </div>
                </div>

                <div class="form-group col-md-4">
                  <div class="row">
                    <label class="col-sm-5">Tier</label>
                    <div class="col-sm-7">
                      <select class="custom-select" name="tier_add_dropdown" formControlName="tier" data-style="btn btn-link" id="tier_add_dropdown">
                        <option *ngFor="let tier of this.tiers" [value]="tier"> {{ tier }} </option>
                          <!-- [selected]="rn == employeeOverrideRoofingRateAdd[0].roleName"  -->
                          
                      </select>
                    </div>
                  </div>
                </div>
<!-- 
                <div class="form-group col-md-4">
                  <div class="row">
                    <label class="col-sm-5">From Roofing Squares</label>
                    <div class="col-sm-7">
                      <input class="custom-input" formControlName="fromRoofSquares" id="from_roofing_squares_add" name="from_roofing_squares_add" value="{{employeeOverrideRoofingRateAdd[0].fromRoofSquares | number : '1.2-2'}}"/>
                    </div>
                  </div>
                </div>

                <div class="form-group col-md-4">
                  <div class="row">
                    <label class="col-sm-5">To Roofing Squares</label>
                    <div class="col-sm-7">
                      <input class="custom-input" formControlName="toRoofSquares" id="to_roofing_squares_add" name="to_roofing_squares_add" value="{{employeeOverrideRoofingRateAdd[0].toRoofSquares | number : '1.2-2'}}"/>
                    </div>
                  </div>
                </div> -->


                <div class="form-group col-md-4">
                  <div class="row">
                    <label class="col-sm-5">Effective Start Date</label>
                    <div class="col-sm-7">
                      <div class="date-picker w-100">
                        <input #AddStartDatePicker type="date" name="start_date" id="start_date" class="custom-input" formControlName="effectiveStartDate" placeholder=""/>
                        <span *ngIf="AddStartDatePicker.value.length > 0" class="mat-icon cal-reset" (click)="clearAddStartDate(AddStartDatePicker)"><i class="far fa-calendar-times"></i></span>
                        <span *ngIf="AddStartDatePicker.value.length <= 0" class="mat-icon cal-open"><i class="far fa-calendar-alt"></i></span>
                      </div>
                    </div>
                  </div>
                </div>

                <div class="form-group col-md-4">
                  <div class="row">
                    <label class="col-sm-5">Effective End Date</label>
                    <div class="col-sm-7">
                      <div class="date-picker w-100">
                        <input #AddEndDatePicker type="date" name="effectiveEndDate" id="effectiveEndDate" class="custom-input" formControlName="effectiveEndDate" placeholder=""/>
                        <span *ngIf="AddEndDatePicker.value.length > 0" class="mat-icon cal-reset" (click)="clearAddEndDate(AddEndDatePicker)"><i class="far fa-calendar-times"></i></span>
                        <span *ngIf="AddEndDatePicker.value.length <= 0" class="mat-icon cal-open"><i class="far fa-calendar-alt"></i></span>
                      </div>
                    </div>
                  </div>
                </div>

                <div class="form-group col-md-4">
                  <div class="row">
                    <label class="col-sm-5">Override Rate</label>
                    <div class="col-sm-7">
                      <input class="custom-input" formControlName="overrideRate" id="overriderate_add" name="overriderate_add" value="{{employeeOverrideRoofingRateAdd[0].overrideRate | number : '1.2-2'}}"/>
                    </div>
                  </div>
                </div>
              </div>
              <div class="row align-button-right">
                <button type="submit" class="btn btn-primary" [disabled]="employeeOverrideFormAdd.invalid"><i class="fas fa-plus"></i> Add Employee Override Rate</button>
              </div>
            </form>
          </div>
        </div>
      </div>

      <div class="card" *ngIf="editRow">
        <div class="card-header-info">
          <h4 class="card-title no-hover-effect">
            <i class="fas fa-plus"></i> Edit Employee Override Rate
          </h4>
        </div>
        <div class="card-body">
          <div>
            <form [formGroup]="employeeOverrideFormEdit" (ngSubmit)="onEditSubmit(employeeOverrideData)" class="w-100">
              <div class="row">
                <div class="form-group col-md-4">
                  <div class="row">
                    <label class="col-sm-5">Role Name</label>
                    <div class="col-sm-7">
                      <input class="custom-input" formControlName="roleName" name="roleName_edit" id="roleName_edit" value="{{ employeeOverrideRoofingRateEdit[0].roleName }}"/>
                    </div>
                  </div>
                </div>

                <div class="form-group col-md-4">
                  <div class="row">
                    <label class="col-sm-5">Tier</label>
                    <div class="col-sm-7">
                      <input class="custom-input" formControlName="tier" name="tier_edit" id="tier_edit" value="{{ employeeOverrideRoofingRateEdit[0].tier }}"/>
                    </div>
                  </div>
                </div>

                <!-- <div class="form-group col-md-4">
                  <div class="row">
                    <label class="col-sm-5">From Roofing Squares</label>
                    <div class="col-sm-7">
                      <input class="custom-input" formControlName="fromRoofSquares" name="fromRoofSquares_edit" id="fromRoofSquares_edit" value="{{ employeeOverrideRoofingRateEdit[0].fromRoofSquares }}"/>
                    </div>
                  </div>
                </div>

                <div class="form-group col-md-4">
                  <div class="row">
                    <label class="col-sm-5">To Roofing Squares</label>
                    <div class="col-sm-7">
                      <input class="custom-input" formControlName="toRoofSquares" name="toRoofSquares_edit" id="toRoofSquares_edit" value="{{ employeeOverrideRoofingRateEdit[0].toRoofSquares }}"/>
                    </div>
                  </div>
                </div> -->

                <div class="form-group col-md-4">
                  <div class="row">
                    <label class="col-sm-5">Effective Start Date</label>
                    <div class="col-sm-7">
                      <div class="date-picker w-100">
                        <input #EditStartDatePicker type="date" name="effectiveStartDate" id="effectiveStartDate" class="custom-input" formControlName="effectiveStartDate" placeholder="" />
                        <span *ngIf="EditStartDatePicker.value.length > 0" class="mat-icon cal-reset" (click)="clearEditStartDate(EditStartDatePicker)"><i class="far fa-calendar-times"></i></span>
                        <span *ngIf="EditStartDatePicker.value.length <= 0" class="mat-icon cal-open"><i class="far fa-calendar-alt"></i></span>
                      </div>
                    </div>
                  </div>
                </div>

                <div class="form-group col-md-4">
                  <div class="row">
                    <label class="col-sm-5">Effective End Date</label>
                    <div class="col-sm-7">
                      <div class="date-picker w-100">
                        <input #EditEndDatePicker type="date" name="effectiveEndDate" id="effectiveEndDate" class="custom-input" formControlName="effectiveEndDate" placeholder=""/>
                        <span *ngIf="EditEndDatePicker.value.length > 0" class="mat-icon cal-reset" (click)="clearEditEndDate(EditEndDatePicker)"><i class="far fa-calendar-times"></i></span>
                        <span *ngIf="EditEndDatePicker.value.length <= 0" class="mat-icon cal-open"><i class="far fa-calendar-alt"></i></span>
                      </div>
                    </div>
                  </div>
                </div>

                <div class="form-group col-md-4">
                  <div class="row">
                    <label class="col-sm-5">Override Rate</label>
                    <div class="col-sm-7">
                      <input class="custom-input" name="overrideRate_edit" id="overrideRate_edit" formControlName="overrideRate" value="{{employeeOverrideRoofingRateEdit[0].overrideRate | number : '1.2-2'}}"/>
                    </div>
                  </div>
                </div>
              </div>
              <div class="row align-button-right">
                <button type="button" class="btn btn-primary" name="delete" (click)="deleteRate()">
                  <i class="fas fa-plus"></i> Delete Rate
                </button>
                <button type="submit" class="btn btn-primary" name="edit" [disabled]="employeeOverrideFormEdit.invalid">
                  <i class="fas fa-plus"></i> Update Employee Override Rate
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>