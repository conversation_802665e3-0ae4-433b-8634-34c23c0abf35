import { Component, OnInit, Inject } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { ApiService } from '../../../services/api.service';
import { ToastrService } from 'ngx-toastr';

@Component({
  selector: 'app-ppa-bonus-flat-rate-maintenance-dialog',
  templateUrl: './ppa-bonus-flat-rate-maintenance-dialog.component.html',
  styleUrls: ['./ppa-bonus-flat-rate-maintenance-dialog.component.css']
})
export class PPABonusFlatRateMaintenanceDialogComponent implements OnInit {
  PPABonusFlatRateGroup: Element[] = [];
  PPABonusFlatRateSelectedGroup: any;
  
  constructor(public dialogRef: MatDialogRef<PPABonusFlatRateMaintenanceDialogComponent>,
    private apiService: ApiService, private toastMsg: ToastrService, @Inject(MAT_DIALOG_DATA) public data: any) { }


  ngOnInit() {
    this.PPABonusFlatRateGroup = this.data.PPABonusFlatRate; 
    this.PPABonusFlatRateGroup.sort((a, b) => {
      return <any>new Date(b[0].effectiveStartDate) - <any>new Date(a[0].effectiveStartDate);
    });
    console.log(this.PPABonusFlatRateGroup);
  }

  groupClick(group: any) {
    this.PPABonusFlatRateSelectedGroup = group;
    console.log(this.PPABonusFlatRateSelectedGroup);
  }

}

export interface Element {
  effectiveStartDate: string,
  effectiveEndDate: string
}

