<div class="card">
  <div class="card-header-info">
    <h4 class="card-title no-hover-effect">Opportunity Details</h4>
  </div>
  <div *ngIf="opportunity?.flip" style="background-color: #f89406; border-radius: 3px; padding: 15px 15px 15px 10px; color: #fff; font-size: 20px">
    <i class="fas fa-exclamation-triangle"></i>
    This is a FLIP Opportunity
  </div>
  <div class="card-body">
    <!-- <div class="row justify-content-between" id="calc-button-container">
      <button class="btn btn-info" (click)="unfinalizeOpportunity(opportunity.opportunityId)" [disabled]="opportunity && !opportunity.opportunityFinalized">Unfinalize</button>
      <button type="submit" class="btn btn-info"
        (click)="checkOpportunityCommission(opportunity.opportunityId)">Calc/ReCalc Commission</button>
    </div> -->

<!-- dilip 05/22/2020--> 
    <div class="row" id="calc-button-container"> <div class="col-md-12 button-bar">
      <span class="float-right mt-3 mx-1" *ngIf="finalizationDate && (opportunity && opportunity.opportunityFinalized)">Finalized On: {{ finalizationDate}}</span>
      <button class="btn btn-primary float-right" (click)="unfinalizeOpportunity(opportunity.opportunityId)" *ngIf="checkCanViewUnfinalize()" [disabled]="opportunity && !opportunity.opportunityFinalized"><i class="far fa-times-circle"></i> Unfinalize</button>
      <button type="submit" class="btn btn-primary float-right"
        (click)="checkOpportunityCommission(opportunity.opportunityId, false)" *ngIf="checkCanViewCalcReCalcCommission()"><i class="fas fa-calculator"></i> Calc/ReCalc Commission</button>
      <button type="submit" class="btn btn-primary float-right"
        (click)="checkOpportunityCommission(opportunity.opportunityId, true)" *ngIf="checkCanViewEvaluateCommission()"><i class="fas fa-calculator"></i> Evaluate Commission</button>
      <button type="submit" class="btn btn-primary float-right" *ngIf="opportunity && !enableEdit"
        (click)="enableEdit=!enableEdit"><i class="fas fa-edit"></i> Edit</button>
      <button type="submit" class="btn btn-primary float-right" *ngIf="enableEdit"
       (click)="onOpportunitySave()"><i class="fas fa-save"></i> Save</button>
      <button type="submit" class="btn btn-primary float-right" *ngIf="enableEdit"
       (click)="enableEdit=!enableEdit"><i class="fas fa-ban"></i> Cancel</button>
    </div>
  </div>

    <ng-container *ngIf="opportunity">
      <app-opportunity-detail-list [header]="'OPPORTUNITY DETAILS'" [rows]="opportunityDetailsList" [show]="true" [enableEdit]="enableEdit" [updatedOpportunityValuesForm]="updatedOpportunityValuesForm"></app-opportunity-detail-list>

      <app-opportunity-detail-list [header]="'SOLAR - SYSTEM INFO'" [rows]="systemInfoList" [enableEdit]="enableEdit" [updatedOpportunityValuesForm]="updatedOpportunityValuesForm"></app-opportunity-detail-list>

      <app-opportunity-detail-list [header]="'TRINITY CONTACTS'" [rows]="trinityContactList" [enableEdit]="enableEdit" [updatedOpportunityValuesForm]="updatedOpportunityValuesForm"></app-opportunity-detail-list>

      <app-opportunity-detail-list [header]="'SOLAR - IMPORTANT DATES'" [rows]="importantDatesList" [enableEdit]="enableEdit" [updatedOpportunityValuesForm]="updatedOpportunityValuesForm"></app-opportunity-detail-list>

      <app-opportunity-detail-list [header]="'SOLAR - FINANCIAL INFO'" [rows]="financeInfoList" [enableEdit]="enableEdit" [updatedOpportunityValuesForm]="updatedOpportunityValuesForm"></app-opportunity-detail-list>

      <app-opportunity-detail-list [header]="'ROOFING DETAILS'" [rows]="roofInfoList" [isRoof]="true" [slopeType]="slopeType" [enableEdit]="enableEdit" [updatedOpportunityValuesForm]="updatedOpportunityValuesForm"></app-opportunity-detail-list>

      <app-opportunity-detail-list [header]="'BATTERY DETAILS'" [rows]="batteryInfoList" [enableEdit]="enableEdit" [updatedOpportunityValuesForm]="updatedOpportunityValuesForm"></app-opportunity-detail-list>

      <app-opportunity-detail-list [header]="'R&R DETAILS'" [rows]="rrInfoList" [enableEdit]="enableEdit" [updatedOpportunityValuesForm]="updatedOpportunityValuesForm"></app-opportunity-detail-list>
    </ng-container>

    <!-- <div class="p opportunity-table">
      <table class="table">
        <tbody>
          <tr>
            <th>Name</th>
            <th>Sales Person</th>
            <th>System Size</th>
            <th>Contract Amount</th>
            <th>Purchase Method</th>
            <th>Territory </th>
            <th>Contract Signed</th>
            <th>Utility</th>
          </tr>
          <tr *ngIf="opportunity">
            <td>{{opportunity.opportunityName}}</td>
            <td><a [routerLink]="['/ui/commissions/salesrep', opportunity.trinitySalespersonId]"
                class="text-info nav-link w-100">{{opportunity.trinitySalespersonName}}</a></td>
            <td>{{opportunity.systemSizeKWdc}} kW</td>
            <td>{{opportunity.opportunityAmount | currency}}</td>
            <td>{{opportunity.purchaseMethod}}</td>
            <td>{{opportunity.salesTerritory}} </td>
            <td>{{opportunity.dateContractSigned | date}}</td>
            <td>{{opportunity.utilityCompany}}</td>
          </tr>
        </tbody>
      </table>
    </div> -->
  </div>
</div>