import { Component, OnInit, Inject } from '@angular/core';
import { IRule } from 'src/app/model/rule.model';
import { AddOnRule } from '../employee-incentive-dialog/employee-incentive-dialog.component';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { ApiService } from 'src/app/services/api.service';
import { ToastrService } from 'ngx-toastr';
import { IPlanDetail } from 'src/app/model/plan.model';
import { IPrompt } from 'src/app/model/prompt.model';

@Component({
  selector: 'app-payment-book-dialog',
  templateUrl: './payment-book-dialog.component.html',
  styleUrls: ['./payment-book-dialog.component.css']
})
export class PaymentBookDialogComponent implements OnInit {
  selectedRuleId: number = null;
  prompts: IRule;
  promptsValues: object = {};
  addOn: AddOnRule;

  constructor(public dialogRef: MatDialogRef<PaymentBookDialogComponent>, @Inject(MAT_DIALOG_DATA) public data: IPaymentBookDialogData, private apiService: ApiService, private toastMsg: ToastrService) { }

  ngOnInit() {
  }

  onSelect() {
    // console.log(this.selectedRuleId);
    this.prompts = null;
    this.addOn = null;
    var rulePrompts = this.data.paymentBookRules[0].rules.filter(p => p.ruleId == this.selectedRuleId)[0];
    // console.log(rulePrompts);
    if (rulePrompts) {
      this.prompts = rulePrompts;
      this.addOn = <AddOnRule>rulePrompts;
      this.addOn.promptValues = {};
    }
  }

}

export interface IPaymentBookDialogData {
  paymentBookRules: IPlanDetail[];
  prompts: IPrompt[];
}