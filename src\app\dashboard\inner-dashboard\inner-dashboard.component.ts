import { Component, OnInit, ViewChild } from '@angular/core';
import { UntypedFormBuilder, FormGroup, Validators } from "@angular/forms";
import { ApiService } from '../../services/api.service';
import { Router, ActivatedRoute } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
// import { stringify } from 'querystring';
import { InfoBox, TabSection, NewTab } from './model/infobox.model';
import {MatSort} from '@angular/material/sort';
import {MatTableDataSource} from '@angular/material/table';
import { DataSource } from '@angular/cdk/table';
import {MatPaginator} from '@angular/material/paginator';
import { TableFilterPipe } from '../../pipe/table-filter.pipe';
import { DatePipe, PercentPipe, CurrencyPipe } from '@angular/common';

@Component ({
    selector: 'app-inner-dashboard',
    templateUrl: './inner-dashboard.component.html',
    styleUrls: ['./inner-dashboard.component.css']
})
export class InnerDashboardComponent implements OnInit {
    // displayedColumns: string[];
    // dataSource = new MatTableDataSource<PeriodicElement>(ELEMENT_DATA);

    tabPosition: number =0;
    infoBox: InfoBox = {blueInfo: {name: "", number: 0}, 
    greenInfo: {name: "", number: 0}, 
    greyInfo: {name: "", number: 0}};
    getProcessedPayments: any;
    getPendingPayments: any;
    getPendingReclaims: any;
    originalDataSource: any;
    originalDataSourcePending: any;
    originalDataSourceReclaim: any;
    originalDataSourceSales: any;
    getActiveSalesReps: any;
    dataSource;
    dataSourcePending;
    dataSourceReclaim;
    dataSourceSales;
    displayedColumns = [];
    displayedColumnsPending = [];
    displayedColumnsReclaim = [];
    displayedColumnsSales = [];
    tabSection: TabSection[];
    tableInfo: NewTab;
    @ViewChild(MatSort) sort: MatSort;
    @ViewChild(MatSort) sortPending: MatSort;
    @ViewChild(MatSort) sortReclaims: MatSort;
    @ViewChild(MatSort) sortSales: MatSort;
    @ViewChild(MatPaginator) paginator: MatPaginator;
    @ViewChild(MatPaginator) paginatorPending: MatPaginator;
    @ViewChild(MatPaginator) paginatorReclaims: MatPaginator;
    @ViewChild(MatPaginator) paginatorSales: MatPaginator;

    columnNames = [{
        id: "Contact_Legal_Name",
        value: "Contact Legal Name"
    
      }, {
        id: "Opportunity_Name",
        value: "Opportunity Name"
      },
      {
        id: "Payment_Type_Name",
        value: "Payment Type Name"
      },
      {
        id: "Date_Processed",
        value: "Date Processed"
      },
      {
        id: "System_Size_kWdc",
        value: "System Size kWdc"
      },
      {
        id: "Amount",
        value: "Amount"
      }];

      columnNamesAlt = [{
          id: "Contact_Name",
          value: "Contact Name"
      },
    {
        id: "Sales_Office",
        value: "Sales Office"
    },
{
    id: "Title",
    value: "Title"
},
{
    id: "Start_Date",
    value: "Start Date"
},
{
    id: "Contact_Phone",
    value: "Contact Phone"
},
{
    id: "Contact_Email",
    value: "Contact Email"
}];
    

    constructor( public apiService: ApiService, private toastMsg: ToastrService, private router: Router, private activatedRoute: ActivatedRoute, private formBuilder: UntypedFormBuilder, private pipe: TableFilterPipe, 
        private datePipe: DatePipe, private percentPipe: PercentPipe, private currencyPipe: CurrencyPipe) {

        this.apiService.hideLoader = true;
        localStorage.setItem('href', window.location.href);
        
        this.infoBox = {blueInfo: {name: "Active Sales Rep", number:145},
    greenInfo: {name: "Active Plans", number: 57}, 
    greyInfo: {name: "Representative Bonus", number: 90}};

    this.tabSection = [{newTab: [{title: "New Item", 
    tabContent: {columnNames: ["Id", "Element 1", "Element 2", "Element 3", "Element 4", "Element 5"], 
    columnData: [{position: 1, data: [1,2,3,4,5]},
    {position: 2, data: [1,2,3,4,5]},
    {position: 3, data: [1,2,3,4,5]},
    {position: 4, data: [1,2,3,4,5]},
    {position: 5, data: [1,2,3,4,5]}]}}]}];

        this.tableInfo = {title: "Nothing Selected Yet", tabContent: {columnNames: [], columnData: []}};
    }

    ngOnInit() {
        this.getData();
        // this.apiService.get('Dashboard/GetProcessedPayments')
        // .subscribe(data => {
        //     console.log(data);
        //     if (data.statusCode === "200" || data.statusCode === "201") {
        //         console.log(data.result);
        //         console.log(Object.keys(data.result[0]));
        //         this.tabSection[0].newTab[this.tabPosition].title = "Get Processed Payment";
        //         this.tabSection[0].newTab[this.tabPosition];
        //         const objectKey = Object.keys(data.result[0]);
        //         this.tabSection[0].newTab[this.tabPosition].tabContent.columnNames = objectKey;
        //         this.tabSection[0].newTab[this.tabPosition].tabContent.columnNames.unshift("Id");
        //         this.tabSection[0].newTab[this.tabPosition].tabContent.columnData = [];
        //         for (let i:number = 0; i < data.result.length; i++) {
        //             const currentItem = data.result[i];
        //             this.tabSection[0].newTab[this.tabPosition].tabContent.columnData[i] = {position: i, data: Object.values(data.result[i])};
        //         }
        //         console.log("New Object", this.tabSection);
        //     }
        // })
        
        // this.apiService.get('Dashboard/GetPendingPayments')
        // .subscribe(data => {
        //     console.log(data);
        //     if (data.statusCode === "200" || data.statusCode === "201") {
        //         console.log(data.result);
        //         console.log(Object.keys(data.result[0]));
        //         this.tabPosition = this.tabPosition + 1;
        //         this.tabSection[0].newTab.push({title: "New Item", 
        //         tabContent: {columnNames: ["Id", "Element 1", "Element 2", "Element 3", "Element 4", "Element 5"], 
        //         columnData: [{position: 1, data: [1,2,3,4,5]},
        //         {position: 2, data: [1,2,3,4,5]},
        //         {position: 3, data: [1,2,3,4,5]},
        //         {position: 4, data: [1,2,3,4,5]},
        //         {position: 5, data: [1,2,3,4,5]}]}});
        //         this.tabSection[0].newTab[this.tabPosition].title = "Get Pending Payment";
        //         this.tabSection[0].newTab[this.tabPosition];
        //         const objectKey = Object.keys(data.result[0]);
        //         this.tabSection[0].newTab[this.tabPosition].tabContent.columnNames = objectKey;
        //         this.tabSection[0].newTab[this.tabPosition].tabContent.columnNames.unshift("Id");
        //         this.tabSection[0].newTab[this.tabPosition].tabContent.columnData = [];
        //         for (let i:number = 0; i < data.result.length; i++) {
        //             const currentItem = data.result[i];
        //             this.tabSection[0].newTab[this.tabPosition].tabContent.columnData[i] = {position: i, data: Object.values(data.result[i])};
        //         }
        //         console.log("New Object", this.tabSection);
        //     }
        // })
        // this.apiService.get('Dashboard/GetPendingReclaims')
        // .subscribe(data => {
        //     console.log(data);
        //     if (data.statusCode === "200" || data.statusCode === "201") {
        //         console.log(data.result);
        //         console.log(Object.keys(data.result[0]));
        //         this.tabPosition = this.tabPosition + 1;
        //         this.tabSection[0].newTab.push({title: "New Item", 
        //         tabContent: {columnNames: ["Id", "Element 1", "Element 2", "Element 3", "Element 4", "Element 5"], 
        //         columnData: [{position: 1, data: [1,2,3,4,5]},
        //         {position: 2, data: [1,2,3,4,5]},
        //         {position: 3, data: [1,2,3,4,5]},
        //         {position: 4, data: [1,2,3,4,5]},
        //         {position: 5, data: [1,2,3,4,5]}]}});
        //         this.tabSection[0].newTab[this.tabPosition].title = "Get Pending Reclaims";
        //         this.tabSection[0].newTab[this.tabPosition];
        //         const objectKey = Object.keys(data.result[0]);
        //         this.tabSection[0].newTab[this.tabPosition].tabContent.columnNames = objectKey;
        //         this.tabSection[0].newTab[this.tabPosition].tabContent.columnNames.unshift("Id");
        //         this.tabSection[0].newTab[this.tabPosition].tabContent.columnData = [];
        //         for (let i:number = 0; i < data.result.length; i++) {
        //             const currentItem = data.result[i];
        //             this.tabSection[0].newTab[this.tabPosition].tabContent.columnData[i] = {position: i, data: Object.values(data.result[i])};
        //         }
        //         console.log("New Object", this.tabSection);
        //     }
        // })
        // this.apiService.get('Dashboard/GetActiveSalesReps')
        // .subscribe(data => {
        //     console.log(data);
        //     if (data.statusCode === "200" || data.statusCode === "201") {
        //         console.log(data.result);
        //         console.log(Object.keys(data.result[0]));
        //         this.tabPosition = this.tabPosition + 1;
        //         this.tabSection[0].newTab.push({title: "New Item", 
        //         tabContent: {columnNames: ["Id", "Element 1", "Element 2", "Element 3", "Element 4", "Element 5"], 
        //         columnData: [{position: 1, data: [1,2,3,4,5]},
        //         {position: 2, data: [1,2,3,4,5]},
        //         {position: 3, data: [1,2,3,4,5]},
        //         {position: 4, data: [1,2,3,4,5]},
        //         {position: 5, data: [1,2,3,4,5]}]}});
        //         this.tabSection[0].newTab[this.tabPosition].title = "Get Active Reclaims";
        //         this.tabSection[0].newTab[this.tabPosition];
        //         const objectKey = Object.keys(data.result[0]);
        //         this.tabSection[0].newTab[this.tabPosition].tabContent.columnNames = objectKey;
        //         this.tabSection[0].newTab[this.tabPosition].tabContent.columnNames.unshift("Id");
        //         this.tabSection[0].newTab[this.tabPosition].tabContent.columnData = [];
        //         for (let i:number = 0; i < data.result.length; i++) {
        //             const currentItem = data.result[i];
        //             this.tabSection[0].newTab[this.tabPosition].tabContent.columnData[i] = {position: i, data: Object.values(data.result[i])};
        //         }
        //         console.log("New Object", this.tabSection);
        //     }
        // })
        
        console.log(this.tabSection);
    }

    changeTableInfo(subTab: NewTab) {
        this.tableInfo = subTab;
        console.log(this.tableInfo);
    }

    getData(){
        this.apiService.get('Dashboard/GetProcessedPayments')
            .subscribe(data => {
                console.log("first", data.result);
                this.getProcessedPayments = data.result;
                this.displayedColumns = this.columnNames.map(x => x.id);
                this.createTable();
              }, (err: any) => {
                this.toastMsg.error(err.message, 'Server Error!');
              });
        
        this.apiService.get('Dashboard/GetPendingPayments')
        .subscribe(data => {
            console.log("second", data.result);
            this.getPendingPayments = data.result;
            this.displayedColumnsPending = this.columnNames.map(x => x.id);
            this.createTablePending();
          }, (err: any) => {
            this.toastMsg.error(err.message, 'Server Error!');
          });
        this.apiService.get('Dashboard/GetPendingReclaims')
        .subscribe(data => {
            console.log("third", data.result);
            this.getPendingReclaims = data.result;
            this.displayedColumnsReclaim = this.columnNames.map(x => x.id);
            this.createTableReclaim();
          }, (err: any) => {
            this.toastMsg.error(err.message, 'Server Error!');
          });
        this.apiService.get('Dashboard/GetActiveSalesReps')
        .subscribe(data => {
            console.log("fourth", data.result);
            this.getActiveSalesReps = data.result;
            this.displayedColumnsSales = this.columnNamesAlt.map(x => x.id);
            this.createTableSales();
          }, (err: any) => {
            this.toastMsg.error(err.message, 'Server Error!');
          });
    }
    createTable() {
        let tableArr: Element[] = [];
        for(let i:number = 0; i <= this.getProcessedPayments.length - 1; i++) {
          let currentRow = this.getProcessedPayments[i];
          tableArr.push({Contact_Legal_Name: currentRow.contact_Legal_Name, Opportunity_Name: currentRow.opportunity_Name, Payment_Type_Name: currentRow.payment_Type_Name,
            Date_Processed: this.datePipe.transform(currentRow.date_Processed), System_Size_kWdc: currentRow.system_Size_kWdc, Amount: this.currencyPipe.transform(currentRow.amount)});
        }
        this.dataSource = new MatTableDataSource(tableArr);
        this.originalDataSource = tableArr;
        this.dataSource.sort = this.sort;
        this.dataSource.paginator = this.paginator;
        console.log(this.originalDataSource);
      }
      createTablePending() {
        let tableArr: Element[] = [];
        for(let i:number = 0; i <= this.getPendingPayments.length - 1; i++) {
          let currentRow = this.getPendingPayments[i];
          tableArr.push({Contact_Legal_Name: currentRow.contact_Legal_Name, Opportunity_Name: currentRow.opportunity_Name, Payment_Type_Name: currentRow.payment_Type_Name,
            Date_Processed: this.datePipe.transform(currentRow.date_Processed), System_Size_kWdc: currentRow.system_Size_kWdc, Amount: this.currencyPipe.transform(currentRow.amount)});
        }
        this.dataSourcePending = new MatTableDataSource(tableArr);
        this.originalDataSourcePending = tableArr;
        this.dataSourcePending.sort = this.sortPending;
        this.dataSourcePending.paginator = this.paginatorPending;
        console.log(this.originalDataSource);
      }
      createTableReclaim() {
        let tableArr: Element[] = [];
        for(let i:number = 0; i <= this.getPendingReclaims.length - 1; i++) {
          let currentRow = this.getPendingReclaims[i];
          tableArr.push({Contact_Legal_Name: currentRow.contact_Legal_Name, Opportunity_Name: currentRow.opportunity_Name, Payment_Type_Name: currentRow.payment_Type_Name,
            Date_Processed: this.datePipe.transform(currentRow.date_Processed), System_Size_kWdc: currentRow.system_Size_kWdc, Amount: this.currencyPipe.transform(currentRow.amount)});
        }
        this.dataSourceReclaim = new MatTableDataSource(tableArr);
        this.originalDataSourceReclaim = tableArr;
        this.dataSourceReclaim.sort = this.sortReclaims;
        this.dataSourceReclaim.paginator = this.paginatorReclaims;
        console.log(this.originalDataSource);
      }
      createTableSales() {
        let tableArr: ElementAlt[] = [];
        for(let i:number = 0; i <= this.getActiveSalesReps.length - 1; i++) {
          let currentRow = this.getActiveSalesReps[i];
          tableArr.push({Contact_Name: currentRow.contact_Name, Sales_Office: currentRow.sales_Office, Title: currentRow.title,
            Start_Date: this.datePipe.transform(currentRow.start_Date), Contact_Phone: currentRow.contact_Phone, Contact_Email: currentRow.contact_Email});
        }
        this.dataSourceSales = new MatTableDataSource(tableArr);
        this.originalDataSourceSales = tableArr;
        this.dataSourceSales.sort = this.sortSales;
        this.dataSourceSales.paginator = this.paginatorSales;
        console.log(this.originalDataSource);
      }
}

export interface Element {
    Contact_Legal_Name: string,
    Opportunity_Name: string,
    Payment_Type_Name:string,
    Date_Processed: string,
    System_Size_kWdc:string,
    Amount: string
}

export interface ElementAlt {
    Contact_Name: string,
    Sales_Office: string,
    Title:string,
    Start_Date: string,
    Contact_Phone:string,
    Contact_Email: string
}