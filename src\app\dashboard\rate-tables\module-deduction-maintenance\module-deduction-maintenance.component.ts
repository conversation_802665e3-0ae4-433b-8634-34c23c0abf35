import { Component, OnInit, ViewChild } from '@angular/core';
import { UntypedFormBuilder, UntypedFormGroup, Validators } from "@angular/forms";
import { ApiService } from "../../../services/api.service";
import { ToastrService } from 'ngx-toastr';
import { dateLessThanDate, maxModuleDeductionDate } from '../../../shared/validators';
import { MatSort } from '@angular/material/sort';
import { MatTableDataSource } from '@angular/material/table';
import { DataSource } from '@angular/cdk/table';
import { MatPaginator } from '@angular/material/paginator';
import { TableFilterPipe } from '../../../pipe/table-filter.pipe';
import { DatePipe, PercentPipe, CurrencyPipe } from '@angular/common';
import { MatDialog } from '@angular/material/dialog';
import { ModuleDeductionMaintenanceDialogComponent } from '../module-deduction-maintenance-dialog/module-deduction-maintenance-dialog.component';

@Component({
  selector: 'app-module-deduction-maintenance',
  templateUrl: './module-deduction-maintenance.component.html',
  styleUrls: ['./module-deduction-maintenance.component.css']
})
export class ModuleDeductionMaintenanceComponent implements OnInit {
  allModuleDeductions: any;
  activeModuleDeductions: any;
  moduleDeductionGroup: any;
  dropdowns: any;
  moduleDeductionForm: UntypedFormGroup;
  addInd: boolean = false;
  salesTerritoryDefault: number = 1;
  moduleTypeDefault: number = 1;
  form: any;
  isReloading :boolean = false ;
  p: number = 1;
  tableArr: Element[] = [];
  moduleDeductionRate1; 
   ismoduleDeductionRateSelected : boolean = false;
  searchText: string = "";
  public date: Date;
  originalDataSource;
  dataSource;
  displayedColumns = [];
  @ViewChild(MatSort, { static: true }) sort: MatSort;
  @ViewChild(MatPaginator, { static: true }) paginator: MatPaginator;

  columnNames = [{
    id: "salesTerritory",
    value: "Sales Territory"

  }, {
    id: "moduleType",
    value: "Module Type"
  },
  {
    id: "effectiveStartDate",
    value: "Effective Start Date"
  },
  {
    id: "effectiveEndDate",
    value: "Effective End Date"
  },
  {
    id: "moduleDeductionRate",
    value: "Module Deduction Rate"
  }];

  constructor(public apiService: ApiService, private toastMsg: ToastrService, private formBuilder: UntypedFormBuilder, private pipe: TableFilterPipe,
    private datePipe: DatePipe, private percentPipe: PercentPipe, private currencyPipe: CurrencyPipe, private dialog: MatDialog) {

  }

  ngOnInit() {
    if (!this.apiService.checkPermission('ViewRateTables')) {
      // this.router.navigate(['/ui/dashboard'])
      this.apiService.goBack();
      this.toastMsg.error("Insufficient Permissions. Please make sure your account can access this information.", "Permissions");
    }
    this.getDropdowns();
    this.getActiveModuleDeductions();
    this.moduleDeductionForm = this.formBuilder.group({
      salesTerritory: [this.salesTerritoryDefault, [Validators.required]],
      moduleType: [this.moduleTypeDefault, [Validators.required]],
      effectiveStartDate: ['', [Validators.required]],
      moduleDeductionRate: [0, [Validators.required, Validators.max(20)]],
    });

    this.onChanges();
  }

  onChanges() {
    this.moduleDeductionForm.valueChanges.subscribe(val => {
      // console.log(this.moduleDeductionForm.errors);
    });
  }

  clearDate(date: HTMLInputElement) {
    date.value = "";
    this.date = null;
    this.moduleDeductionForm.controls.effectiveStartDate.setValue('');
    event.stopPropagation();
  }

  onSubmit() {
    if (!this.moduleDeductionForm.invalid) {
      var values = {
        salesTerritoryId: this.moduleDeductionForm.controls.salesTerritory.value,
        moduleTypeId: this.moduleDeductionForm.controls.moduleType.value,
        effectiveStartDate: this.moduleDeductionForm.controls.effectiveStartDate.value,
        moduleDeductionRate: this.moduleDeductionForm.controls.moduleDeductionRate.value,
      }
   

      var body = {
        newModuleDeduction: values
      }
      // console.log("Body  110 => "+JSON.stringify(body));

     
      this.apiService.post('ModuleDeductionMaintenance', body)
        .subscribe(data => {
          this.toastMsg.success('Module Deduction Successfully Added');
          this.isReloading = true ;
          this.getAllModuleDeductions();
          this.getActiveModuleDeductions();
          this.addInd = !this.addInd;
          // this.tableArr
        }, (err: any) => {
          if (err?.err?.message)
            this.toastMsg.error(err.message, "Server Error!");
          if (typeof err === "string") {
            let isJsonObject = false;            
            let parsedErr: any;
            try {
              parsedErr = JSON.parse(err);
              isJsonObject =parsedErr && typeof parsedErr === "object" &&!Array.isArray(parsedErr);
            } catch (e) {
              isJsonObject = false;
            }
            if (isJsonObject) {
              this.toastMsg.error(parsedErr.result, "Error!");
            } else this.toastMsg.error(err, "Error!");
          }
          if (err?.result) {
            this.toastMsg.error(err.result, "Error!");
          }
        });
    }
  }

 
  getAllModuleDeductions() {
    this.apiService.get('ModuleDeductionMaintenance/retrieveall')
      .subscribe(data => {
        this.allModuleDeductions = data;
        if (!this.isReloading) {
          this.moduleDeductionForm.setValidators([maxModuleDeductionDate(this.allModuleDeductions)]);
        } else {
          this.moduleDeductionForm.clearValidators();
        }

        if (this.moduleDeductionGroup) this.getModuleDeductionGroup(this.moduleDeductionGroup[0]);
      }, (err: any) => {
        this.toastMsg.error(err.message, 'Server Error!');
      });
  }

  getActiveModuleDeductions() {
    this.apiService.get('ModuleDeductionMaintenance/retrieveactive')
      .subscribe(data => {
        this.activeModuleDeductions = data;
        this.displayedColumns = this.columnNames.map(x => x.id);
        this.createTable();
      }, (err: any) => {
        this.toastMsg.error(err.message, 'Server Error!');
      });
  }

  getDropdowns() {
    this.apiService.get('ModuleDeductionMaintenance/dropdowns')
      .subscribe(data => {
        this.dropdowns = data;
        this.getAllModuleDeductions();
        // this.getActiveModuleDeductions();
      }, (err: any) => {
        this.toastMsg.error(err.message, 'Server Error!');
      });
  }

  getModuleDeductionGroup(moduleDeduction: any) {
    // var moduleDeductions = this.allModuleDeductions.filter(x => x.salesTerritoryId === moduleDeduction.salesTerritoryId && x.utilityCompanyId === moduleDeduction.utilityCompanyId && x.financePartnerId === moduleDeduction.financePartnerId && x.purchaseMethod === moduleDeduction.purchaseMethod);
    var moduleDeductions = this.allModuleDeductions.filter(x => x.salesTerritoryId === moduleDeduction.salesTerritoryId && x.moduleTypeId === moduleDeduction.moduleTypeId);
    this.moduleDeductionGroup = moduleDeductions;
  }
  get moduleDeductionRate() { return this.moduleDeductionForm.get('moduleDeductionRate'); }

  rowClick(moduleDeduction: any) { 
    var moduleDeduction = this.allModuleDeductions.filter(x => x.salesTerritoryId === moduleDeduction.salesTerritoryId && x.moduleTypeId === moduleDeduction.moduleTypeId);
    this.moduleDeductionRate1 = moduleDeduction;
    const dialogRef = this.dialog.open(ModuleDeductionMaintenanceDialogComponent, {
      width: '80%', data: { moduleDeduction }
    });
    this.ismoduleDeductionRateSelected = true;
    // console.log("territoryRate = >" + JSON.stringify(this.moduleDeductionRate1));
    // console.log("this.territoryRate  = >" + JSON.stringify(this.moduleDeductionRate1));

    this.moduleDeductionForm.controls['salesTerritory'].setValue(this.moduleDeductionRate1[0].salesTerritoryId);
    this.moduleDeductionForm.controls['moduleType'].setValue(this.moduleDeductionRate1[0].moduleTypeId);
    this.moduleDeductionForm.controls['moduleDeductionRate'].setValue(this.moduleDeductionRate1[0].moduleDeductionRate);
    dialogRef.afterClosed().subscribe(result => {
      // console.log(result);
    });
  }

  
Add(){
  this.moduleDeductionRate1 = this.tableArr;
  this.isReloading = true ;
  if(!this.isReloading)
  {
      this.moduleDeductionForm.setValidators([maxModuleDeductionDate(this.allModuleDeductions)]);
  } else {
    this.moduleDeductionForm.clearValidators();
  }
  this.addInd = !this.addInd;
  // console.log("Add this.moduleDeductionRate1 => 292   " +JSON.stringify(this.moduleDeductionRate1));
  this.moduleDeductionForm.controls['salesTerritory'].setValue(this.moduleDeductionRate1[0].salesTerritoryId);
  this.moduleDeductionForm.controls['moduleType'].setValue(this.moduleDeductionRate1[0].moduleTypeId);
  this.moduleDeductionForm.controls['moduleDeductionRate'].setValue(this.moduleDeductionRate1[0].moduleDeductionRate);
}

  createTable() {
    let tableArr: Element[] = [];
    for (let i: number = 0; i <= this.activeModuleDeductions.length - 1; i++) {
      let currentRow = this.activeModuleDeductions[i];
      if(i == 0){
        this.tableArr[0] =this.activeModuleDeductions[0];
      // console.log(" this.tableArr = 301 > "+ JSON.stringify(this.tableArr));
      }
      tableArr.push({
        activeInd: currentRow.activeInd, effectiveEndDate: this.datePipe.transform(currentRow.effectiveEndDate), effectiveStartDate: this.datePipe.transform(currentRow.effectiveStartDate),
        moduleDeductionId: currentRow.moduleDeductionId, moduleType: currentRow.moduleType, moduleTypeId: currentRow.moduleTypeId, salesTerritory: currentRow.salesTerritory,
        // salesTerritoryId: currentRow.salesTerritoryId, moduleDeductionRate: this.currencyPipe.transform(currentRow.moduleDeductionRate)});
        // Dilip Rate table changes
        salesTerritoryId: currentRow.salesTerritoryId, moduleDeductionRate: this.currencyPipe.transform(currentRow.moduleDeductionRate, "USD", true, "1.3-3")
      });
    }
    this.dataSource = new MatTableDataSource(tableArr);
    this.originalDataSource = tableArr;
    this.dataSource.sort = this.sort;
    this.dataSource.paginator = this.paginator;
  }
  searchForItem(): void {
    let filteredResults: Element[] = [];
    if (this.searchText == '') {
      this.dataSource = new MatTableDataSource(this.originalDataSource);
      this.dataSource.sort = this.sort;
      this.dataSource.paginator = this.paginator;
    } else {
      // filteredResults = this.originalDataSource.filter(option => option.salesTerritory.toLowerCase().includes(this.searchText));
      filteredResults = this.pipe.transform(this.originalDataSource, this.searchText);
      this.dataSource = new MatTableDataSource(filteredResults);
      this.dataSource.sort = this.sort;
      this.dataSource.paginator = this.paginator;
    }
  }
}

export interface Element {
  moduleType: string,
  effectiveStartDate: string,
  effectiveEndDate: string,
  moduleTypeId: number,
  activeInd: boolean,
  moduleDeductionRate: string,
  moduleDeductionId: number,
  salesTerritory: string,
  salesTerritoryId: number
}
