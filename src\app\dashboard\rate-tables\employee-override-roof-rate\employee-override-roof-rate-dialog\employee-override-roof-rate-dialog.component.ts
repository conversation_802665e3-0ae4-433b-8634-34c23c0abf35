import { Component, OnInit, Inject } from '@angular/core';
import {MAT_DIALOG_DATA} from '@angular/material/dialog';
import { MatDialog } from '@angular/material/dialog';
import { MatPaginator } from '@angular/material/paginator';
import { MatSort } from '@angular/material/sort';
import { MatTableDataSource } from '@angular/material/table';

@Component({
  selector: 'app-employee-override-roof-rate-dialog',
  templateUrl: './employee-override-roof-rate-dialog.component.html',
  styleUrls: ['./employee-override-roof-rate-dialog.component.css']
})
export class EmployeeOverrideRoofRateDialogComponent implements OnInit {
  errors: any;
  columnsToDisplay = ["reason", "effectiveStartDate", "effectiveEndDate"];
  constructor(@Inject(MAT_DIALOG_DATA) public data: any, private  dialogRef : MatDialog) { this.errors = data.errors }
  ngOnInit() {
  }

}
