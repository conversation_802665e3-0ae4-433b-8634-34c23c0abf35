import { Component, OnInit, Output, EventEmitter, ViewChild } from '@angular/core';
import { ApiService } from 'src/app/services/api.service';
import { ToastrService } from 'ngx-toastr';
import { IPaymentStatus, IPayment, Payment, UpdatePaymentStatus, UpdatePaymentApproval } from 'src/app/model/payment.model';
import { HttpClient, HttpParams } from '@angular/common/http';
import { ApiResponse } from 'src/app/services/api.response';
import { environment } from 'src/environments/environment';
import { ICommissionType } from 'src/app/model/commission-type.model';
import { IPaymentType } from 'src/app/model/payment-type.model';
import * as FileSaver from 'file-saver';
import { MatDialog } from '@angular/material/dialog';
import { MatPaginator } from '@angular/material/paginator';
import { MatSort } from '@angular/material/sort';
import { MatTableDataSource } from '@angular/material/table';
import { Router, NavigationEnd } from "@angular/router";
import { ConfirmationDialogComponent } from 'src/app/confirmation-dialog/confirmation-dialog.component';
//import { SSL_OP_SSLEAY_080_CLIENT_DH_BUG } from 'constants';

@Component({
  selector: 'app-payment-review',
  templateUrl: './payment-review.component.html',
  styleUrls: ['./payment-review.component.css']
})
export class PaymentReviewComponent implements OnInit {
  @ViewChild(MatSort, { static: true }) sort: MatSort;
  @ViewChild(MatPaginator, { static: true }) paginator: MatPaginator;
  originalPayments: MatTableDataSource<IPayment> = new MatTableDataSource([]);
  payments: MatTableDataSource<IPayment> = new MatTableDataSource([]);
  // pageSizeOptions: number[] = [10, 50, 100, 300, 500];
  // pageSize: number = 50; 
  pageSizeOptions: number[] = [10, 20, 50];  // Dilip
  pageSize: number = 10;
  paymentColumns: string[] = ["selected", "contactName", "opportunityName", "stageName", "paymentTypeName", "commissionTypeName", "commissionRuleName", "amount", "paymentStatusName", "salesDivision", "salesOffice", "planName", "appointmentSetting", "appointmentConfirmed", "actualInstallDate","roofActualInstallDate","batteryActualInstallDate", "dateContractSigned", "processedDate", "paymentDueDate", "purchaseMethod", "employeeStatus", "dateProjectPaidinFull"];
  @Output() updated: EventEmitter<boolean> = new EventEmitter<boolean>();
  searchText: string = "";
  reviewPage: number = 1;
  paymentStatuses: IPaymentStatus[] = <IPaymentStatus[]>[];
  selectedStatusId: number;
  allSelected: boolean = false;
  salesOffices: string[] = [];
  salesDivisions: string[] = [];
  salesOffice: string = null;
  salesDivision: string = null;
  commissionTypeId: string = null;
  commissionTypes: ICommissionType[] = [];
  paymentTypeId: string = null;
  selectedStage: number = 0;
  paymentTypes: IPaymentType[] = [];
  actualInstallStartDateRangeStart: Date = null;
  actualInstallStartDateRangeEnd: Date = null;
  dateContractSignedRangeStart: Date = null;
  dateContractSignedRangeEnd: Date = null;
  paymentCreatedRangeStart: Date = null;
  paymentCreatedRangeEnd: Date = null;
  paymentDueDateRangeStart: Date = null;
  paymentDueDateRangeEnd: Date = null;
  public date: Date;
  filter: boolean = false;
  processDate: Date = new Date();
  notes: string = '';
  commissionStages = ["1", "2", "3", "4", "5", "6", "7", "8", "9", "10", "11"];
  stageNames: string[] = [];
  purchaseMethod: string = null;
  employeeStatus: string = null;
  exclude0Payment: boolean = false;
  exclude0PaymentString: string;
  selectedPaymentData:any=[];
  roofInstallStartDateRangeStart: Date = null;
  roofInstallStartDateRangeEnd: Date = null;
  batteryInstallStartDateRangeStart: Date = null;
  batteryInstallStartDateRangeEnd: Date = null;
  constructor(private apiService: ApiService, private toastMsg: ToastrService, private http: HttpClient, private router: Router,private dialog: MatDialog) { }

  ngOnInit() {
    if (!this.apiService.checkPermission('ApprovePayments')) {
      // this.router.navigate(['/ui/dashboard'])
      this.apiService.goBack();
      this.toastMsg.error("Insufficient Permissions. Please make sure your account can access this information.", "Permissions");
    }
    this.getPaymentStatuses();
    this.getSalesDivisions();
    this.getSalesOffices();
    this.getCommissionTypes();
    this.getPaymentTypes();
  }

  getPaymentStatuses() {
    this.apiService.get('Payments/Statuses')
      .subscribe(data => {
        if (data && data.result) {
          this.paymentStatuses = data.result.map((status: IPaymentStatus) => { return status });

          this.selectedStatusId = this.paymentStatuses.filter(status => status.paymentStatusName == "Created")[0].paymentStatusId;

          this.onChangeStatus();

          // console.log("Payment Statuses", this.paymentStatuses);
        }
      }, err => {
        this.toastMsg.error(err.message, "Error!");
      })
  }

  getPayments(paymentStatusId: number) {    
    let params = new HttpParams();
    params = params.append('paymentStatusId', paymentStatusId != null && paymentStatusId.toString() != 'null' ? paymentStatusId.toString() : '');
    params = params.append('salesDivision', this.salesDivision != null && this.salesDivision != 'null' ? this.salesDivision : '');
    params = params.append('salesOffice', this.salesOffice != null && this.salesOffice != 'null' ? this.salesOffice : '');
    params = params.append('commissionTypeId', this.commissionTypeId != null && this.commissionTypeId.toString() != 'null' ? this.commissionTypeId.toString() : '');
    params = params.append('paymentTypeId', this.paymentTypeId != null && this.paymentTypeId.toString() != 'null' ? this.paymentTypeId.toString() : '');
    params = params.append('actualInstallStartDateRangeStart', this.actualInstallStartDateRangeStart ? this.actualInstallStartDateRangeStart.toString() : '');
    params = params.append('actualInstallStartDateRangeEnd', this.actualInstallStartDateRangeEnd ? this.actualInstallStartDateRangeEnd.toString() : '');
    params = params.append('roofActualInstallStartDateRangeStart', this.roofInstallStartDateRangeStart ? this.roofInstallStartDateRangeStart.toString() : '');
    params = params.append('roofActualInstallStartDateRangeEnd', this.roofInstallStartDateRangeEnd ? this.roofInstallStartDateRangeEnd.toString() : '');
    params = params.append('batteryActualInstallStartDateRangeStart', this.batteryInstallStartDateRangeStart ? this.batteryInstallStartDateRangeStart.toString() : '');
    params = params.append('batteryActualInstallStartDateRangeEnd', this.batteryInstallStartDateRangeEnd ? this.batteryInstallStartDateRangeEnd.toString() : '');
    params = params.append('dateContractSignedRangeStart', this.dateContractSignedRangeStart ? this.dateContractSignedRangeStart.toString() : '');
    params = params.append('dateContractSignedRangeEnd', this.dateContractSignedRangeEnd ? this.dateContractSignedRangeEnd.toString() : '');
    // Payment Created Filters
    params = params.append('paymentCreatedRangeStart', this.paymentCreatedRangeStart ? this.paymentCreatedRangeStart.toString() : '');
    params = params.append('paymentCreatedRangeEnd', this.paymentCreatedRangeEnd ? this.paymentCreatedRangeEnd.toString() : '');
    // Payment Due Date Filters
    params = params.append('paymentDueDateRangeStart', this.paymentDueDateRangeStart ? this.paymentDueDateRangeStart.toString() : '');
    params = params.append('paymentDueDateRangeEnd', this.paymentDueDateRangeEnd ? this.paymentDueDateRangeEnd.toString() : '');

    params = params.append('purchaseMethod', this.purchaseMethod != null && this.purchaseMethod != 'null' ? this.purchaseMethod : '');
    params = params.append('employeeStatus', this.employeeStatus != null && this.employeeStatus != 'null' ? this.employeeStatus : '');
    params = params.append('exclude0Payment', this.exclude0Payment.toString());

    this.http.get<ApiResponse>(`${environment.apiBaseUrl}Payments`, { params: params })
      .subscribe(data => {
        if (data && data.result) {
          console.log("Payments Response", data);          
          var payments = data.result.map((payment: IPayment) => { return payment });
          this.payments = new MatTableDataSource<IPayment>(payments);
          this.originalPayments = new MatTableDataSource<IPayment>(payments);
          this.payments.paginator = this.paginator;
          this.payments.sort = this.sort;
          this.applyFilter(this.searchText);
          this.getStageNames();

          this.filterStages()
          // console.log("Payments", this.payments);
        }
      }, err => {
        this.toastMsg.error(err.message, "Error!");
      })
  }

  getStageNames() {
    let stageSet = new Set<string>();
    for (let i = 0; i < this.originalPayments.data.length; i++) {
      if (this.originalPayments.data[i].fullStageName != null) {
        stageSet.add(String(this.originalPayments.data[i].fullStageName));
      }
    }
    this.stageNames = Array.from(stageSet).sort(function (a, b) {
      let cleanedA = a.match(/\d/g);
      let cleanedB = b.match(/\d/g);
      return Number(cleanedA) - Number(cleanedB)
    });
  }

  updatePaymentStatuses(payments: IPayment[], paymentStatusId: number) {
    let updates: UpdatePaymentStatus[] = payments.map(payment => {
      return <UpdatePaymentStatus>{
        paymentId: payment.paymentId,
        paymentStatusId: paymentStatusId,
        processDate: this.processDate,
        notes: this.notes,
        dateProjectPaidinFull: payment.dateProjectPaidinFull
      }
    });
    let hasPifDate = payments.filter(x => x.dateProjectPaidinFull);
    if ((!hasPifDate || hasPifDate.length == 0) && paymentStatusId == this.approvedStatusId() && payments[0].paymentStatus.paymentStatusId == this.approvedOnHoldStatusId()) {
      this.toastMsg.warning(`${payments.length == 1 ? "Opportunity does" : "Some opportunities"} do not have a valid Paid in Full date. Please note that ${payments.length == 1 ? "the commission for this opportunity" : "commissions for these opportunities"} will not be moved to Approved`);
    }
    else if (hasPifDate && hasPifDate.length > 0 && paymentStatusId == this.approvedOnHoldStatusId()) {
      this.toastMsg.warning(`${payments.length == 1 ? "Opportunity already has" : "Some opportunities already have"} a valid Paid in Full date. Please note that ${payments.length == 1 ? "the commission for this opportunity" : "commissions for these opportunities"} will not be moved to Approved-On Hold.`)
    }
    // Dilip 05/25/2020
    // let pDate:Date = this.processDate; 
    // let updatePaymentDate: UpdatePaymentApproval = {updatePaymentStatus: updates, processDate: pDate};    
    // this.apiService.post('Payments/UpdatePaymentApproval', updatePaymentDate)

    this.apiService.post('Payments/UpdatePaymentStatus', updates)
      .subscribe(data => {
        if(data.result.paymentStatusId != 3){
          this.toastMsg.warning(data.result.message)
        }else{
          this.toastMsg.success(data.result.message)
        }
        this.updated.emit(true);
        this.getPayments(this.selectedStatusId);
      }, err => {
        this.toastMsg.error(err.message, "Error!");
      })
  }

  reopenPaymentStatus(payments: IPayment[], paymentStatusId: number) {
    let updates: UpdatePaymentStatus[] = payments.map(payment => {
      return <UpdatePaymentStatus>{
        paymentId: payment.paymentId,
        paymentStatusId: paymentStatusId,
        processDate: this.processDate,
        notes: this.notes
      }
    });

    this.apiService.post('Payments/ReopenPaymentStatus', updates)
      .subscribe(data => {
        this.toastMsg.success("Successfully update payments");
        this.updated.emit(true);
        this.getPayments(this.selectedStatusId);
      }, err => {
        this.toastMsg.error(err.message, "Error!");
      })
  }

  getCommissionTypes() {
    this.apiService.get('GetData/GetCommissionTypes')
      .subscribe(data => {
        if (data && data.result) {
          this.commissionTypes = data.result.map(type => { return <ICommissionType>{ commissionTypeId: type.id, commissionTypeName: type.name } })
        }
      }, err => {
        this.toastMsg.error(err.message, "Error!");
      })
  }

  getPaymentTypes() {
    this.apiService.get('GetData/GetPaymentTypes')
      .subscribe(data => {
        if (data && data.result) {
          this.paymentTypes = data.result.map(type => { return <IPaymentType>{ paymentTypeId: type.id, paymentTypeName: type.name } })
        }
      }, err => {
        this.toastMsg.error(err.message, "Error!");
      })
  }

  onBulkSelectionChange() {
    this.payments.filteredData.map(bal => bal.selected = this.allSelected);
    if(this.allSelected){
      this.openConfirmationPopup();
    }
  }
  openConfirmationPopup(){
    const dialogRef = this.dialog.open(ConfirmationDialogComponent, {
      disableClose:true,
      width: '28%',
      data: {
        message: "Please note that all the records are selected. Do you want to continue?"
      }
    });
    dialogRef.beforeClosed().subscribe((confirmed) => {
      if (!confirmed) {
        this.allSelected = false;
        if(this.selectedPaymentData.length > 0){
          this.payments.filteredData.filter(obj1 =>
            this.selectedPaymentData.some(obj2 => obj1.paymentId === obj2.paymentId ? obj1.selected = true:obj1.selected = false) 
          );
        }
        else{
          this.payments.filteredData.map(bal => bal.selected = false);
        }
        return false
      }
      this.selectedPaymentData = [];
    });
  }
  onSelectionChange() {
    let balances = this.payments.filteredData.filter(bal => bal.selected);
    this.selectedPaymentData = balances;
    if (balances.length == this.payments.filteredData.length) {
      this.allSelected = true;
    } else {
      this.allSelected = false;
    }
  }

  bulkUpdate(paymentStatusId: number) {
    let payments = this.payments.data.filter(payment => payment.selected);

    this.updatePaymentStatuses(payments, paymentStatusId);
  }

  bulkReopen(paymentStatusId: number) {
    let payments = this.payments.data.filter(payment => payment.selected);

    this.reopenPaymentStatus(payments, paymentStatusId);
  }

  update(payment: IPayment, paymentStatusId: number) {
    this.updatePaymentStatuses([payment], paymentStatusId);
  }

  onChangeStatus() {
    this.getPayments(this.selectedStatusId);
  }

  onChangeFilter() {
    this.getPayments(this.selectedStatusId);
  }

  filterStages() {

    let update: IPayment[] = []
    for (let i = 0; i < this.originalPayments.data.length; i++) {
      try {
        let current: IPayment = this.originalPayments.data[i];
        if (current.fullStageName == null && current.opportunityId > 0) {
          continue;
        }
        if (this.selectedStage == 0) {
          update.unshift(current);
          continue;
        }
        let cleanedCurrent = current.fullStageName.match(/\d/g);
        let cleanedSelected = String(this.selectedStage).match(/\d/g);
        let current1 = 0;
        let selected1 = 0;
        if (cleanedCurrent.length == 2) {
          current1 = Number(cleanedCurrent[0] + cleanedCurrent[1]);
        } else {
          current1 = Number(cleanedCurrent);
        }
        if (cleanedSelected.length == 2) {
          selected1 = Number(cleanedSelected[0] + cleanedSelected[1]);
        } else {
          selected1 = Number(cleanedSelected);
        }
        //if (Number(cleanedCurrent) >= Number(cleanedSelected)) {
        if (current1 >= selected1) {
          update.unshift(current);
        }
      } catch (e) {
        console.log(e);
    } 
    }
    this.payments.data = update;
  }

  // METHOD OVERLOAD
  applyFilter(input: string): void;

  applyFilter(input: Event): void;

  applyFilter(input: any): any {
    var filterValue: string;
    if (typeof input === "string") {
      filterValue = input;
    } else {
      filterValue = (input.target as HTMLInputElement).value;
    }
    this.payments.filter = filterValue.trim().toLowerCase();
  }

  checkSelected() {
    return this.payments && this.payments.data.filter(payment => payment.selected).length > 0;
  }

  checkReviewableStatus() {
    if (this.paymentStatuses && this.paymentStatuses.length > 0) {
      let status = this.paymentStatuses.filter(status => status.paymentStatusId == this.selectedStatusId)[0].paymentStatusName;
      console.log(status)
      return this.selectedStatusId && status == "Created" || status == "On Hold" || status == "Approved-On Hold"
    } else {
      return false;
    }
  }

  IsOnHoldSelected() {
    if (this.paymentStatuses && this.paymentStatuses.length > 0) {
      let status = this.paymentStatuses.filter(status => status.paymentStatusId == this.selectedStatusId)[0].paymentStatusName;
      return this.selectedStatusId && status == "On Hold"
    } else {
      return false;
    }
  }

  IsApprovedOnHoldSelected() {
    if (this.paymentStatuses && this.paymentStatuses.length > 0) {
      let status = this.paymentStatuses.filter(status => status.paymentStatusId == this.selectedStatusId)[0].paymentStatusName;
      return this.selectedStatusId && status == "Approved-On Hold"
    } else {
      return false;
    }
  }

  approvedStatusId() {
    if (this.paymentStatuses) {
      return this.paymentStatuses.filter(status => status.paymentStatusName == "Approved")[0].paymentStatusId;
    }
  }

  approvedOnHoldStatusId() {
    if (this.paymentStatuses) {
      return this.paymentStatuses.filter(status => status.paymentStatusName == "Approved-On Hold")[0].paymentStatusId;
    }
  }

  rejectedStatusId() {
    if (this.paymentStatuses) {
      return this.paymentStatuses.filter(status => status.paymentStatusName == "Rejected")[0].paymentStatusId;
    }
  }

  onHoldStatusId() {
    if (this.paymentStatuses) {
      return this.paymentStatuses.filter(status => status.paymentStatusName == "On Hold")[0].paymentStatusId;
    }
  }

  getSalesDivisions() {
    this.apiService.get('GetData/SalesDivisions')
      .subscribe(data => {
        if (data && data.result) {
          this.salesDivisions = data.result.map(div => { return <string>div });
        }
      }, err => {
        this.toastMsg.error(err.message, "Error!");
      });
  }

  getSalesOffices() {
    this.apiService.get('GetData/SalesOffices')
      .subscribe(data => {
        if (data && data.result) {
          this.salesOffices = data.result.map(office => { return <string>office });
        }
      }, err => {
        this.toastMsg.error(err.message, "Error!");
      });
  }

  getNumberSelected(): number {
    if (this.payments) return this.payments.data.filter(payment => payment.selected).length;
  }

  getSelectedSum(): number {
    if (this.payments) return this.payments.data.filter(payment => payment.selected).reduce((sum, current) => sum + current.amount, 0);
  }

  getPaymentsWorksheet() {
    var paymentIds = this.payments.filteredData.map(p => { return p.paymentId });

    var body = {
      paymentIds: paymentIds
    }

    this.http.post(`${environment.apiBaseUrl}Payments/PaymentsWorksheet`, body, { responseType: 'blob' })
      .subscribe(data => {
        this.downLoadFile(data, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;");
      }, err => {
        this.toastMsg.error(err.message, "Error!");
      });
  }

  /**
   * Method is use to download file.
   * @param data - Array Buffer data
   * @param type - type of the document.
   */
  downLoadFile(data: any, type: string) {
    let blob = new Blob([data], { type: type });

    FileSaver.saveAs(blob, 'Payments_Export.xlsx');
  }

  openSalesrep(id) {
    const url = `#/ui/commissions/salesrep/${id}`;
    window.open(url, '_blank');
  }

  openOpportunity(id) {
    const url = `#/ui/commissions/opportunitydetails/${id}`;
    window.open(url, '_blank');
  }

  clearDate(date: HTMLInputElement) {
    date.value = "";
    this.date = null;    
        event.stopPropagation();
      }
}
