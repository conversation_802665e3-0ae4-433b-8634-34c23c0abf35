import { Component, OnInit, Inject } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { ApiService } from '../../../services/api.service';
import { ToastrService } from 'ngx-toastr';

@Component({
  selector: 'app-installation-type-deduction-maintenance-dialog',
  templateUrl: './installation-type-deduction-maintenance-dialog.component.html',
  styleUrls: ['./installation-type-deduction-maintenance-dialog.component.css']
})
export class InstallationTypeDeductionMaintenanceDialogComponent implements OnInit {
  installationTypeDeductionGroup: Element[] = [];
  constructor(public dialogRef: MatDialogRef<InstallationTypeDeductionMaintenanceDialogComponent>,
    private apiService: ApiService, private toastMsg: ToastrService, @Inject(MAT_DIALOG_DATA) public data: any) { }


  ngOnInit() {
    this.installationTypeDeductionGroup = this.data.installationTypeDeduction;
    this.installationTypeDeductionGroup.sort((a, b) => {
      return <any>new Date(b.effectiveStartDate) - <any>new Date(a.effectiveStartDate);
    });
  }

}

export interface Element {
  salesTerritory: string,
  effectiveStartDate: string,
  effectiveEndDate: string,
  installationTypeDeduction: string
}


