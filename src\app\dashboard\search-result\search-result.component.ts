import { Component, OnInit, ViewChild, ViewEncapsulation } from '@angular/core';
import { MatPaginator } from '@angular/material/paginator';
import { MatSort } from '@angular/material/sort';
import { MatTableDataSource } from '@angular/material/table';
import { ActivatedRoute, Router } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { ApiService } from 'src/app/services/api.service';
import { IDropdownSettings } from 'ng-multiselect-dropdown';
import { UntypedFormControl } from '@angular/forms';
import { HttpClient } from '@angular/common/http';
@Component({
  selector: 'app-search-result',
  templateUrl: './search-result.component.html',
  styleUrls: ['./search-result.component.css'],
  encapsulation: ViewEncapsulation.None
})
export class SearchResultComponent implements OnInit {

  keyword:string;
  opportunityColumns: string[] = [ 'opportunityName', 'dlNumber', 'stage','stageStaus','systemSizeKWdc', 'opportunityType', 'salesTerritory', 'utilityCompany'];
  opportunityDataSource:any;
  contactColumns: string[] = ['contactName','contactEmail','contactPhone','contactLegalName','salesDivision', 'sales_Office','solarPro','employmentStatus','startDate']
  contactDataSource:any;
  ruleColumns: string[] = ['commissionRuleId','commissionRuleName','commissionRuleDescription','noReclaim','numberOfBonuses', 'ruleType'];
  ruleDataSource:any;
  formulaColumns: string[] = ['formulaId','formulaName','formulaDescription'];
  formulaDataSource:any;
  planColumns: string[] = ['planHeaderId','planName','planDescription','effectiveStartDate','effectiveEndDate'];
  planDataSource:any;
  filter:boolean = false;
  searchText:string=''
  salesTerritory: any;
  salesOffices: string[] = [];
  actualInstallStartDateRangeStart: Date = null;
  actualInstallStartDateRangeEnd: Date = null;
  dateContractSignedRangeStart: Date = null;
  dateContractSignedRangeEnd: Date = null;
  demoDateStart:Date = null;
  demoDateEnd:Date = null;
  dropdownList = [];
  selectedItems = []
  dropdownSettings:IDropdownSettings = {};
  filterArray:any[] = [];
  multiselectControl = new UntypedFormControl([]);
  disableSpinner:boolean = false;
  isEnableLoader:boolean = true;
  promptMessage:string;
  dataSource = [];

  @ViewChild('opportunityMatSort',{ read: MatSort, static: true }) opportunityMatSort: MatSort;
  @ViewChild('contactMatSort',{ read: MatSort, static: true }) contactMatSort: MatSort;
  @ViewChild('ruleMatSort',{ read: MatSort, static: true }) ruleMatSort: MatSort;
  @ViewChild('formulaMatSort',{ read: MatSort, static: true }) formulaMatSort: MatSort;
  @ViewChild('plaMatSort',{ read: MatSort, static: true }) plaMatSort: MatSort;

  @ViewChild('opportunityPaginator',{static:true}) opportunityPaginator: MatPaginator;
  @ViewChild('contactPaginator',{static:true}) contactPaginator: MatPaginator;
  @ViewChild('rulePaginator',{static:true}) rulePaginator: MatPaginator;
  @ViewChild('formulaPaginator',{static:true}) formulaPaginator: MatPaginator;
  @ViewChild('planPaginator',{static:true}) planPaginator: MatPaginator;  

  constructor(private toastMsg: ToastrService,private activatedRoute: ActivatedRoute,
    private apiService: ApiService,private http:HttpClient,
    private router: Router) {
  }

  ngOnInit() {
    this.activatedRoute.params.subscribe(params => {
      this.keyword = params.keyword;
      this.activatedRoute.queryParams.subscribe(qparams =>{
        let data = {
        keyword: params.keyword, 
        viewRule: JSON.parse(qparams.viewRule), 
        viewOpportunity: JSON.parse(qparams.viewOpportunity), 
        viewContact: JSON.parse(qparams.viewContact), 
        viewPlan: JSON.parse(qparams.viewPlan),
        viewBaseFormula: JSON.parse(qparams.viewBaseFormula),
        salesTerritory:null,
        actualInstallStartDateFrom:null,
        actualInstallStartDateTo:null,
        demoDateStartFrom:null,
        demoDateStartTo:null,
        datecontractsignedFrom:null,
        datecontractsignedTo:null      
      }
      this.disableSpinner = false;
      this.apiService.hide();
      this.apiService.post("SearchContent/viewAllResults",data)
      .subscribe( (res:any) => {
          let opportunityDataSource = [];
          let contactDataSource = [];
          let ruleDataSource = [];
          let formulaDataSource = [];
          let planDataSource = [];
          this.opportunityDataSource = null;
          this.contactDataSource = null;
          this.ruleDataSource = null;
          this.formulaDataSource = null;
          this.planDataSource = null;
          res.result.forEach(element => {
            switch(element.objectType){
              case "Opportunity":
                let opportunityData = element.opportunities;
                opportunityData.objectId = element.objectId;
                opportunityDataSource.push(opportunityData);
                break;
              case "Contact":
                let contactsData = element.contacts;
                contactsData.objectId = element.objectId;
                contactDataSource.push(contactsData);
                break;
              case "CommissionRule":
                let ruleData = element.rules;
                ruleData.objectId = element.objectId;
                ruleData.ruleType = element.ruleType;
                ruleDataSource.push(ruleData);
                break;
              case "Formula":
                let formulaData = element.baseFormulas;
                formulaData.objectId = element.objectId;
                formulaDataSource.push(formulaData);
                break;
              case "Plan":
                let planData = element.plans;
                planData.objectId = element.objectId;
                planDataSource.push(planData);
                break;
              default:
            }
          });
          this.disableSpinner = true;
          if(opportunityDataSource.length > 0){
            this.opportunityDataSource = new MatTableDataSource<any>(opportunityDataSource);
            this.opportunityDataSource.sort = this.opportunityMatSort;
            this.opportunityDataSource.paginator = this.opportunityPaginator;
          }
          if(contactDataSource.length > 0){
            this.contactDataSource = new MatTableDataSource<any>(contactDataSource);
            this.contactDataSource.sort = this.contactMatSort;
            this.contactDataSource.paginator = this.contactPaginator;
          }
          if(ruleDataSource.length > 0){
            this.ruleDataSource = new MatTableDataSource<any>(ruleDataSource);
            this.ruleDataSource.sort = this.ruleMatSort;
            this.ruleDataSource.paginator = this.rulePaginator;
          }
          if(formulaDataSource.length > 0){
            this.formulaDataSource = new MatTableDataSource<any>(formulaDataSource);
            this.formulaDataSource.sort = this.formulaMatSort;
            this.formulaDataSource.paginator = this.formulaPaginator;
          }
          if(planDataSource.length > 0){
            this.planDataSource = new MatTableDataSource<any>(planDataSource);
            this.planDataSource.sort = this.plaMatSort;
            this.planDataSource.paginator = this.planPaginator;
          }
          if(res && res.result.length == 0){
            this.promptMessage = 'No Records Found';
             this.disableSpinner = true;
          }
          else{
            this.promptMessage = '';
          }
        },(err: any) => {
          this.isEnableLoader = false;
          this.disableSpinner = false;
          console.log(err);
        }
      );
    })
    })
    this.dropdownSettings = {
      singleSelection: false,
      idField: 'item_id',
      textField: 'item_text',
      selectAllText: 'Select All',
      unSelectAllText: 'UnSelect All',
      enableCheckAll: false,
      itemsShowLimit: 1,
      allowSearchFilter: false
    };
    this.dropdownList = [
      { item_id: 0, item_text: 'All' },
      { item_id: 1, item_text: 'BaseFormula' },
      { item_id: 2, item_text: 'Contact' },
      { item_id: 3, item_text: 'Opportunity' },
      { item_id: 4, item_text: 'Plan' },
      { item_id: 5, item_text: 'Rule' }
    ];
    this.selectedItems=[{ item_id: 0, item_text: 'All' }]  
    this.multiselectControl.patchValue(this.selectedItems);
    this.filterArray = this.selectedItems;
    this.getTerritory();
  }
  viewOpportunity() {
    localStorage.setItem("searchedParam", "opportunity");
  }

  getTerritory() {
    this.apiService.get('GetData/SalesOffices')
      .subscribe(data => {
        if (data && data.result) {
          this.salesOffices = data.result.map(office => { return <string>office });
        }
      }, err => {
        this.toastMsg.error(err.message, "Error!");
      });
    }
    onItemSelect(item: any) {
      if (item.item_text == 'All') {
        this.multiselectControl.patchValue([item]);
        this.filterArray = [item]
      } else {
        this.filterArray = this.filterArray.filter(obj => obj.item_text !== 'All');
        this.filterArray.push(item);
        this.multiselectControl.patchValue(this.filterArray);
        if (this.filterArray.length == 5) {
          this.filterArray = [{ item_id: 0, item_text: 'All' }]
          this.multiselectControl.patchValue(this.filterArray);
        }
      }
    }

    onSelectAll(items: any) {
      this.filterArray = items;
    }

    onDeSelect(item) {
      if (item.item_text == 'All') {
        this.multiselectControl.patchValue([item]);
        this.filterArray = [item];
      } else if (this.filterArray.length == 1) {
        this.multiselectControl.patchValue([item]);
        this.filterArray = [item];
      }
      else {
        this.filterArray = this.filterArray.filter(obj => obj.item_id !== item.item_id);
      }
    }

    onSearch(){
      
      let data;      
      if(this.salesTerritory =="" || this.salesTerritory =="null" || this.salesTerritory == undefined){
        this.salesTerritory = null;
      }
      if (this.filterArray.length == 1 && this.filterArray[0].item_text == 'All') {        
        data={
          keyword: this.searchText, 
        viewRule: true, 
        viewOpportunity: true, 
        viewContact: true, 
        viewPlan: true,
        viewBaseFormula: true,
        salesTerritory:this.salesTerritory,
        actualInstallStartDateFrom:this.actualInstallStartDateRangeStart,
        actualInstallStartDateTo:this.actualInstallStartDateRangeEnd,
        demoDateStartFrom:this.demoDateStart,
        demoDateStartTo:this.demoDateEnd,
        datecontractsignedFrom:this.dateContractSignedRangeStart,
        datecontractsignedTo:this.dateContractSignedRangeEnd
        }
      }else{
        const namesToCheck = ['BaseFormula', 'Contact', 'Opportunity', 'Plan', 'Rule'];
      const resultObject: any = {};
      namesToCheck.forEach(name => {
        resultObject[name] = this.filterArray.some(obj => obj.item_text === name);
      });      
      data={
        keyword: this.searchText, 
      viewRule: resultObject.Rule, 
      viewOpportunity: resultObject.Opportunity, 
      viewContact: resultObject.Contact, 
      viewPlan: resultObject.Plan,
      viewBaseFormula: resultObject.BaseFormula,
      salesTerritory:this.salesTerritory,
      actualInstallStartDateFrom:this.actualInstallStartDateRangeStart,
      actualInstallStartDateTo:this.actualInstallStartDateRangeEnd,
      demoDateStartFrom:this.demoDateStart,
      demoDateStartTo:this.demoDateEnd,
      datecontractsignedFrom:this.dateContractSignedRangeStart,
      datecontractsignedTo:this.dateContractSignedRangeEnd
      }      
      }  
      this.disableSpinner = false;    
      this.apiService.post("SearchContent/viewAllResults",data)
      .subscribe( (res:any) => {
          let opportunityDataSource = [];
          let contactDataSource = [];
          let ruleDataSource = [];
          let formulaDataSource = [];
          let planDataSource = [];
          this.opportunityDataSource = null;
          this.contactDataSource = null;
          this.ruleDataSource = null;
          this.formulaDataSource = null;
          this.planDataSource = null;
          res.result.forEach(element => {
            switch(element.objectType){
              case "Opportunity":
                let opportunityData = element.opportunities;
                opportunityData.objectId = element.objectId;
                opportunityDataSource.push(opportunityData);
                break;
              case "Contact":
                let contactsData = element.contacts;
                contactsData.objectId = element.objectId;
                contactDataSource.push(contactsData);
                break;
              case "CommissionRule":
                let ruleData = element.rules;
                ruleData.objectId = element.objectId;
                ruleData.ruleType = element.ruleType;
                ruleDataSource.push(ruleData);
                break;
              case "Formula":
                let formulaData = element.baseFormulas;
                formulaData.objectId = element.objectId;
                formulaDataSource.push(formulaData);
                break;
              case "Plan":
                let planData = element.plans;
                planData.objectId = element.objectId;
                planDataSource.push(planData);
                break;
              default:
            }
          });
          this.disableSpinner = true;
          if(opportunityDataSource.length > 0){
            this.opportunityDataSource = new MatTableDataSource<any>(opportunityDataSource);
            this.opportunityDataSource.sort = this.opportunityMatSort;
            this.opportunityDataSource.paginator = this.opportunityPaginator;
          }
          if(contactDataSource.length > 0){
            this.contactDataSource = new MatTableDataSource<any>(contactDataSource);
            this.contactDataSource.sort = this.contactMatSort;
            this.contactDataSource.paginator = this.contactPaginator;
          }
          if(ruleDataSource.length > 0){
            this.ruleDataSource = new MatTableDataSource<any>(ruleDataSource);
            this.ruleDataSource.sort = this.ruleMatSort;
            this.ruleDataSource.paginator = this.rulePaginator;
          }
          if(formulaDataSource.length > 0){
            this.formulaDataSource = new MatTableDataSource<any>(formulaDataSource);
            this.formulaDataSource.sort = this.formulaMatSort;
            this.formulaDataSource.paginator = this.formulaPaginator;
          }
          if(planDataSource.length > 0){
            this.planDataSource = new MatTableDataSource<any>(planDataSource);
            this.planDataSource.sort = this.plaMatSort;
            this.planDataSource.paginator = this.planPaginator;
          }
          if(res && res.result.length == 0){
            this.promptMessage = 'No Records Found';
             this.disableSpinner = true;
          }
          else{
            this.promptMessage = '';
          }
        },(err: any) => {
          console.log(err);
        }
      );

    }

    reset(){
      this.searchText="";
      this.filterArray=[{ item_id: 0, item_text: 'All' }];
      this.multiselectControl.patchValue(this.filterArray);
      this.salesTerritory='';
      this.actualInstallStartDateRangeStart=null;
      this.actualInstallStartDateRangeEnd =null;
      this.demoDateStart=null;
      this.demoDateEnd=null;
      this.dateContractSignedRangeStart=null;
      this.dateContractSignedRangeEnd=null;
      
    }
  viewContact() {
    localStorage.setItem("searchedParam", "contact");
  }

  viewRule() {
    localStorage.setItem("searchedParam", "rule");
  }
  getViewUrl(rule: any):string{
    let url = '../../../../'
    if (rule.ruleType == "Base Pay Structure") {
      url = url.concat("ui/commissions/viewBasePayStructure");
    } else if (rule.ruleType == "Payment Book") {
      url = url.concat("ui/commissions/viewPaymentBook");
    } else if (rule.ruleType == "Base Pay" || rule.ruleType == "Kilowatt Incentives"
      || rule.ruleType == "Self-Gen Install Incentive Goal" || rule.ruleType == "Outreach High Flyer" || rule.ruleType == "Direct High Flyer"
      || rule.ruleType == "Traditional President's Club" || rule.ruleType == "Bonus" || rule.ruleType == "Employee Incentive" || rule.ruleType == "Outreach - EOM 10/1/10 Incentive" || rule.ruleType == "Outreach - EOM Existing Incentive"
      || rule.ruleType == "Traditional High Flyer" || rule.ruleType == "Outreach - EOM Old DM Pay Incentive" || rule.ruleType == "Outreach - EOM Old DM Pay Unlimited Incentive" || rule.ruleType == "Direct President's Club") {
        url = url.concat("ui/commissions/viewRule");
    } else {
      url = url.concat("ui/commissions/viewRule");
    }
    return url;
  }

  viewPlan() {
    localStorage.setItem("searchedParam", "plan");
  }

  viewBaseFormula() {
    // this.router.navigate(["ui/commissions/viewBaseFormula", formulaId]);
    localStorage.setItem("searchedParam", "baseFormula");
  }

  test(event:any){
    console.log(event);
    console.log(this.opportunityDataSource);
  }

}
