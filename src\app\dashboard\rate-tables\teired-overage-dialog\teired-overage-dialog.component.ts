import { Component, OnInit, Inject } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { ApiService } from '../../../services/api.service';
import { ToastrService } from 'ngx-toastr';

@Component({
  selector: 'app-teired-overage-maintenance-dialog',
  templateUrl: './teired-overage-dialog.component.html',
  styleUrls: ['./teired-overage-dialog.component.css']
})
export class TieredOverageMaintenanceDialogComponent implements OnInit {
  tieredOverageGroup: Element[] = [];
  tieredOverageSelectedGroup: any;
  
  constructor(public dialogRef: MatDialogRef<TieredOverageMaintenanceDialogComponent>,
    private apiService: ApiService, private toastMsg: ToastrService, @Inject(MAT_DIALOG_DATA) public data: any) { }


  ngOnInit() {
    this.tieredOverageGroup = this.data.tieredOverageBonusRate;
    this.tieredOverageGroup.sort((a, b) => {
      return <any>new Date(b.effectiveStartDate) - <any>new Date(a.effectiveStartDate);
    });
    console.log(this.tieredOverageGroup);
  }

  groupClick(group: any) {
    this.tieredOverageSelectedGroup = group;
  }

}
export interface Element {
  effectiveStartDate: string,
  effectiveEndDate: string
}

