import { Component, OnInit, ViewChild } from '@angular/core';
import { UntypedFormBuilder, FormGroup, Validators } from "@angular/forms";
import { ApiService } from "../../../services/api.service";
import { ToastrService } from 'ngx-toastr';
import { MatSort } from '@angular/material/sort';
import { MatTableDataSource } from '@angular/material/table';
import { DataSource } from '@angular/cdk/table';
import { MatPaginator } from '@angular/material/paginator';
import { Router, ActivatedRoute } from '@angular/router';
import { TableFilterPipe } from '../../../pipe/table-filter.pipe';
import { DatePipe, PercentPipe, CurrencyPipe } from '@angular/common';
import { MatDialog } from '@angular/material/dialog';
import { HttpParams, HttpClient } from '@angular/common/http';
import { ApiResponse } from '../../../services/api.response';
import { environment } from '../../../../environments/environment';
import { IRuleVersion } from '../../../model/rule-version';
import * as FileSaver from 'file-saver';

@Component({
    selector: 'app-rule-versions',
    templateUrl: './rule-versions-component.html',
    styleUrls: ['./rule-versions-component.css']
})
export class RuleVersionsComponent implements OnInit {
    pageSizeOptions: number[] = [10, 20, 50];
    pageSize: number = 10;
  ruleId: number = 0;
  selectedVersion: number = 0;
    ruleVersionsDataSource: MatTableDataSource<IRuleVersion> = new MatTableDataSource([]);
  ruleVersionCols: string[] = ["selected", "versionNumber", "ruleName", "createdBy", "createdDate", "active"];
    @ViewChild(MatSort, { static: true }) sort: MatSort;
    @ViewChild(MatPaginator, { static: true }) paginator: MatPaginator;

    constructor(public apiService: ApiService, private toastMsg: ToastrService, private formBuilder: UntypedFormBuilder, private pipe: TableFilterPipe, private http: HttpClient,
        private activatedRoute: ActivatedRoute, private datePipe: DatePipe, private percentPipe: PercentPipe, private currencyPipe: CurrencyPipe, private dialog: MatDialog) {
    }

    ngOnInit() {
        this.activatedRoute.params.subscribe(params => {
            this.ruleId = params.rule_id;
           
        });

        this.getRuleVersions();
        if (!this.apiService.checkPermission('ViewRateTables')) {
            this.apiService.goBack();
            this.toastMsg.error("Insufficient Permissions. Please make sure your account can access this information.", "Permissions");
        }

    }

  viewRuleVersion(versionNo) {
    const url = `#/ui/commissions/viewRule/${this.ruleId}/${versionNo}`;
    window.open(url, '_self');
  }

  checkSelected() {
    return this.selectedVersion > 0;
    }

    getNumberSelected(): number {
        if (this.ruleVersionsDataSource) return this.ruleVersionsDataSource.data.filter(record => record.selected).length;
    }

    onSelectionChange() {
      let balances = this.ruleVersionsDataSource.filteredData.filter(bal => bal.selected);
    }

  setSelectedVersion() {
    
      var body = {
        ruleId: this.ruleId,
        versionNumber: this.selectedVersion
      }

      this.apiService.post('rule/SetActiveVersion', body)
        .subscribe(data => {
          this.toastMsg.success('Selected version ' + this.selectedVersion + ' activated successfully');
          this.getRuleVersions();
          
        }, (err: any) => {
          this.toastMsg.error(err.message, 'Server Error!');
        });
    }


    getRuleVersions() {
       
      this.http.get<ApiResponse>(`${environment.apiBaseUrl}/rule/ruleVersions/${this.ruleId}`)
            .subscribe(data => {
                if (data && data.result) {
                    var ruleVersionsDataSource = data.result.map((rows: IRuleVersion) => { return rows });
                    this.ruleVersionsDataSource = new MatTableDataSource<IRuleVersion>(ruleVersionsDataSource);
                    this.ruleVersionsDataSource.paginator = this.paginator;
                    this.ruleVersionsDataSource.sort = this.sort;
                }
            }, err => {
                this.toastMsg.error(err.message, "Error!");
            })
    }
}

export interface Element {

  versionNumber: number,
  ruleName: string,
  active: boolean,
  createdBy: string,
  createdDate: string
}

