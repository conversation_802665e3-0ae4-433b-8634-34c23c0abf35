import { Component, OnInit, Inject, ViewChild } from '@angular/core';
import { ApiService } from 'src/app/services/api.service';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { ToastrService } from 'ngx-toastr';
import { IRuleData } from 'src/app/model/rule-data.model';
import { IPrompt } from 'src/app/model/prompt.model';
import { IPlanDetail } from 'src/app/model/plan.model';
import { IRule, IStep } from 'src/app/model/rule.model';
import { IRulePrompt } from 'src/app/model/rule-prompt.model';
import { PaymentBookSchedulePromptComponent } from '../payment-book-schedule-prompt/payment-book-schedule-prompt.component';

@Component({
  selector: 'app-payment-book-schedule-dialog',
  templateUrl: './payment-book-schedule-dialog.component.html',
  styleUrls: ['./payment-book-schedule-dialog.component.css']
})
export class PaymentBookScheduleDialogComponent implements OnInit {
  @ViewChild(PaymentBookSchedulePromptComponent) paymentBookSchedulePrompt: PaymentBookSchedulePromptComponent;
  selectedRuleId: number = null;
  prompts: IRule;
  promptsValues: object = {};
  addOn: AddOnRule;

  constructor(public dialogRef: MatDialogRef<PaymentBookScheduleDialogComponent>, @Inject(MAT_DIALOG_DATA) public data: IPaymentBookScheduleDialogData, private apiService: ApiService, private toastMsg: ToastrService) { }

  ngOnInit() {
  }

  onSelect() {
    // console.log(this.selectedRuleId);
    this.prompts = null;
    this.addOn = null;
    var rulePrompts = this.data.paymentBookRules[0].rules.filter(p => p.ruleId == this.selectedRuleId)[0];
    //console.log(rulePrompts);
    rulePrompts.ruleTypeName = "Payment Book Schedule";
    if (rulePrompts) {
      this.prompts = rulePrompts;
      this.addOn = <AddOnRule>rulePrompts;
      this.addOn.promptValues = {};
    }
  }

  onRulePromptChange(prompt: IRulePrompt) {
    //alert(prompt);
    this.addOn.promptValues[prompt.commissionRuleId] = prompt;
     console.log("Prompts:", this.promptsValues);
  }

  checkAllPromptsEntered() {
    if (this.paymentBookSchedulePrompt && this.paymentBookSchedulePrompt.ruleItems && this.paymentBookSchedulePrompt.ruleItems.find(r => r.ruleItemInputs.find(rii => rii.columnName != "End_Date" && (rii.columnValue == null || rii.columnValue == "") && rii.columnValue !== 0) != null) != null) {
      return false;
    } else if (!this.paymentBookSchedulePrompt || !this.paymentBookSchedulePrompt.ruleItems) {
      return false;
    } else {
      return true;
    }
  }

  checkValidDates() {
    var valid = true;

    var startDate: Date;
    var endDate: Date;

    if (this.paymentBookSchedulePrompt && this.paymentBookSchedulePrompt.ruleItems) {
      startDate = this.paymentBookSchedulePrompt.ruleItems.find(x => x.ruleItemInputs.find(y => y.columnName == "Start_Date") != null).ruleItemInputs.find(y => y.columnName == "Start_Date").columnValue;
  
      endDate = this.paymentBookSchedulePrompt.ruleItems.find(x => x.ruleItemInputs.find(y => y.columnName == "End_Date") != null).ruleItemInputs.find(y => y.columnName == "End_Date").columnValue;
    }
    
    if (startDate && endDate && endDate < startDate) {
      valid = false;
    }

    if (!startDate || (startDate && !endDate)) {
      valid = true;
    }
    
    return valid;
  }

}

export interface IPaymentBookScheduleDialogData {
  paymentBookRules: IPlanDetail[];
  prompts: IPrompt[];
}

export class AddOnRule implements IRule {
  ruleId: number;
  ruleName: string;
  promptAssignPlan: boolean;
  ruleTypeId: number;
  ruleTypeName: string;
  description: string;
  formulaId: number;
  metadataTypeId: number;
  tableSchema: string;
  identifier: string;
  tableName: string;
  columnName: string;
  displayName: string;
  noReclaim: boolean;
  effectiveStartDate: any;
  effectiveEndDate: any;
  paymentRuleTypeId: number;
  triggerRuleTypeId: number;
  sqlQuery: string;
  paymentBookTypeId: number;
  overdrawLimit: number;
  weeklyPay: number;
  fallbackRuleId: number;
  prompts: IPrompt[];
  steps: IStep[];
  numberOfBonuses: number;
  isProcessing: boolean;
  isInclusion: boolean;
  inclusionEffectiveStartDate: string;
  inclusionEffectiveEndDate: string;
  promptValues: object;
}