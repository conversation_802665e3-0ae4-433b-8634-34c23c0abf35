import { DatePipe, DecimalPipe } from '@angular/common';
import { Component, OnInit, ViewChild } from '@angular/core';
import { UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { MatDialog } from '@angular/material/dialog';
import { MatPaginator } from '@angular/material/paginator';
import { MatSort } from '@angular/material/sort';
import { MatTableDataSource } from '@angular/material/table';
import { IDropdownSettings } from 'ng-multiselect-dropdown';
import { ToastrService } from 'ngx-toastr';
import { ApiService } from 'src/app/services/api.service';
import { groupBy } from 'src/app/shared/group-by';
import { SplitCommissionRatesDialogComponent } from '../split-commission-rates-dialog/split-commission-rates-dialog.component';

@Component({
  selector: 'app-split-commission-rates',
  templateUrl: './split-commission-rates.component.html',
  styleUrls: ['./split-commission-rates.component.css']
})
export class SplitCommissionRatesComponent implements OnInit {
  columnNames = [
    {
      id: "salesDivision",
      value: "Sales Division"
    },
    {
      id: "stateCode",
      value: "State"
    },
    {
      id: "team",
      value: "Team"
    },
    {
      id: "productType",
      value: "Product Type"
    },
    {
      id: "splitType",
      value: "Split Type"
    },
    {
      id: "splitTypePrimaryValue",
      value: "Primary Split Value"
    },
    {
      id: "splitTypeSecondaryValue",
      value: "Secondary Split Value"
    },
    {
      id: "splitDescription",
      value: "Split Description"
    },
    {
      id: "effectiveStartDate",
      value: "Effective Start Date",
      dataType: 'Date'
    },
    {
      id: "effectiveEndDate",
      value: "Effective End Date",
      dataType: 'Date'
    }];
  displayedColumns = [];
  tableArr: any[] = [];
  dataSource: any;
  originalDataSource: any;
  salesDivisions = ["Sales- Direct", "Sales- Traditional", "Sales- Outreach", "Sales- Inside"];
  teamData = ["Roofing", "Non Roofing"];
  dateColumns: any;
  splitCommissionData: any;
  splitCommissionAllData: any;
  splitCommission: any;
  stateData: string[] = [];
  productTypeData: any[] = [];
  dropdownSettings: IDropdownSettings = {};
  addRow: boolean = false;
  editRow: boolean = false;
  addSplitCommission: UntypedFormGroup;
  editSplitCommission: UntypedFormGroup;
  disableDateField: boolean = false
  @ViewChild(MatSort, { static: true }) sort: MatSort;
  @ViewChild(MatPaginator, { static: true }) paginator: MatPaginator;
  constructor(private formBuilder: UntypedFormBuilder, public apiService: ApiService, private toastMsg: ToastrService, private datePipe: DatePipe,
    private decimalPipe: DecimalPipe, private dialog: MatDialog) { }

  ngOnInit(): void {
    this.displayedColumns = this.columnNames.map(x => x.id);
    this.dateColumns = this.columnNames.filter(s => s.dataType == 'Date');
    this.getState();
    this.getProductType()
    this.getSplitCommission();
    this.getSplitAllCommission();
    this.initializeForm();
  }
  getSplitCommission() {
    this.apiService.get('SplitCommission/RetrieveActive')
      .subscribe(data => {
        this.splitCommissionData = data;
        this.createTable();
      }, err => {
        this.toastMsg.error(err.message, "Error!");
      })
  }
  getSplitAllCommission() {
    this.apiService.get('SplitCommission/RetrieveAll')
      .subscribe(data => {
        this.splitCommissionAllData = data;
      }, err => {
        this.toastMsg.error(err.message, "Error!");
      })
  }
  getState() {
    this.apiService.get('GetData/StateCode')
      .subscribe(data => {
        if (data && data.result) {
          this.stateData = data.result.map(office => { return <string>office });
        }
      }, err => {
        this.toastMsg.error(err.message, "Error!");
      });
  }
  getProductType() {
    this.apiService.get('getdata/ProductType')
      .subscribe(data => {
        console.log(data)
        if (data && data.result) {
          this.productTypeData = data.result;
        }
      }, err => {
        this.toastMsg.error(err.message, "Error!");
      });
  }
  initializeForm() {
    this.addSplitCommission = this.formBuilder.group({
      splitCommissionId: [0],
      splitType: ["", [Validators.required]],
      splitTypePrimaryValue: [null, [Validators.required]],
      splitTypeSecondaryValue: [null, [Validators.required]],
      stateCode: [""],
      salesDivision: [""],
      productType: [0],
      team: [""],
      splitDescription: [""],
      effectiveStartDate: ["", [Validators.required]],
      effectiveEndDate: [null]
    });
    this.editSplitCommission = this.formBuilder.group({
      splitCommissionId: [{ value: 0, disabled: true }],
      splitType: [{ value: "", disabled: true }],
      splitTypePrimaryValue: [{ value: "", disabled: true }],
      splitTypeSecondaryValue: [{ value: "", disabled: true }],
      stateCode: [{ value: "", disabled: true }],
      salesDivision: [{ value: "", disabled: true }],
      productType: [{ value: 0, disabled: true }],
      team: [{ value: "", disabled: true }],
      splitDescription: [{ value: "", disabled: true }],
      effectiveStartDate: [{ value: "", disabled: true }],
      effectiveEndDate: [{ value: "" }]
    });
  }
  createTable() {
    let tableArr: any[] = [];
    for (let i: number = 0; i <= this.splitCommissionData.length - 1; i++) {
      let currentRow = this.splitCommissionData[i];
      if (i == 0) {
        this.tableArr[0] = this.splitCommissionData[0];
      }
      tableArr.push({
        splitCommissionId: currentRow.splitCommissionId, splitType: currentRow.splitType,
        splitTypePrimaryValue: currentRow.splitTypePrimaryValue,
        splitTypeSecondaryValue: currentRow.splitTypeSecondaryValue,
        salesDivision: currentRow.salesDivision,
        stateCode: currentRow.stateCode,
        productType: currentRow.productType,
        team: currentRow.team,
        splitDescription: currentRow.splitDescription,
        effectiveStartDate: this.datePipe.transform(currentRow.effectiveStartDate),
        effectiveEndDate: this.datePipe.transform(currentRow.effectiveEndDate),
      });
    }
    this.dataSource = new MatTableDataSource(tableArr);
    this.originalDataSource = tableArr;
    this.dataSource.sort = this.sort;
    this.dataSource.paginator = this.paginator;
  }
  add() {
    this.addRow = !this.addRow;
  }
  rowClick(event: any) {
    this.editRow = true;
    this.addRow = false;
    var splitCommission = this.splitCommissionAllData.filter(x => x.salesDivision === event.salesDivision && x.stateCode === event.stateCode && x.team == event.team && x.productType === event.productType);
    this.splitCommission = splitCommission;
    splitCommission = Object.values(groupBy(splitCommission, 'effectiveStartDate'));
    const dialogRef = this.dialog.open(SplitCommissionRatesDialogComponent, {
      width: '80%', data: { splitCommission }
    });
    if (this.splitCommission && this.splitCommission.length > 0) {
      this.editSplitCommission.controls['splitCommissionId'].setValue(event.splitCommissionId);
      this.editSplitCommission.controls['splitType'].setValue(event.splitType);
      this.editSplitCommission.controls['splitTypePrimaryValue'].setValue(event.splitTypePrimaryValue);
      this.editSplitCommission.controls['splitTypeSecondaryValue'].setValue(event.splitTypeSecondaryValue);
      this.editSplitCommission.controls['salesDivision'].setValue(event.salesDivision);
      this.editSplitCommission.controls['stateCode'].setValue(event.stateCode);
      this.editSplitCommission.controls['productType'].setValue(event.productType);
      this.editSplitCommission.controls['team'].setValue(event.team);
      this.editSplitCommission.controls['splitDescription'].setValue(event.splitDescription);
      this.editSplitCommission.controls['effectiveStartDate'].setValue(this.datePipe.transform(event.effectiveStartDate, 'yyyy-MM-dd'));
      this.editSplitCommission.controls['effectiveEndDate'].setValue(this.datePipe.transform(event.effectiveEndDate, 'yyyy-MM-dd'));
      if (event.effectiveEndDate) {
        this.disableDateField = true;
        this.editSplitCommission.controls['effectiveEndDate'].disable();
      }
      else {
        this.disableDateField = false;
        this.editSplitCommission.controls['effectiveEndDate'].enable();
      }
    }
    dialogRef.afterClosed().subscribe(result => {
    });
  }
  clearAddStartDate(date: HTMLInputElement) {
    date.value = "";
    this.addSplitCommission.controls.effectiveStartDate.setValue('');
  }
  clearAddEndDate(date: HTMLInputElement) {
    date.value = null;
    this.addSplitCommission.controls.effectiveEndDate.setValue(null);
  }
  clearEditEndDate(date: HTMLInputElement) {
    date.value = "";
    this.editSplitCommission.controls.effectiveEndDate.setValue('');
  }
  onSubmit() {
    var values = {
      splitCommissionId: this.addSplitCommission.controls.splitCommissionId.value,
      splitType: this.addSplitCommission.controls.splitType.value,
      splitTypePrimaryValue: this.addSplitCommission.controls.splitTypePrimaryValue.value,
      splitTypeSecondaryValue: this.addSplitCommission.controls.splitTypeSecondaryValue.value,
      salesDivision: this.addSplitCommission.controls.salesDivision.value,
      stateCode: this.addSplitCommission.controls.stateCode.value,
      productTypeId: this.addSplitCommission.controls.productType.value,
      team: this.addSplitCommission.controls.team.value,
      splitDescription: this.addSplitCommission.controls.splitDescription.value,
      effectiveStartDate: this.addSplitCommission.controls.effectiveStartDate.value,
      effectiveEndDate: this.addSplitCommission.controls.effectiveEndDate.value,
    }

    if (values.splitTypePrimaryValue + values.splitTypeSecondaryValue > 100 && values.splitType === "Percentage") {
      this.toastMsg.error("The sum of both split percentages cannot exceed 100%");
    }
    else {
      this.apiService.post('SplitCommission/AddSplitCommission', values)
      .subscribe(data => {
        this.toastMsg.success('Split Commission Added Successfully');
        this.addRow = !this.addRow;
        this.getSplitCommission();
        this.getSplitAllCommission();
        this.initializeForm();
      }, (err: any) => {
          if (err?.err?.message)
            this.toastMsg.error(err.message, "Server Error!");
          if (typeof err === "string") {
            let isJsonObject = false;            
            let parsedErr: any;
            try {
              parsedErr = JSON.parse(err);
              isJsonObject =parsedErr && typeof parsedErr === "object" &&!Array.isArray(parsedErr);
            } catch (e) {
              isJsonObject = false;
            }
            if (isJsonObject) {
              this.toastMsg.error(parsedErr.result, "Error!");
            } else this.toastMsg.error(err, "Error!");
          }
          if (err?.result) {
            this.toastMsg.error(err.result, "Error!");
          }
        });
    }
  }
  onEditSubmit() {
    var values = {
      splitCommissionId: this.editSplitCommission.controls.splitCommissionId.value,
      effectiveEndDate: this.editSplitCommission.controls.effectiveEndDate.value,
    }
    this.apiService.post('SplitCommission/UpdateSplitCommission', values)
      .subscribe(data => {
        this.toastMsg.success('Split Commission Updated Successfully');
        this.editRow = !this.editRow;
        this.getSplitCommission();
        this.getSplitAllCommission();
        this.initializeForm();
      }, (err: any) => {
          if (err?.err?.message)
            this.toastMsg.error(err.message, "Server Error!");
          if (typeof err === "string") {
            let isJsonObject = false;            
            let parsedErr: any;
            try {
              parsedErr = JSON.parse(err);
              isJsonObject =parsedErr && typeof parsedErr === "object" &&!Array.isArray(parsedErr);
            } catch (e) {
              isJsonObject = false;
            }
            if (isJsonObject) {
              this.toastMsg.error(parsedErr.result, "Error!");
            } else this.toastMsg.error(err, "Error!");
          }
          if (err?.result) {
            this.toastMsg.error(err.result, "Error!");
          }
        });
  }
}
