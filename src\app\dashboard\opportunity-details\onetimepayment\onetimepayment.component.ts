import { Component, OnInit, Input, SimpleChanges, ViewChild, ViewEncapsulation } from '@angular/core';
import { ApiService } from 'src/app/services/api.service';
import { ToastrService } from 'ngx-toastr';
import { ICommission } from 'src/app/model/commission.model';
import {MatSort} from '@angular/material/sort';
import {MatTableDataSource} from '@angular/material/table';
import { DataSource } from '@angular/cdk/table';
import {MatPaginator} from '@angular/material/paginator';
import { TableFilterPipe } from '../../../pipe/table-filter.pipe';
import { CurrencyPipe } from '@angular/common';
import { UntypedFormGroup, UntypedFormBuilder, Validators } from '@angular/forms';
import { IContact, IRuleType, ICommissionProcessingOneTimePayment } from 'src/app/model/one-time-payment.model'
// import { ThrowStmt } from '@angular/compiler';
import { Observable, OperatorFunction } from 'rxjs';
import { debounceTime, distinctUntilChanged, map, filter } from 'rxjs/operators';

@Component({
  selector: 'app-onetimepayment',
  templateUrl: './onetimepayment.component.html',
  encapsulation: ViewEncapsulation.None,
  styleUrls: ['./onetimepayment.component.css']
})
export class OneTimePaymentComponent implements OnInit {
  @Input() opportunityId: number;
  commissions: ICommission[] = [];
  p: number = 1;

  originalDataSource;
  dataSource;
  displayedColumns = [];
  @ViewChild(MatSort, {static: true}) sort: MatSort;
  @ViewChild(MatPaginator, { static: true }) paginator: MatPaginator;
  
  oneTimePaymentFormGroup: UntypedFormGroup;
  // One Time Payment
  commissionRuleTypeId: number = null;
  commissionRuleTypes: IRuleType[] = [];
  contacts: IContact[] = [];
  commissionProcessingOneTimePayment: ICommissionProcessingOneTimePayment[] = [];

  public model: IContact;

  formatter = (c: IContact) => c.contactName;
  search: OperatorFunction<string, readonly { contactId, contactName }[]> = (text$: Observable<string>) => text$.pipe(
    debounceTime(0),
    distinctUntilChanged(),
    filter(term => term.length >= 2),
    map(term => this.contacts.filter(c => new RegExp(term, 'mi').test(c.contactName)).slice(0, 10))
  )


  columnNames = [{
    id: "details",
    value: "Details"

  }, {
    id: "paidTo",
    value: "Paid To"
  },
  {
    id: "calculatedVia",
    value: "Calculated Via (Plan/Rule)"
  },
  {
    id: "commissionAmount",
    value: "Commission Amount"
  }];

  constructor(private apiService: ApiService, private toastMsg: ToastrService, private formBuilder: UntypedFormBuilder, private currencyPipe: CurrencyPipe) { }

  ngOnInit() {
    this.oneTimePaymentFormGroup = this.formBuilder.group({
      contactId: ["0", [Validators.required]],
      commissionRuleTypeId: ["0", [Validators.required]],
      amount: [null],
      notes: [null]
    });
    this.getCommissionRulesOneTimePayment();
    this.getContacts();
    this.oneTimePaymentFormGroup.controls.amount.setValue(0);
    this.oneTimePaymentFormGroup.controls.commissionRuleTypeId.setValue(0);
    this.oneTimePaymentFormGroup.controls.amount.setValidators([Validators.required]);
    this.oneTimePaymentFormGroup.controls.amount.updateValueAndValidity();
    this.oneTimePaymentFormGroup.controls.commissionRuleTypeId.updateValueAndValidity();

  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes.opportunityId) {
      
    }
  }

  getCommissionRulesOneTimePayment() {
    this.apiService.get('GetData/GetCommissionRulesOneTimePayment')
      .subscribe(data => {
        if (data && data.result) {
          this.commissionRuleTypes = data.result.map(type => { return <IRuleType>{ commissionRuleTypeId: type.id, commissionRuleTypeName: type.name } })
          this.oneTimePaymentFormGroup.controls.commissionRuleTypeId.setValue("0");
        }
      }, err => {
        this.toastMsg.error(err.message, "Error!");
      })
  }

  getContacts() {
    this.apiService.get('GetData/GetActiveContacts')
      .subscribe(data => {
        if (data && data.result) {
          this.contacts = data.result.map(type => { return <IContact>{ contactId: type.id, contactName: type.name } })
          this.oneTimePaymentFormGroup.controls.contactId.setValue("0");
        }
      }, err => {
        this.toastMsg.error(err.message, "Error!");
      })
  }
clearOneTimePaymentsFields(){
  this.oneTimePaymentFormGroup.controls.contactId.setValue('0');
  this.oneTimePaymentFormGroup.controls.commissionRuleTypeId.setValue(0);
  this.oneTimePaymentFormGroup.controls.amount.setValue(0);
  this.oneTimePaymentFormGroup.controls.notes.setValue(null);
}

  onSubmitOneTimePayment() {

    if (this.oneTimePaymentFormGroup.controls.commissionRuleTypeId.value == "0") {
      this.toastMsg.error("Select Commission Rule.");
      return;
    }

    let form = <ICommissionProcessingOneTimePayment>{
      contactId: this.oneTimePaymentFormGroup.controls.contactId.value.contactId,
      commissionRuleId: this.oneTimePaymentFormGroup.controls.commissionRuleTypeId.value,
      amount: this.oneTimePaymentFormGroup.controls.amount.value,
      opportunityId: this.opportunityId,
      notes: this.oneTimePaymentFormGroup.controls.notes.value
    }
   
    this.apiService.post('Commissions/OneTimePayment', form)
      .subscribe(data => {
        this.toastMsg.success("One Time Payment Successfully Created!");
        this.clearOneTimePaymentsFields();
      }, (err: any) => {
        this.toastMsg.error(err.message, "Server Error!");
      });
  }

}

