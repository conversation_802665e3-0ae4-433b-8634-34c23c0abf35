import { Component, OnInit, Inject } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { ApiService } from '../../../services/api.service';
import { ToastrService } from 'ngx-toastr';

@Component({
  selector: 'app-module-deduction-maintenance-dialog',
  templateUrl: './module-deduction-maintenance-dialog.component.html',
  styleUrls: ['./module-deduction-maintenance-dialog.component.css']
})
export class ModuleDeductionMaintenanceDialogComponent implements OnInit {
  moduleDeductionGroup: Element[] =[];
  constructor(public dialogRef: MatDialogRef<ModuleDeductionMaintenanceDialogComponent>,
    private apiService: ApiService, private toastMsg: ToastrService, @Inject(MAT_DIALOG_DATA) public data: any) { }


  ngOnInit() {
    this.moduleDeductionGroup = this.data.moduleDeduction;
    this.moduleDeductionGroup.sort((a, b) => {
      return <any>new Date(b.effectiveStartDate) - <any>new Date(a.effectiveStartDate);
    });
  }

}

export interface Element {
  salesTerritory: string,
  moduleType: string,
  effectiveStartDate: string,
  effectiveEndDate: string,
  moduleDeductionRate: string,
}


