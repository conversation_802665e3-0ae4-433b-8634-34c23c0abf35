import { Component, Input, OnInit, ViewChild } from '@angular/core';
import { MatPaginator } from '@angular/material/paginator';
import { MatSort } from '@angular/material/sort';
import { MatTableDataSource } from '@angular/material/table';
import { Chart } from 'angular-highcharts';
import { IPendingPayment, IdateRange } from '../../models/models';
import { DatePipe, CurrencyPipe } from '@angular/common';
import { ToastrService } from 'ngx-toastr';
import { ApiService } from 'src/app/services/api.service';
import { DatezonePipe } from 'src/app/pipe/datezone.pipe';

interface CustomPoint extends Highcharts.Point {
  custom: any;
}
@Component({
  selector: 'app-kilowatt',
  templateUrl: './kilowatt.component.html',
  styleUrls: ['./kilowatt.component.css']
})
export class KilowattComponent {
  displayedColumns = [];
  kiloWattData: any;
  salesDivisionChartData: any;
  salesTerittoryChartData: any;
  originalDataSource: any;
  paymentDataSource: any;
  @ViewChild('table', { read: MatSort, static: true }) sort: MatSort;
  @ViewChild('paginator', { static: true }) paginator: MatPaginator;
  salesTerittoryChart: Chart;
  salesDivisionChart: Chart;
  @Input() dateRange: IdateRange | null = null;
  @Input() tabNumber: number | null = null;
  previousDateRange: IdateRange | null = null;

  columnNames = [
    {
      id: "contactLegalName",
      value: "Contact Legal Name"
    },
    {
      id: "opportunityName",
      value: "Opportunity Name"
    },
    {
      id: "amount",
      value: "Amount"
    },
    {
      id: "projectStatus",
      value: "Project Status"
    },
    {
      id: "salesTerritoryStateCode",
      value: "State"
    },
    {
      id: "salesDivision",
      value: "Sales Division"
    },
    {
      id: "actualInstallDate",
      value: "Actual Install Date",
      dataType:'Date'
    },
    {
      id: "systemSizekWdc",
      value: "System Size"
    },
  ];

  constructor(public apiService: ApiService, private toastMsg: ToastrService, private datePipe: DatePipe,private currencyPipe: CurrencyPipe,private dateZonePipe: DatezonePipe) { }

  ngOnChanges(){
    if (this.tabNumber === 8) {
      if (this.dateRange) {
        if (this.previousDateRange === null || this.previousDateRange !== this.dateRange) {
          this.previousDateRange = this.dateRange;
          this.getKiloWatt();
        }       
      }
    }    
  }

  getKiloWatt() {
    this.apiService.get(`BusinessDashboard/KilowattsData?toDate=${this.dateRange.endDate}&fromDate=${this.dateRange.startDate}`)
      .subscribe((res: any) => {
        this.kiloWattData = res.kiloWattList;
        this.salesDivisionChartData = res.salesDivision;        
        this.salesTerittoryChartData = res.kilowWatts;        
        this.displayedColumns = this.columnNames.map(x => x.id);
        this.createTable();        
        this.getSalesDivisionChart();        
        this.getTeritorryChart();
        
      }, (err: any) => {
        this.toastMsg.error(err.message, "Server Error!");
      });
  }

  createTable() {
    let tableArr: any[] = [];
    for (let i: number = 0; i <= this.kiloWattData.length - 1; i++) {
      let currentRow = this.kiloWattData[i];
      tableArr.push({
        contactId: currentRow.trinity_Salesperson_Id, amount: currentRow.opportunity_Amount, contactLegalName: currentRow.contact_Legal_Name,
        opportunityId: currentRow.opportunity_Id, opportunityName: currentRow.opportunity_Name, projectStatus: currentRow.project_Status,
        salesDivision: currentRow.sales_Division, salesTerritoryStateCode: currentRow.sales_Territory_State_Code,
        actualInstallDate: this.dateZonePipe.transform(currentRow.actual_Install_Date),systemSizekWdc: currentRow.system_Size_kWdc
      });
    }
    this.paymentDataSource = new MatTableDataSource(tableArr);
    this.originalDataSource = tableArr;
    this.paymentDataSource.sort = this.sort;
    this.paymentDataSource.paginator = this.paginator;
  }
  onSortChange(event:any){
    let dateFieldData = this.columnNames.filter(s=>s.dataType === 'Date');
    let dateField = dateFieldData && dateFieldData.length > 0 ? dateFieldData.filter(s=>s.id === event.active).map(c=> c.id).toString():'';
    if(dateField){
      this.paymentDataSource.sortingDataAccessor = (item, property) => {
        switch (property) {
          case dateField:
            return new Date(item[dateField]).toISOString();
          default:
            return item[property];
        }
      };

      this.paymentDataSource.sortingFn = (a: any, b: any, active: string, direction: string) => {
        if (active === dateField) {
          const dateA = new Date(a);
          const dateB = new Date(b);
          if (direction === 'asc') {
            return dateA.getTime() - dateB.getTime();
          } else {
            return dateB.getTime() - dateA.getTime();
          }
        } else {
          return this.paymentDataSource.sortingDataAccessor(a, active) > this.paymentDataSource.sortingDataAccessor(b, active) ? 1 : -1;
        }
      };
    }
    
    
  }
  getSalesDivisionChart() {
    this.salesDivisionChart = this.setChartData('Sales Divisions', this.salesDivisionChartData);
  }

  getTeritorryChart(){
    this.salesTerittoryChart = this.setChartData('States', this.salesTerittoryChartData);
  }

  setChartData(title: string, chartData: any) {
    let chart = new Chart({
      chart: {
        plotBackgroundColor: null,
        plotBorderWidth: null,
        plotShadow: false,
      },
      title: {
        text: title
      },
      tooltip: {
        pointFormatter: function() {
          const point = this as CustomPoint;
          return `Total: <b>${point.y.toLocaleString("en-US")} KW</b> <br/> Total Count : ${point.custom.toLocaleString("en-US")}<b></b>`;
        },
      },
      accessibility: {
        point: {
          valueSuffix: '%',
        },
      },
      credits: {
        enabled: false
      },
      legend: {
        maxHeight: 90,  
      },
      plotOptions: {
        pie: {
          allowPointSelect: true,
            innerSize: '50%',
            cursor: 'pointer',
            dataLabels: {
                enabled: true,
            },
            showInLegend: true
        },
      },
      series: [
        {
          type: 'pie',
          name: 'Total',
          showInLegend: true,
          data: chartData
        }
      ]
    });
    return chart;    
  }
}
