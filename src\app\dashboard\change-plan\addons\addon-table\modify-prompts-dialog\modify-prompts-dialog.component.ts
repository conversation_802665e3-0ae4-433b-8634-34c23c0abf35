import { Component, OnInit, Inject } from '@angular/core';
import { IRule } from 'src/app/model/rule.model';
import { AddOnRule } from '../../../employee-incentive-dialog/employee-incentive-dialog.component';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { ApiService } from 'src/app/services/api.service';
import { ToastrService } from 'ngx-toastr';
import { IRulePrompt } from 'src/app/model/rule-prompt.model';
import { IPlanDetail } from 'src/app/model/plan.model';
import { IPrompt } from 'src/app/model/prompt.model';
import { root } from 'rxjs/internal-compatibility';

@Component({
  selector: 'app-modify-prompts-dialog',
  templateUrl: './modify-prompts-dialog.component.html',
  styleUrls: ['./modify-prompts-dialog.component.css']
})
export class ModifyPromptsDialogComponent implements OnInit {
  addOn: AddOnRule;
  datesValid: boolean = true;
  promptsFilled: boolean = true;

  constructor(public dialogRef: MatDialogRef<ModifyPromptsDialogComponent>, @Inject(MAT_DIALOG_DATA) public data: IModifyPromptDialogData, private apiService: ApiService, private toastMsg: ToastrService) { }

  ngOnInit() {
  }

  onRulePromptChange(prompt: IRulePrompt) {
    if (!this.addOn) this.addOn = <AddOnRule>this.data.rule;

    this.addOn.promptValues[prompt.commissionRuleId] = prompt;

    if (this.data.rule.ruleTypeName == "Employee Incentive") {
      this.datesValid = this.checkValidEmployeeIncentiveDates(prompt.ruleItems);
      this.promptsFilled = this.checkAllEmployeeIncentivePromptsEntered(prompt.ruleItems);
    } else if (this.data.rule.ruleTypeName == "Base Pay Structure") {
      this.datesValid = this.checkValidBasePayStructureDates(prompt.ruleItems);
      this.promptsFilled = this.checkAllBasePayStructurePromptsEntered(prompt.ruleItems);
    } else if (this.data.rule.ruleTypeName == "Payment Book Schedule") {
      this.datesValid = this.checkValidPaymentBookScheduleDates(prompt.ruleItems);
      this.promptsFilled = this.checkAllPaymentBookSchedulePromptsEntered(prompt.ruleItems);
    }
  }

  checkValidEmployeeIncentiveDates(ruleItems) {
    var valid = true;

    var startDate: Date;
    var endDate: Date;

    if (ruleItems) {
      startDate = ruleItems.find(x => x.ruleItemInputs.find(y => y.columnName == "Start_Date") != null).ruleItemInputs.find(y => y.columnName == "Start_Date").columnValue;
  
      endDate = ruleItems.find(x => x.ruleItemInputs.find(y => y.columnName == "End_Date") != null).ruleItemInputs.find(y => y.columnName == "End_Date").columnValue;
    }
    
    if (startDate && endDate && endDate < startDate) {
      valid = false;
    }

    if (!startDate || (startDate && !endDate)) {
      valid = true;
    }

    return valid;
  }

  checkAllEmployeeIncentivePromptsEntered(ruleItems) {
    if (ruleItems && ruleItems.find(r => r.ruleItemInputs.find(rii => rii.columnName != "End_Date" && (rii.columnValue == null || rii.columnValue == "") && rii.columnValue !== 0) != null) != null) {
      return false;
    } else if (!ruleItems) {
      return false;
    } else {
      return true;
    }
  }

  checkAllBasePayStructurePromptsEntered(ruleItems) {
    if (ruleItems && ruleItems.find(r => r.ruleItemInputs.find(rii => rii.columnValue == null || rii.columnValue == "") != null) != null) {
      return false;
    } else if (!ruleItems) {
      return false;
    } else {
      return true;
    }
  }

  checkAllPaymentBookSchedulePromptsEntered(ruleItems) {
    if (ruleItems && ruleItems.find(r => r.ruleItemInputs.find(rii => (rii.columnValue == null || rii.columnValue == "") && rii.columnName == "Start_Date") != null) != null) {
      return false;
    } else if (!ruleItems) {
      return false;
    } else {
      return true;
    }
  }

  checkValidBasePayStructureDates(ruleItems) {
    var valid = true;

    ruleItems.forEach((item, i) => {
      var next = ruleItems[i + 1];

      if (next == null || next.ruleItemInputs == null || next.ruleItemInputs.find(x => x.columnName == "Start_Date") == null) return;

      if (item.ruleItemInputs && item.ruleItemInputs.find(x => x.columnName == "Start_Date") != null && item.ruleItemInputs.find(x => x.columnName == "Start_Date").columnValue >= next.ruleItemInputs.find(x => x.columnName == "Start_Date").columnValue) {
        valid = false;
      }
    });

    return valid;
  }

  checkValidPaymentBookScheduleDates(ruleItems) {
    var valid = true;

    ruleItems.forEach((item, i) => {
      var next = ruleItems[i + 1];

      if (next == null || next.ruleItemInputs == null || next.ruleItemInputs.find(x => x.columnName == "Start_Date") == null) return;

      if (item.ruleItemInputs && item.ruleItemInputs.find(x => x.columnName == "Start_Date") != null && item.ruleItemInputs.find(x => x.columnName == "Start_Date").columnValue >= next.ruleItemInputs.find(x => x.columnName == "Start_Date").columnValue) {
        valid = false;
      }
    });

    return valid;
  }

}

export interface IModifyPromptDialogData {
  rule: IRule;
}
