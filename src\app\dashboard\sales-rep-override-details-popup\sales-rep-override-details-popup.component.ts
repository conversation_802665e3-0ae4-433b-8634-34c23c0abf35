import { Component, Inject, OnInit, ViewChild } from '@angular/core';
import { MAT_DIALOG_DATA } from '@angular/material/dialog';
import { MatPaginator } from '@angular/material/paginator';
import { MatSort } from '@angular/material/sort';
import { MatTableDataSource } from '@angular/material/table';
import { IOverrideDetails } from '../../model/IOverrideDetails';
import { CurrencyPipe } from '@angular/common';
import { TableFilterPipe } from 'src/app/pipe/table-filter.pipe';

@Component({
  selector: 'app-sales-rep-override-details-popup',
  templateUrl: './sales-rep-override-details-popup.component.html',
  styleUrls: ['./sales-rep-override-details-popup.component.css']
})
export class SalesRepOverrideDetailsPopupComponent implements OnInit {
 overrideDetails: Array<IOverrideDetails>;
 originalTotalAmount: any;
 totalAmount: any;
 @ViewChild(MatSort, { static: true }) sort: MatSort;
 @ViewChild(MatPaginator, {static: true}) paginator: MatPaginator;
 dataSource;
 originalDataSource;
 employeeName: string;
 tableArr: IOverrideDetails[] = [];
 displayedColumns = [];
 columnNames = [{
  id: "monthYear",
  value: "Month-Year"
 },
 {
  id: "salesDivision",
  value: "Sales Division"
 },
 {
  id: "salesOffice",
  value: "Sales Office"
 },
 {
  id: "rate",
  value: "Rate"
 },
 {
  id: "systemSize",
  value: "System Size"
 },
 {
  id: "totalAmount",
  value: "Total Amount"
 }];
 searchText: string = "";
 monthFilter: any;

  constructor(
    @Inject(MAT_DIALOG_DATA) public data: any,
    private currencyPipe: CurrencyPipe,
    private pipe: TableFilterPipe,
    ) 
    {
      this.overrideDetails = data.overrides;
    }
 
  ngOnInit() {
    this.displayedColumns = this.columnNames.map(x => x.id);
    this.employeeName = this.overrideDetails[0].employeeName;
    this.createTable();
  }


  createTable() {
    let tableArr: any[] = [];
    var total = 0;
    for (let i: number = 0; i <= this.overrideDetails.length - 1; i++) {
      let currentRow = this.overrideDetails[i];
      if(i==0)
      {
        this.tableArr[0] =this.overrideDetails[0];
      }
      total+= Number(currentRow.totalAmount);
      tableArr.push({
        monthYear: currentRow.monthYear, salesDivision: currentRow.salesDivision,
        salesOffice: currentRow.salesOffice, rate: currentRow.rate, systemSize: currentRow.systemSize,
        totalAmount: this.currencyPipe.transform(currentRow.totalAmount)
      });
    }
    this.totalAmount = this.currencyPipe.transform(total);
    this.originalTotalAmount = this.currencyPipe.transform(total);
    this.dataSource = new MatTableDataSource(tableArr);
    this.originalDataSource = tableArr;
    this.dataSource.sort = this.sort;
    this.dataSource.paginator = this.paginator;
  }

  searchForItem(event: any): void {
    var total = 0;
    this.clearDate(event)
    let filteredResults: any[] = [];
    if (this.searchText == '') {
      this.dataSource = new MatTableDataSource(this.originalDataSource);
      this.totalAmount = this.originalTotalAmount;
      this.dataSource.sort = this.sort;
      this.dataSource.paginator = this.paginator;
    } else {
      filteredResults = this.pipe.transform(this.originalDataSource, this.searchText);
      this.dataSource = new MatTableDataSource(filteredResults);
      filteredResults.forEach(x => {total+= Number(x.totalAmount.replace("$", "").replace(",", ""))});
      this.totalAmount = this.currencyPipe.transform(total);
      this.dataSource.sort = this.sort;
      this.dataSource.paginator = this.paginator;
    }
  }

  onMonthChange(date: any): void {
    var total = 0;
    this.searchText = "";
    const dateOptions: Intl.DateTimeFormatOptions = {
      month: 'short'
    };
    var m = new Date('January 1, 1901, 12:00:00');
    m.setMonth(Number(date.toString().substring(date.toString().length-2))-1)
    var search = m.toLocaleString('en-US', dateOptions);
    search += "-" + date.toString().substring(0, 4)
    console.log(search)
    let filteredResults: any[] = [];
    if (!search) {
      this.dataSource = new MatTableDataSource(this.originalDataSource);
      this.totalAmount = this.originalTotalAmount;
      this.dataSource.sort = this.sort;
      this.dataSource.paginator = this.paginator;
    } else {
      filteredResults = this.pipe.transform(this.originalDataSource, search);
      filteredResults.forEach(x => {total+= Number(x.totalAmount.replace("$", "").replace(",", ""))});
      this.totalAmount = this.currencyPipe.transform(total);
      this.dataSource = new MatTableDataSource(filteredResults);
      this.dataSource.sort = this.sort;
      this.dataSource.paginator = this.paginator;
    }
  }

  clearDate(date: HTMLInputElement) {
    date.value = "";   
    this.dataSource = new MatTableDataSource(this.originalDataSource);
    this.totalAmount = this.originalTotalAmount;
    this.dataSource.sort = this.sort;
    this.dataSource.paginator = this.paginator;
    event.stopPropagation();
  }

}

