import { C<PERSON><PERSON>cyPipe, DatePipe } from '@angular/common';
import { Component, Input, OnInit, ViewChild } from '@angular/core';
import { MatPaginator } from '@angular/material/paginator';
import { MatSort } from '@angular/material/sort';
import { MatTableDataSource } from '@angular/material/table';
import { ToastrService } from 'ngx-toastr';
import { ApiService } from 'src/app/services/api.service';
import { IdateRange } from '../../models/models';
import { Chart } from 'angular-highcharts';
import { DatezonePipe } from 'src/app/pipe/datezone.pipe';

interface CustomPoint extends Highcharts.Point {
  custom: any;
}
interface CustomSeries extends Highcharts.Series {
  color: any;
}

@Component({
  selector: 'app-breakdown-product',
  templateUrl: './breakdown-product.component.html',
  styleUrls: ['./breakdown-product.component.css']
})
export class BreakdownProductComponent implements OnInit {
  displayedColumns = [];
  breakdownData: any;
  originalDataSource: any;
  dataSource: any;
  @ViewChild('table', { read: MatSort, static: true }) sort: MatSort;
  @ViewChild('paginator', { static: true }) paginator: MatPaginator;
  @Input() dateRange: IdateRange |any;
  @Input() tabNumber: number | null = null;
  previousDateRange: IdateRange | null = null;
  salesDivisionChart: Chart;
  salesDivisionChartData: any;
  paymentMethodChart: Chart;
  paymentMethodChartData: any;
  category:string = "S";
  columnNames = [
    {
      id: "productType",
      value: "Product Type"
    },
    {
      id: "contactLegalName",
      value: "Contact Legal Name"
    },
    {
      id: "opportunityName",
      value: "Opportunity Name"
    },
    {
      id: "amount",
      value: "Amount"
    },
    {
      id: "paymentStatus",
      value: "Payment Status"
    },
    {
      id: "paymentType",
      value: "Payment Type"
    },
    {
      id: "salesDivision",
      value: "Sales Division"
    },
    {
      id: "modifiedDate",
      value: "Modified Date",
      dataType:'Date'
    },
  ];
  constructor(public apiService: ApiService, private toastMsg: ToastrService, private datePipe: DatePipe,private currencyPipe: CurrencyPipe,private dateZonePipe: DatezonePipe) { }

  ngOnInit() {
  }
  ngOnChanges(){
    if (this.tabNumber === 5) {
      if (this.dateRange) {
        if (this.previousDateRange === null || this.previousDateRange !== this.dateRange) {
          this.previousDateRange = this.dateRange;
          this.getBreakdownData()
        }       
      }
    }
  }
  getBreakdownData(){
    this.apiService.get(`BusinessDashboard/BreakDownProductData?fromDate=${this.dateRange.startDate}&toDate=${this.dateRange.endDate}&category=${this.category}`).subscribe((res: any) => {
      this.breakdownData = res.breakDownProductData;      
      this.salesDivisionChartData = res.divisionsByProductTypeChart;
      this.paymentMethodChartData = res.productsByPaymentTypeChart;
      this.salesDivisionChart = this.setPieChart('By Sales Division',this.salesDivisionChartData,"Total Amount","Count","");
      this.paymentMethodChart = this.setPieChart('By Payment Type',this.paymentMethodChartData,"Total Amount","Count","");
      this.displayedColumns = this.columnNames.map(x => x.id);
      this.createTable();
    }, (err: any) => {
      this.toastMsg.error(err.message, "Server Error!");
    });
  }
  createTable() {
    let tableArr: any[] = [];
    for (let i: number = 0; i <= this.breakdownData.length - 1; i++) {
      let currentRow = this.breakdownData[i];
      tableArr.push({
        contactId: currentRow.contactId,productType:currentRow.productType, amount: currentRow.amount, contactLegalName: currentRow.contactLegalName,
        opportunityId: currentRow.opportunityId, opportunityName: currentRow.opportunityName, paymentStatus: currentRow.paymentStatusName,
        paymentType: currentRow.paymentTypeName, salesDivision: currentRow.salesDivision,commissionId:currentRow.commissionId,
        modifiedDate: this.dateZonePipe.transform(currentRow.userModifiedTimestamp)
      });
    }
    this.dataSource = new MatTableDataSource(tableArr);
    this.originalDataSource = tableArr;
    this.dataSource.sort = this.sort;
    this.dataSource.paginator = this.paginator;
  }
  onSortChange(event:any){
    let dateFieldData = this.columnNames.filter(s=>s.dataType === 'Date');
    let dateField = dateFieldData && dateFieldData.length > 0 ? dateFieldData.filter(s=>s.id === event.active).map(c=> c.id).toString():'';
    if(dateField){
      this.dataSource.sortingDataAccessor = (item, property) => {
        switch (property) {
          case dateField:
            return new Date(item[dateField]).toISOString();
          default:
            return item[property];
        }
      };

      this.dataSource.sortingFn = (a: any, b: any, active: string, direction: string) => {
        if (active === dateField) {
          const dateA = new Date(a);
          const dateB = new Date(b);
          if (direction === 'asc') {
            return dateA.getTime() - dateB.getTime();
          } else {
            return dateB.getTime() - dateA.getTime();
          }
        } else {
          return this.dataSource.sortingDataAccessor(a, active) > this.dataSource.sortingDataAccessor(b, active) ? 1 : -1;
        }
      };
    }
    
    
  }
  setPieChart(title:string,data:any,ylabel:string,customLabels:any,subtitle:string){
    let chart = new Chart({
      chart: {
        plotBackgroundColor: null,
        plotBorderWidth: null,
        plotShadow: false,
      },
      title: {
        text: title
      },
      subtitle:{
        text: subtitle,
      },
      lang: {
        thousandsSep: ','
      },
      tooltip: {  
        pointFormatter: function() {
          const point = this as CustomPoint;
          return `${ylabel}:<b>$${point.custom.toLocaleString("en-US")}</b> <br/> ${customLabels} : <b>${point.y.toLocaleString("en-US")}</b>`;
        },
      },
      accessibility: {
        point: {
          valueSuffix: '%',
        },
      },
      legend: {
        maxHeight: 90,  
      },
      credits:{
        enabled: false
      },
      plotOptions: {
        pie: {
            allowPointSelect: true,
            innerSize: '50%',
            cursor: 'pointer',
            dataLabels: {
                enabled: true
            },
            showInLegend: true
        }
      },
      series: [
        {
          type: 'pie',
          name: ylabel,
          showInLegend: true,
          data:data
        }
      ]
    });
    return chart;
  }

  onCategoryChange(){
    this.getBreakdownData();
  }
}
