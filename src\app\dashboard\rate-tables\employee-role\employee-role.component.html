<div class="page-title col-md-12">
  <h1>Employee Role</h1>
  <div class="breadcrumbs">
    <a href="#">Home</a>/<span>Employee Role</span>
  </div>
</div>

<div class="content">
  <div class="card">
    <div class="card-header-info">
      <h4 class="card-title no-hover-effect">Employee Role</h4>
    </div>


    <div class="card-body">
      <div class="row">
        <div class="card-body">
          <app-grid-mat-table [gridData]="originalDataSource"  
            [columnData]="columnNames" 
            [displayColumnData]="displayedColumns"
            [dateFields]="dateColumns"
            [isScrollWidth]="false"
            [isSearchAvailable]="true"
            (rowClick)="rowClick($event)">
          </app-grid-mat-table>
          <div>
            <a class="btn btn-primary float-right" *ngIf="!addRow" (click)="Add()"><i
                class="material-icons pointer">add_circle</i> Add</a>
            <a class="btn btn-primary float-right" *ngIf="addRow" (click)="addRow = !addRow"><i
                class="material-icons pointer">remove_circle</i> Hide</a>
          </div>


        </div>
      </div>
    </div>

    <div class="card" *ngIf="addRow">
      <div class="card-header-info">
        <h4 class="card-title no-hover-effect">
          <i class="fas fa-plus"></i> Add Employee Role
        </h4>
      </div>

      <div class="card-body">
        <div>
          <form [formGroup]="employeeRoleAddForm" (ngSubmit)="onSubmit()" class="w-100">
            <div class="row">
              <div class="form-group col-md-4">
                <div class="row">
                  <label class="col-sm-5">Contact</label>
                  <div class="col-sm-7">
                    <input id="typeahead-prevent-manual-entry" type="text" class="custom-select"
                      formControlName="contactId" [ngbTypeahead]="search" [inputFormatter]="formatter"
                      [editable]="false" [resultTemplate]="rt" placeholder="Type to search" />
                  </div>
                </div>
              </div>
              <ng-template #rt let-r="result" let-t="id">
                <div class="col-sm-12" style="width: 80%">
                  {{ r.contactName }}
                </div>
              </ng-template>

              <div class="form-group col-md-4">
                <div class="row">
                  <label class="col-sm-5">Sales Division</label>
                  <div class="col-sm-7">
                    <select class="custom-select" name="salesDivision_add_dropdown" formControlName="salesDivision"
                      data-style="btn btn-link" id="salesDivision_add_dropdown">
                      <option *ngFor="let sd of salesDivisions" value="{{sd}}"> {{sd}}</option>
                    </select>
                  </div>
                </div>
              </div>
              <div class="form-group col-md-4">
                <div class="row">
                  <label class="col-sm-5">State</label>
                  <div class="col-sm-7">
                    <select class="custom-select" name="state_add_dropdown" formControlName="state" (change)="changeState($event)"
                      data-style="btn btn-link" id="state_add_dropdown">
                      <option value=""> </option>
                      <option *ngFor="let sd of saleState" value="{{sd}}"> {{sd}}</option>
                    </select>
                  </div>
                </div>
              </div>
              <div class="form-group col-md-4">
                <div class="row">
                  <label class="col-sm-5">Sales Office</label>
                  <div class="col-sm-7">
                    <select class="custom-select" name="salesOffice_add_dropdown" formControlName="salesOffice" (change)="changeSalesOffice($event)"
                      data-style="btn btn-link" id="salesOffice_add_dropdown">
                      <option value=""> </option>
                      <option value="All"> All</option>
                      <option *ngFor="let so of salesOffices" value="{{so}}"> {{so}}</option>
                    </select>
                  </div>
                </div>
              </div>
              <div class="form-group col-md-4">
                <div class="row">
                  <label class="col-sm-5">Role Name</label>
                  <div class="col-sm-7">
                    <select class="custom-select" name="rolename_add_dropdown" formControlName="roleName"
                      data-style="btn btn-link" id="rolename_add_dropdown">
                      <option *ngFor="let rn of this.roleNames" [value]="rn"> {{ rn }} </option>
                    </select>
                  </div>
                </div>
              </div>

              <div class="form-group col-md-4">
                <div class="row">
                  <label class="col-sm-5">Effective Start Date</label>
                  <div class="col-sm-7">
                    <div class="date-picker w-100">
                      <input #AddStartDatePicker type="date" name="start_date" id="start_date" class="custom-input"
                        formControlName="effectiveStartDate" placeholder="" />
                      <span *ngIf="AddStartDatePicker.value.length > 0" class="mat-icon cal-reset"
                        (click)="clearAddStartDate(AddStartDatePicker)"><i class="far fa-calendar-times"></i></span>
                      <span *ngIf="AddStartDatePicker.value.length <= 0" class="mat-icon cal-open"><i
                          class="far fa-calendar-alt"></i></span>
                    </div>
                  </div>
                </div>
              </div>

              <div class="form-group col-md-4">
                <div class="row">
                  <label class="col-sm-5">Effective End Date</label>
                  <div class="col-sm-7">
                    <div class="date-picker w-100">
                      <input #AddEndDatePicker type="date" name="effectiveEndDate" id="effectiveEndDate"
                        class="custom-input" formControlName="effectiveEndDate" placeholder="" />
                      <span *ngIf="AddEndDatePicker.value.length > 0" class="mat-icon cal-reset"
                        (click)="clearAddEndDate(AddEndDatePicker)"><i class="far fa-calendar-times"></i></span>
                      <span *ngIf="AddEndDatePicker.value.length <= 0" class="mat-icon cal-open"><i
                          class="far fa-calendar-alt"></i></span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div class="row align-button-right">
              <button type="submit" class="btn btn-primary" [disabled]="employeeRoleAddForm.invalid"><i
                  class="fas fa-plus"></i> Add Employee Role</button>
            </div>
          </form>
        </div>
      </div>
    </div>

    <div class="card" *ngIf="editRow">
      <div class="card-header-info">
        <h4 class="card-title no-hover-effect">
          <i class="fas fa-plus"></i> Edit Employee Role
        </h4>
      </div>
      <div class="card-body">
        <div>
          <form [formGroup]="employeeRoleEditForm" (ngSubmit)="onEditSubmit(employeeRole)" class="w-100">
            <div class="row">
              <div class="form-group col-md-4">
                <div class="row">
                  <label class="col-sm-5">Contact Name</label>
                  <div class="col-sm-7">
                    <input class="custom-input" formControlName="contactId" name="contactId_edit" id="contactId_edit"
                      value="{{ contactName }}" disabled />
                  </div>
                </div>
              </div>

              <div class="form-group col-md-4">
                <div class="row">
                  <label class="col-sm-5">State</label>
                  <div class="col-sm-7">
                    <input class="custom-input" formControlName="state" name="state_edit"
                      id="state_edit" value="{{state}}" disabled />
                  </div>
                </div>
              </div>
              <div class="form-group col-md-4">
                <div class="row">
                  <label class="col-sm-5">Sales Division</label>
                  <div class="col-sm-7">
                    <input class="custom-input" formControlName="salesDivision" name="salesDivision_edit"
                      id="salesDivision_edit" value="{{salesDivision}}" disabled />
                  </div>
                </div>
              </div>

              <div class="form-group col-md-4">
                <div class="row">
                  <label class="col-sm-5">Sales Office</label>
                  <div class="col-sm-7">
                    <input class="custom-input" formControlName="salesOffice" name="salesOffice_edit"
                      id="salesOffice_edit" value="{{salesOffice}}" disabled />
                  </div>
                </div>
              </div>

              <div class="form-group col-md-4">
                <div class="row">
                  <label class="col-sm-5">Role Name</label>
                  <div class="col-sm-7">
                    <input class="custom-input" formControlName="roleName" name="roleName_edit" id="roleName_edit"
                      value="{{ roleName }}" disabled />
                  </div>
                </div>
              </div>

              <div class="form-group col-md-4">
                <div class="row">
                  <label class="col-sm-5">Effective Start Date</label>
                  <div class="col-sm-7">
                    <div class="date-picker w-100">
                      <input class="custom-input" formControlName="effectiveStartDate" name="effectiveStartDate_edit"
                        id="effectiveStartDate_edit" value="{{ effectiveStartDate }}" disabled />
                    </div>
                  </div>
                </div>
              </div>

              <div class="form-group col-md-4">
                <div class="row">
                  <label class="col-sm-5">Effective End Date</label>
                  <div class="col-sm-7">
                    <div class="date-picker w-100" *ngIf="allowEdit">
                      <input #EditEndDatePicker type="date" name="effectiveEndDate" id="effectiveEndDate"
                        class="custom-input" formControlName="effectiveEndDate" placeholder="" />
                      <span *ngIf="EditEndDatePicker.value.length > 0" class="mat-icon cal-reset"
                        (click)="clearEditEndDate(EditEndDatePicker)"><i class="far fa-calendar-times"></i></span>
                      <span *ngIf="EditEndDatePicker.value.length <= 0" class="mat-icon cal-open"><i
                          class="far fa-calendar-alt"></i></span>
                    </div>
                    <div class="date-picker w-100" *ngIf="!allowEdit">
                      <input class="custom-input" formControlName="effectiveEndDate" name="effectiveEndDate_edit"
                        id="effectiveEndDate_edit" value="{{ effectiveEndDate }}" disabled />
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="row align-button-right">
              <button type="submit" class="btn btn-primary" name="edit" [disabled]="employeeRoleEditForm.invalid">
                <i class="fas fa-plus"></i> Update Employee Role
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>


  </div>
</div>