import { Component, Inject, OnInit } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';

@Component({
  selector: 'app-split-commission-rates-dialog',
  templateUrl: './split-commission-rates-dialog.component.html',
  styleUrls: ['./split-commission-rates-dialog.component.css']
})
export class SplitCommissionRatesDialogComponent implements OnInit {
  splitCommissionGroup: Element[] = [];
  splitCommissionSelectedGroup: any;
  selectedSplitCommission =[];
  constructor(public dialogRef: MatDialogRef<SplitCommissionRatesDialogComponent>, @Inject(MAT_DIALOG_DATA) public data: any) { }

  ngOnInit(): void {
    this.splitCommissionGroup = this.data.splitCommission; 
    this.splitCommissionGroup.sort((a, b) => {
      return <any>new Date(b[0].effectiveStartDate) - <any>new Date(a[0].effectiveStartDate);
    });
  }
  groupClick(group: any) {
    this.splitCommissionSelectedGroup = group;
  } 
}
