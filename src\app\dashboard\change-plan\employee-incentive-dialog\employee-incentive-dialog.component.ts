import { Component, OnInit, Inject, ViewChild } from '@angular/core';
import { ApiService } from 'src/app/services/api.service';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { ToastrService } from 'ngx-toastr';
import { IRuleData } from 'src/app/model/rule-data.model';
import { IPrompt } from 'src/app/model/prompt.model';
import { IPlanDetail } from 'src/app/model/plan.model';
import { IRule, IStep } from 'src/app/model/rule.model';
import { IRulePrompt } from 'src/app/model/rule-prompt.model';
import { EmployeeIncentivePromptComponent } from '../employee-incentive-prompt/employee-incentive-prompt.component';

@Component({
  selector: 'app-employee-incentive-dialog',
  templateUrl: './employee-incentive-dialog.component.html',
  styleUrls: ['./employee-incentive-dialog.component.css']
})
export class EmployeeIncentiveDialogComponent implements OnInit {
  @ViewChild(EmployeeIncentivePromptComponent) employeeIncentivePrompt: EmployeeIncentivePromptComponent;
  selectedRuleId: number = null;
  prompts: IRule;
  promptsValues: object = {};
  addOn: AddOnRule;

  constructor(public dialogRef: MatDialogRef<EmployeeIncentiveDialogComponent>, @Inject(MAT_DIALOG_DATA) public data: IEmployeeIncentiveDialogData, private apiService: ApiService, private toastMsg: ToastrService) { }

  ngOnInit() {
  }

  onSelect() {
    // console.log(this.selectedRuleId);
    this.prompts = null;
    this.addOn = null;
    var rulePrompts = this.data.employeeIncentiveRules[0].rules.filter(p => p.ruleId == this.selectedRuleId)[0];
    // console.log(rulePrompts);
    if (rulePrompts) {
      this.prompts = rulePrompts;
      this.addOn = <AddOnRule>rulePrompts;
      this.addOn.promptValues = {};
    }
  }

  onRulePromptChange(prompt: IRulePrompt) {
    this.addOn.promptValues[prompt.commissionRuleId] = prompt;
    // console.log("Prompts:", this.promptsValues);
  }

  checkAllPromptsEntered() {
    if (this.employeeIncentivePrompt && this.employeeIncentivePrompt.ruleItems && this.employeeIncentivePrompt.ruleItems.find(r => r.ruleItemInputs.find(rii => rii.columnName != "End_Date" && (rii.columnValue == null || rii.columnValue == "") && rii.columnValue !== 0) != null) != null) {
      return false;
    } else if (!this.employeeIncentivePrompt || !this.employeeIncentivePrompt.ruleItems) {
      return false;
    } else {
      return true;
    }
  }

  checkValidDates() {
    var valid = true;

    var startDate: Date;
    var endDate: Date;

    if (this.employeeIncentivePrompt && this.employeeIncentivePrompt.ruleItems) {
      startDate = this.employeeIncentivePrompt.ruleItems.find(x => x.ruleItemInputs.find(y => y.columnName == "Start_Date") != null).ruleItemInputs.find(y => y.columnName == "Start_Date").columnValue;
  
      endDate = this.employeeIncentivePrompt.ruleItems.find(x => x.ruleItemInputs.find(y => y.columnName == "End_Date") != null).ruleItemInputs.find(y => y.columnName == "End_Date").columnValue;
    }
    
    if (startDate && endDate && endDate < startDate) {
      valid = false;
    }

    if (!startDate || (startDate && !endDate)) {
      valid = true;
    }

    return valid;
  }

}

export interface IEmployeeIncentiveDialogData {
  employeeIncentiveRules: IPlanDetail[];
  prompts: IPrompt[];
}

export class AddOnRule implements IRule {
  ruleId: number;
  ruleName: string;
  promptAssignPlan: boolean;
  ruleTypeId: number;
  ruleTypeName: string;
  description: string;
  formulaId: number;
  metadataTypeId: number;
  tableSchema: string;
  identifier: string;
  tableName: string;
  columnName: string;
  displayName: string;
  noReclaim: boolean;
  effectiveStartDate: any;
  effectiveEndDate: any;
  paymentRuleTypeId: number;
  triggerRuleTypeId: number;
  sqlQuery: string;
  paymentBookTypeId: number;
  overdrawLimit: number;
  weeklyPay: number;
  fallbackRuleId: number;
  prompts: IPrompt[];
  steps: IStep[];
  numberOfBonuses: number;
  isProcessing: boolean;
  isInclusion: boolean;
  inclusionEffectiveStartDate: string;
  inclusionEffectiveEndDate: string;
  promptValues: object;
  basePayRuleId: number;
  basePayRuleName: string;
}