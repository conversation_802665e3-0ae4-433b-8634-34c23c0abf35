import { Component, Inject, OnInit } from '@angular/core';
import { UntypedFormControl, UntypedFormGroup } from '@angular/forms';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { ToastrService } from 'ngx-toastr';
import { ApiService } from 'src/app/services/api.service';

@Component({
  selector: 'app-payment-note-model',
  templateUrl: './payment-note-model.component.html',
  styleUrls: ['./payment-note-model.component.css']
})
export class PaymentNoteModelComponent implements OnInit {

  noteForm = new UntypedFormGroup({
    note:new UntypedFormControl('')
  });
  constructor(public dialogRef: MatDialogRef<PaymentNoteModelComponent>,
    @Inject(MAT_DIALOG_DATA) public data: any,
    private apiService:ApiService,
    private toastMsg:ToastrService) { }

  ngOnInit() {
    this.noteForm.controls['note'].setValue(this.data.paymentNote)
  }

  onSubmit(){
    let data:any = {
      PaymentNote: this.noteForm.controls['note'].value
    }
    if(this.data.paymentId) data.PaymentId =  this.data.paymentId;
    else data.PaymentHistoryId = this.data.paymentHistoryId;
    this.apiService.post(this.data.url,data).subscribe((res) => {
        if (res) {
          this.dialogRef.close();
          console.log(res);
        }
      }, (err) => {
        this.toastMsg.error(err.message, "Error!");
      })
  }
}
