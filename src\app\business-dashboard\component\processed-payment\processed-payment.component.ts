import { CurrencyPipe, DatePipe } from '@angular/common';
import { Component, Input, OnInit, ViewChild } from '@angular/core';
import { ToastrService } from 'ngx-toastr';
import { ApiService } from 'src/app/services/api.service';
import { MatTableDataSource } from '@angular/material/table';
import { MatPaginator } from '@angular/material/paginator';
import { MatSort } from '@angular/material/sort';
import { Chart } from 'angular-highcharts';
import { IdateRange } from '../../models/models';
import { DatezonePipe } from 'src/app/pipe/datezone.pipe';

interface CustomPoint extends Highcharts.Point {
  custom: any;
}

export interface IProcessedPayments {
  contactId: number,
  amount: string,
  contactLegalName: string,
  opportunityId: number,
  commissionId:number,
  opportunityName: string,
  paymentStatus: string
  paymentType: string,
  salesDivision: string,
  salesTerritoryStateCode: string,
  modifiedDate: string
}
@Component({
  selector: 'app-processed-payment',
  templateUrl: './processed-payment.component.html',
  styleUrls: ['./processed-payment.component.css']
})
export class ProcessedPaymentComponent implements OnInit {
  displayedColumns = [];
  paymentData: any;
  salesTerritoryChart: any;
  salesDivisionChart: any;
  originalDataSource: any;
  paymentDataSource: any;
  @ViewChild('table', { read: MatSort, static: true }) sort: MatSort;
  @ViewChild('paginator', { static: true }) paginator: MatPaginator;
  salesTerritoryChartData: Chart;
  salesDivisionChartData: Chart;
  @Input() dateRange: IdateRange |any;
  @Input() tabNumber: number | null = null;
  previousDateRange: IdateRange | null = null;
  columnNames = [
    {
      id: "contactLegalName",
      value: "Contact Legal Name"
    },
    {
      id: "opportunityName",
      value: "Opportunity Name"
    },
    {
      id: "amount",
      value: "Amount"
    },
    {
      id: "paymentStatus",
      value: "Payment Status"
    },
    {
      id: "paymentType",
      value: "Payment Type"
    },
    {
      id: "salesDivision",
      value: "Sales Division"
    },
    {
      id: "modifiedDate",
      value: "Modified Date",
      dataType:'Date'
    },
    {
      id: "salesTerritoryStateCode",
      value: "State Code"
    },
  ];
  constructor(public apiService: ApiService, private toastMsg: ToastrService, private datePipe: DatePipe,private currencyPipe: CurrencyPipe,private dateZonePipe: DatezonePipe) { }
  ngOnInit() {
  }
  ngOnChanges(){
    if (this.tabNumber === 4) {
      if (this.dateRange) {
        if (this.previousDateRange === null || this.previousDateRange !== this.dateRange) {
          this.previousDateRange = this.dateRange;
          this.getProcessedPayments()
        }       
      }
    }
  }
  getProcessedPayments() {
    this.apiService.get(`BusinessDashboard/GetProcessedPayments?fromDate=${this.dateRange.startDate}&toDate=${this.dateRange.endDate}`).subscribe((res: any) => {
      this.paymentData = res.paymentData;
      this.salesTerritoryChart = res.salesTerritoryStateCodeChart;
      this.salesTerritoryChart.forEach(s=>{
        s.custom = this.currencyPipe.transform(s.custom);
      })
      this.salesDivisionChart = res.salesDivisionChart;
      this.salesDivisionChart.forEach(s=>{
        s.custom = this.currencyPipe.transform(s.custom);
      })
      this.displayedColumns = this.columnNames.map(x => x.id);
      this.createTable();
      this.getSalesTerritoryChart();
      this.getSalesDivisionChart();
    }, (err: any) => {
      this.toastMsg.error(err.message, "Server Error!");
    });
  }
  createTable() {
    let tableArr: IProcessedPayments[] = [];
    for (let i: number = 0; i <= this.paymentData.length - 1; i++) {
      let currentRow = this.paymentData[i];
      tableArr.push({
        contactId: currentRow.contactId, amount:currentRow.amount, contactLegalName: currentRow.contactLegalName,
        opportunityId: currentRow.opportunityId, opportunityName: currentRow.opportunityName, paymentStatus: currentRow.paymentStatusName,
        paymentType: currentRow.paymentTypeName, salesDivision: currentRow.salesDivision, salesTerritoryStateCode: currentRow.salesTerritoryStateCode,
        commissionId:currentRow.commissionId,
        modifiedDate: this.dateZonePipe.transform(currentRow.userModifiedTimestamp)
      });
    }
    this.paymentDataSource = new MatTableDataSource(tableArr);
    this.originalDataSource = tableArr;
    this.paymentDataSource.sort = this.sort;
    this.paymentDataSource.paginator = this.paginator;
  }
  onSortChange(event:any){
    let dateFieldData = this.columnNames.filter(s=>s.dataType === 'Date');
    let dateField = dateFieldData && dateFieldData.length > 0 ? dateFieldData.filter(s=>s.id === event.active).map(c=> c.id).toString():'';
    if(dateField){
      this.paymentDataSource.sortingDataAccessor = (item, property) => {
        switch (property) {
          case dateField:
            return new Date(item[dateField]).toISOString();
          default:
            return item[property];
        }
      };

      this.paymentDataSource.sortingFn = (a: any, b: any, active: string, direction: string) => {
        if (active === dateField) {
          const dateA = new Date(a);
          const dateB = new Date(b);
          if (direction === 'asc') {
            return dateA.getTime() - dateB.getTime();
          } else {
            return dateB.getTime() - dateA.getTime();
          }
        } else {
          return this.paymentDataSource.sortingDataAccessor(a, active) > this.paymentDataSource.sortingDataAccessor(b, active) ? 1 : -1;
        }
      };
    }
    
    
  }
  getSalesTerritoryChart() {
    this.salesTerritoryChartData = this.setChartData('By Sales Territory', this.salesTerritoryChart);
  }
  getSalesDivisionChart() {
    this.salesDivisionChartData = this.setChartData('By Sales Division', this.salesDivisionChart);
  }
  setChartData(title: string, chartData: any) {
    let chart = new Chart({
      chart: {
        plotBackgroundColor: null,
        plotBorderWidth: null,
        plotShadow: false,
      },
      title: {
        text: title
      },
      tooltip: {
        pointFormatter: function() {
          const point = this as CustomPoint;
          return `Total Amount : <b>${point.custom.toLocaleString("en-US")}</b> <br/> Total Count : ${point.y.toLocaleString("en-US")}<b></b>`;
        },
      },
      accessibility: {
        point: {
          valueSuffix: '%',
        },
      },
      credits: {
        enabled: false
      },
      legend: {
        maxHeight: 90,  
      },
      plotOptions: {
        pie: {
          allowPointSelect: true,
            innerSize: '50%',
            cursor: 'pointer',
            dataLabels: {
                enabled: true,
            },
            showInLegend: true
        },
      },
      series: [
        {
          type: 'pie',
          name: 'Total Amount',
          showInLegend: true,
          data: chartData
        }
      ]
    });
    return chart;
  }
}
