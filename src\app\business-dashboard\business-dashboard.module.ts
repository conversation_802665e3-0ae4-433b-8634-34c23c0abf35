import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { BusinessDashboardRoutingModule } from './business-dashboard-routing.module';
import { DashboardHomeComponent } from './component/dashboard-home/dashboard-home.component';
import { MatIconModule } from '@angular/material/icon';
import { MatPaginatorModule } from '@angular/material/paginator';
import { MatSortModule } from '@angular/material/sort';
import { MatTableModule } from '@angular/material/table';
import { MatTabsModule } from '@angular/material/tabs';
import { ChartModule } from 'angular-highcharts';
import { HighchartsChartModule } from 'highcharts-angular';
import { MapComponent } from './component/map/map.component';
import { AllChartsComponent } from './component/all-charts/all-charts.component';
import { ScheduledJobsComponent } from './component/scheduled-jobs/scheduled-jobs.component';
import { NgxPaginationModule } from 'ngx-pagination';
import { RepsWithoutPlansComponent } from './component/reps-without-plans/reps-without-plans.component';
import { PendingPaymentsComponent } from './component/pending-payments/pending-payments.component';
import { ProcessedPaymentComponent } from './component/processed-payment/processed-payment.component';
import { FormsModule } from '@angular/forms';
import { KilowattComponent } from './component/kilowatt/kilowatt.component';
import { AgingPaymentsComponent } from './component/aging-payments/aging-payments.component';
import { RecentCommissionHistoryComponent } from './component/recent-commission-history/recent-commission-history.component';
import { RecentEomComponent } from './component/recent-eom/recent-eom.component';
import { PaymentHoldComponent } from './component/payment-hold/payment-hold.component';
import { DynamicReportComponent } from './component/dynamic-report/dynamic-report.component';
import { BreakdownProductComponent } from './component/breakdown-product/breakdown-product.component';
import { MatRadioModule } from '@angular/material/radio';
import { GridFilterComponent } from './component/grid-filter/grid-filter.component';
import { GridMatTableComponent } from './component/grid-mat-table/grid-mat-table.component';
import { MainPipeModule } from '../pipe/main-pipe.module';
import { DatezonePipe } from '../pipe/datezone.pipe';

@NgModule({
  declarations: [DashboardHomeComponent,
     MapComponent,
     AllChartsComponent,
     ScheduledJobsComponent,
     RepsWithoutPlansComponent,
     PendingPaymentsComponent,
     ProcessedPaymentComponent,
     KilowattComponent,
     AgingPaymentsComponent,
     RecentCommissionHistoryComponent,
     RecentEomComponent,
     PaymentHoldComponent,
     DynamicReportComponent,
     BreakdownProductComponent,
    ],
  imports: [
    CommonModule,
    BusinessDashboardRoutingModule,
    MatTabsModule,
    ChartModule,
    HighchartsChartModule,
    MatTableModule,
    MatSortModule,
    MatPaginatorModule,
    MatIconModule,
    MatRadioModule,
    FormsModule,
    NgxPaginationModule,
    MainPipeModule,
    GridFilterComponent,
    GridMatTableComponent
  ],providers: [
    DatezonePipe
  ]
})
export class BusinessDashboardModule { }
