{"name": "ag8-ui", "version": "0.0.0", "scripts": {"ng": "ng", "start": "set NODE_OPTIONS=--openssl-legacy-provider && ng serve", "start-local": "ng serve --configuration=local", "build": "ng build", "build-prod": "ng build --prod", "build-staging": "ng build --configuration=staging", "build-test": "ng build --configuration=test", "test": "ng test", "lint": "ng lint", "e2e": "ng e2e"}, "private": true, "dependencies": {"@angular/animations": "^14.3.0", "@angular/cdk": "^14.2.7", "@angular/common": "^14.3.0", "@angular/compiler": "^14.3.0", "@angular/core": "^14.3.0", "@angular/forms": "^14.3.0", "@angular/localize": "^14.3.0", "@angular/material": "^14.2.7", "@angular/platform-browser": "^14.3.0", "@angular/platform-browser-dynamic": "^14.3.0", "@angular/router": "^14.3.0", "@azure/msal-angular": "2.4.5", "@azure/msal-browser": "2.30.0", "@highcharts/map-collection": "^1.1.3", "@ng-bootstrap/ng-bootstrap": "~13.1.1", "@ngx-pwa/local-storage": "^13.0.6", "@types/crypto-js": "^3.1.43", "angular-highcharts": "^14.1.7", "@popperjs/core": "^2.10.2", "crypto-js": "^3.1.9-1", "file-saver": "^2.0.2", "font-awesome": "^4.7.0", "hammerjs": "^2.0.8", "highcharts": "^9.1.2", "highcharts-angular": "^2.10.0", "lodash": "^4.17.15", "moment": "^2.24.0", "ng-multiselect-dropdown": "0.2.5", "ngx-cacheable": "^1.3.0", "ngx-currency": "^2.0.0", "ngx-infinite-scroll": "^8.0.2", "ngx-pagination": "^4.1.0", "ngx-timeago": "^1.0.4", "ngx-toastr": "^12.1.0", "rxjs": "~6.6.7", "rxjs-compat": "^6.5.5", "tslib": "^2.0.0", "zone.js": "~0.11.8"}, "devDependencies": {"@angular-devkit/build-angular": "^14.2.13", "@angular/cli": "^14.2.13", "@angular/compiler-cli": "^14.3.0", "@angular/language-service": "^14.3.0", "@types/jasmine": "^3.3.16", "@types/jasminewd2": "^2.0.8", "@types/node": "^12.11.1", "codelyzer": "^6.0.2", "jasmine-spec-reporter": "~5.0.0", "karma": "~6.4.2", "jasmine-core": "~3.10.0", "karma-chrome-launcher": "~3.1.0", "karma-coverage": "~2.0.3", "karma-jasmine": "~4.0.0", "karma-jasmine-html-reporter": "~1.7.0", "karma-coverage-istanbul-reporter": "~3.0.2", "protractor": "~7.0.0", "ts-node": "~7.0.0", "tslint": "~6.1.0", "typescript": "~4.6.4"}}