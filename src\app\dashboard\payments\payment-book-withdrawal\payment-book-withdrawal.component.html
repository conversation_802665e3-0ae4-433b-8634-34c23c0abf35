<div class="page-title col-md-12 ">
  <h1 class="">Payment Withdrawals</h1>
  <div class="breadcrumbs"><a href="#">Home</a>/<span>Payment Withdrawals</span>
  </div>
</div>

<div class=" w-100">
  <div class="content">
    <div class="card">
      <div class="card-header-info paymentwithdrawal">
        <h4 class="card-title">Payment Book Withdrawals</h4>

      </div>
      <div class="card-body">
        <div class="row">
          <div class="col-md-12">
            <div class="float-right ">
              <button class="btn btn-primary" (click)="filter = !filter"><i class="material-icons">filter_list</i>
                Filter</button>

              <button class="btn btn-primary" (click)="getWithdrawalsWorksheet()"><i class="material-icons">save_alt</i>
                Download</button>
            </div>
            <div class="float-right col-md-3 text-right pr-2">
              <div class="form-group input-group ">

                <input class="custom-input ng-pristine ng-valid ng-touched" type="text" id="searchTextId"
                  [(ngModel)]="searchText" (keyup)="applyFilter($event)" name="searchText" placeholder="Search">
                <span class="input-group-icon">
                  <i class="fas fa-search"></i>
                </span>
              </div>
            </div>
          </div>
        </div>
        <div class="text-right">
          <button class="btn btn-primary" style="margin-right:15px; margin-top:-1px;" (click)="resetWithdrawalOverrides()">Reset Withdrawals</button>
          <mat-checkbox class="example-margin" [(ngModel)]="showInActive" (change)="getAllPaymentBookBalances()">Show Inactive Contacts</mat-checkbox>
          <mat-checkbox class="example-margin ml-2" [(ngModel)]="showNegativeBooks" (change)="changeBalancesShown()">Show Negative Balances</mat-checkbox>
        </div>
        <ng-container *ngIf="filter">
          <div class="row">
            <div class="col-md-12  gray-bg">
              <div class="row">
                <div class="form-group col-md-4">
                  <label>Sales Division</label>
                  <select class="custom-select hover" multiple [(ngModel)]="salesDivision" (change)="onChangeFilter()">
                    <option [value]="null">--NO FILTER--</option>
                    <option *ngFor="let division of salesDivisions" [value]="division">
                      {{division}}
                    </option>
                  </select>
                </div>
                <div class="form-group col-md-4">
                  <label>Sales Office</label>
                  <select class="custom-select hover" multiple [(ngModel)]="salesOffice" (change)="onChangeFilter()">
                    <option [value]="null">--NO FILTER--</option>
                    <option *ngFor="let office of salesOffices" [value]="office">
                      {{office}}
                    </option>
                  </select>
                </div>
                <!-- <div class="form-group col-md-3">
            <input class="form-control" type="text" id="searchTextId" [(ngModel)]="searchText" (keyup)="applyFilter($event)" name="searchText"
              placeholder="Search">
          </div> -->
                <!-- Dilip 06/09/2020 COM-881 -->

                <div class="form-group col-md-4">
                  <label>Payment Approval Date - From</label>
                  <div class="input-group date-picker">
                    <input #PaymentApprovalDateRangeStartGreater type="date" class="custom-input " [(ngModel)]="PaymentApprovalDateRangeStart" 
                    (change)="onChangeFilter()">
                    <span *ngIf="PaymentApprovalDateRangeStartGreater.value.length > 0" class="mat-icon cal-reset"
                      (click)="this.PaymentApprovalDateRangeStart = null; onChangeFilter();"><i
                        class="far fa-calendar-times"></i></span>
                    <span *ngIf="PaymentApprovalDateRangeStartGreater.value.length <= 0" class="mat-icon cal-open"><i
                        class="far fa-calendar-alt"></i></span>
                  </div>
                </div>
                <div class="form-group col-md-4">
                  <label>Payment Approval Date - To</label> <br>
                  <div class="input-group date-picker">
                    <input type="date" #PaymentApprovalDateRangeEndLess class="custom-input"
                      [(ngModel)]="PaymentApprovalDateRangeEnd" (change)="onChangeFilter()">
                    <span *ngIf="PaymentApprovalDateRangeEndLess.value.length > 0" class="mat-icon cal-reset"
                      (click)="this.PaymentApprovalDateRangeEnd = null; onChangeFilter();"><i
                        class="far fa-calendar-times"></i></span>
                    <span *ngIf="PaymentApprovalDateRangeEndLess.value.length <= 0" class="mat-icon cal-open"><i
                        class="far fa-calendar-alt"></i></span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </ng-container>

        <table mat-table [dataSource]="paymentBookBalances" matSort class="my-table" style="width: 100%">
          <ng-container matColumnDef="selected">
            <th mat-header-cell *matHeaderCellDef>
              <!-- <section class="checkbox-section"> -->
              <mat-checkbox [(ngModel)]="allSelected" (change)="onBulkSelectionChange()" [disabled]="paymentBookBalances?.filteredData.length === 0">
              </mat-checkbox>
              <!-- </section> -->
            </th>
            <td mat-cell *matCellDef="let element">
              <section class="checkbox-section">
                <mat-checkbox [(ngModel)]="element.selected" (change)="onSelectionChange()">
                </mat-checkbox>
              </section>

            </td>
          </ng-container>

          <ng-container matColumnDef="payBookLink">
            <th mat-header-cell *matHeaderCellDef> Payment Book </th>
            <td data-td-head="Payment Book" mat-cell *matCellDef="let element"
              (click)="openPaymentBook(element.contactId)" class="hover-withdrawal" style="text-align: center;">
              <a>
                <mat-icon>account_balance_wallet</mat-icon>
              </a>
            </td>
          </ng-container>

          <ng-container matColumnDef="contactName">

            <th class="mat-column-width" mat-header-cell *matHeaderCellDef mat-sort-header> Contact </th>
            <td data-td-head="Contact" mat-cell *matCellDef="let element"> 
              <a  [routerLink]="['/ui/commissions/paybook', element.contactId ? element.contactId : 0]">{{element.contactName
                ? element.contactName : "-"}} </a>
            </td>
          </ng-container>

          <ng-container matColumnDef="balance">
            <th class="mat-column-width" mat-header-cell *matHeaderCellDef mat-sort-header> Balance </th>
            <td data-td-head="Balance" mat-cell *matCellDef="let element"> {{element.balance | currency}} </td>
          </ng-container>

          <ng-container matColumnDef="paymentBookTypeName">
            <th class="mat-column-width" mat-header-cell *matHeaderCellDef mat-sort-header> Payment Book Type </th>
            <td data-td-head="Payment Book Type" mat-cell *matCellDef="let element"> {{element.paymentBookTypeName}}
            </td>
          </ng-container>

          <ng-container matColumnDef="weeklyPay">
            <th class="mat-column-width" mat-header-cell *matHeaderCellDef mat-sort-header> Weekly Pay </th>
            <td data-td-head="Weekly Pay" mat-cell *matCellDef="let element">
              {{element.paymentBookTypeName == 'Max Weekly Pay' ? (element.weeklyPay | currency) : 'N/A'}} </td>
          </ng-container>

          <ng-container matColumnDef="overdrawLimit">
            <th class="mat-column-width" mat-header-cell *matHeaderCellDef mat-sort-header> Overdraw Limit </th>
            <td data-td-head="Overdraw Limit" mat-cell *matCellDef="let element">
              {{element.paymentBookTypeName == 'Max Weekly Pay' ? (element.overdrawLimit | currency) : 'N/A'}} </td>
          </ng-container>

          <ng-container matColumnDef="withdrawalAmount">
            <th class="mat-column-width" mat-header-cell *matHeaderCellDef mat-sort-header> Withdrawal Amount </th>
            <td data-td-head="Withdrawal Amount" mat-cell *matCellDef="let element"> 
              <div *ngIf="element.hasWithdrawalOverrideValue; then active else inactive"></div>
              <ng-template #active><input [formControl]="withdrawalOverrideSingle" style="width:70%; margin-bottom:0px;" value="{{ element.overrideWithdrawalAmount }}" class="custom-input" type="text"><i (click)="closeOverrideWithdrawal(element)" class="material-icons">visibility</i></ng-template>
              <ng-template #inactive>{{ (element.overrideWithdrawalAmount ? element.overrideWithdrawalAmount : element.withdrawalAmount) | currency }}<mat-icon (click)="editWithdrawalAmount(element)" [ngStyle]="{'color': element.overrideWithdrawalAmount > 0 ? '#37b471' : black}" style="margin-top:-3px;" class="material-icons">visibility</mat-icon></ng-template>
            </td>
          </ng-container>

          <ng-container matColumnDef="lastWithdrawalDate">
            <th class="mat-column-width" mat-header-cell *matHeaderCellDef mat-sort-header> Last Withdrawal Date </th>
            <td data-td-head="Last Withdrawal Date" mat-cell *matCellDef="let element"> {{element.lastWithdrawalDate |
              date}} </td>
          </ng-container>

          <ng-container matColumnDef="processDate">
            <th class="mat-column-width" mat-header-cell *matHeaderCellDef mat-sort-header> Latest Approval Date </th>
            <td data-td-head="Last Withdrawal Date" mat-cell *matCellDef="let element"> {{element.processDate | date}}
            </td>
          </ng-container>

          <ng-container matColumnDef="salesDivision">
            <th class="mat-column-width" mat-header-cell *matHeaderCellDef mat-sort-header> Sales Division </th>
            <td data-td-head="Sales Division" mat-cell *matCellDef="let element"> {{element.salesDivision}} </td>
          </ng-container>

          <ng-container matColumnDef="salesOffice">
            <th class="mat-column-width" mat-header-cell *matHeaderCellDef mat-sort-header> Sales Office </th>
            <td data-td-head="Sales Office" mat-cell *matCellDef="let element"> {{element.salesOffice}} </td>
          </ng-container>

          <tr mat-header-row *matHeaderRowDef="paymentColumns"></tr>
          <tr mat-row *matRowDef="let row; columns paymentColumns;"></tr>
        </table>
        <div style="display: flex; justify-content: space-between; align-items: center">
          <p style="font-size: 15px; margin-top: 10px"><b>Total Selected Amount: {{getSelectedSum() | currency}}</b></p>
          <mat-paginator [pageSize]="pageSize" [pageSizeOptions]="pageSizeOptions">
          </mat-paginator>
        </div>

        <!-- <table class="table table-striped table-borderless table-hover table-active">
          <thead>
            <tr class="">
              <th scope="col">
                <div class="row">
                  <section class="checkbox-section">
                    <mat-checkbox class="checkbox-all-margin" [(ngModel)]="allSelected" (change)="onBulkSelectionChange()">
                    </mat-checkbox>
                  </section>
                  Selected
                </div>
              </th>
              <th scope="col" class="no-hover-effect">Contact</th>
              <th scope="col" class="no-hover-effect">Balance</th>
              <th scope="col" class="no-hover-effect">Book Type</th>
              <th scope="col" class="no-hover-effect">Weekly Pay</th>
              <th scope="col" class="no-hover-effect">Overdraw Limit</th>
              <th scope="col" class="no-hover-effect">Withdrawal Amount</th>
              <th scope="col" class="no-hover-effect">Last Withdrawal Date</th>
              <th scope="col" class="no-hover-effect">Sales Division</th>
              <th scope="col" class="no-hover-effect">Sales Office</th>
              
            </tr>
          </thead>
          <tbody *ngIf="paymentBookBalances">
            <tr
              *ngFor="let bal of (paymentBookBalances | tableFilter: searchText) | paginate: { id: 'withdrawals', itemsPerPage: pageSize, currentPage: withdrawalPage }">
              <td>
                <section class="checkbox-section">
                  <mat-checkbox class="checkbox-margin" [(ngModel)]="bal.selected" (change)="onSelectionChange()">
                  </mat-checkbox>
                </section>
              </td>
              <td class="no-hover-effect">{{bal.contactName}}</td>
              <td class="no-hover-effect">{{bal.balance | currency}}</td>
              <td class="no-hover-effect">{{bal.paymentBookTypeName}}</td>
              <td class="no-hover-effect">{{bal.paymentBookTypeName == 'Max Weekly Pay' ? (bal.weeklyPay | currency) : 'N/A'}}</td>
              <td class="no-hover-effect">{{bal.paymentBookTypeName == 'Max Weekly Pay' ? (bal.overdrawLimit | currency) : 'N/A'}}</td>
              <td class="no-hover-effect">{{bal.withdrawalAmount | currency}}</td>
              <td class="no-hover-effect">{{bal.lastWithdrawalDate | date}}</td>
              <td class="no-hover-effect">{{bal.salesDivision}}</td>
              <td class="no-hover-effect">{{bal.salesOffice}}</td>

            </tr>
          </tbody>
        </table>
        <div class="row">
          <div class="col-md-10">
            <pagination-controls id="withdrawals" (pageChange)="withdrawalPage = $event"></pagination-controls>
          </div>
          <div class="col-md-2" style="display: flex; justify-content: space-evenly;">
            <mat-form-field>
              <mat-select [(ngModel)]="pageSize" name="pageSize">
                <mat-option value="10">10 items</mat-option>
                <mat-option value="50">50 items</mat-option>
                <mat-option value="100">100 items</mat-option>
                <mat-option value="300">300 items</mat-option>
                <mat-option value="500">500 items</mat-option>
              </mat-select>
            </mat-form-field>
          </div>
        </div> -->

        <div class="pay-selected gray-bg mt-3 pt-3 pb-3">
          <div class="col-md-12">
            
        <div class="float-left" style="width: 260px;">
          <mat-label><b>Hidden Users List</b></mat-label>
          <mat-form-field style="margin-top: 10px;" appearance="none">
            <div class="custom-select">View List of Hidden Users</div>
            <mat-select [formControl]="this.selectedUsers" multiple style="visibility: hidden; margin-left: 35px;">
                <mat-optgroup>
                  <mat-form-field style="width: 100%">
                    <input
                      #search
                      autocomplete="off"
                      placeholder="Search"
                      aria-label="Search"
                      matInput
                      [formControl]="searchInactive"
                    />
                    <button
                      [disableRipple]="true"
                      *ngIf="searchInactive.value"
                      matSuffix
                      mat-icon-button
                      aria-label="Clear"
                      (click)="clearSearch($event)"
                    >
                      <mat-icon>close</mat-icon>
                    </button>
                  </mat-form-field>
                </mat-optgroup>
                <mat-option *ngFor="let user of this.hiddenUsers" [value]="user.contactId">{{user.contactName}}</mat-option>
            </mat-select>
          </mat-form-field>
          <button class="btn float-left btn-primary" style="margin-top: 15px;" (click)="addWithdrawalExclusions()" [disabled]="checkHiddenUsers()">
            Hide ({{getNumberSelected()}}) Selected</button>
        </div>
            <div class="col-md-5 float-right">
              <span>
                <!-- <input style="display: inline-block; margin-right: 5px;"  type="checkbox" value="" [(ngModel)]="AllowUsersOverride">  -->
                <mat-checkbox [(ngModel)]="AllowUsersOverride"> Allow Override</mat-checkbox>
                <!-- <b style="font-weight: bold;"> Allow Override</b> -->
              </span>
              <p>Please enter a name for the withdrawal:</p>

              <div>
                <!-- <input class="form-control" id="withdrawalName" [(ngModel)]="withdrawalName" required> -->
                <input class="custom-input" id="withdrawalName" [(ngModel)]="withdrawalName"
                  placeholder="Name for the withdrawal" required> <!--  Dilip 05/27/2020 COM-743 -->
              </div>
              <button class="btn float-right btn-primary" (click)="bulkWithdrawal()" [disabled]="!checkSelected()"><i
                  class="far fa-money-bill-alt"></i> Pay ({{getNumberSelected()}}) Selected</button>
            </div>


          </div>

        </div>
      </div>
    </div>