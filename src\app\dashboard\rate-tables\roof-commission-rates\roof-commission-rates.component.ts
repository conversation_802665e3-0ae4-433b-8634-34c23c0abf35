import { Component, OnInit, ViewChild } from '@angular/core';
import { UntypedFormBuilder, UntypedFormGroup, Validators, UntypedFormControl, AbstractControl } from "@angular/forms";
import { ApiService } from "../../../services/api.service";
import { ToastrService } from 'ngx-toastr';
import { rateNotExisting } from '../../../shared/validators';
import { groupBy } from '../../../shared/group-by';
import { getControlName } from '../../../shared/get-control-name';
import { MatSort } from '@angular/material/sort';
import { MatTableDataSource } from '@angular/material/table';
import { MatPaginator } from '@angular/material/paginator';
import { TableFilterPipe } from '../../../pipe/table-filter.pipe';
import { DatePipe, PercentPipe, CurrencyPipe } from '@angular/common';
import { MatDialog } from '@angular/material/dialog';
import { RoofCommissionRateDialogComponent } from '../roof-commission-rate-dialog/roof-commission-rate-dialog.component';

@Component({
  selector: 'app-roof-commission-rates',
  templateUrl: './roof-commission-rates.component.html',
  styleUrls: ['./roof-commission-rates.component.css']
})
export class RoofCommissionRatesComponent implements OnInit {
  allRoofCommissionRate: any;
  activeRoofCommissionRate: any;
  roofCommissionRateGroup: any;
  roofCommissionSelectedGroup: any;
  dropdowns: any;
  roofCommissionRateForm: UntypedFormGroup;
  roofGroup: AbstractControl[][];
  addInd: boolean = false;
  isReloading :boolean = false ;
  p: number = 1;
  tableArr: Element[] = [];
  roofCommissionRate; 
  searchText: string = "";
  public date: Date;
  originalDataSource;
  dataSource;
  displayedColumns = [];
  @ViewChild(MatSort, { static: true }) sort: MatSort;
  @ViewChild(MatPaginator, { static: true }) paginator: MatPaginator;

  columnNames = [{
    id: "pricingType",
    value: "Pricing Type"

  }, {
    id: "salesRepType",
    value: "SalesRep Type"
  },
  {
    id: "floorRate",
    value: "Floor Rate"
  },
  {
    id: "baseRate",
    value: "Base Rate"
  },  
  {
    id: "bonusTierRate",
    value: "Bonus Tier Rate"
  },
  {
    id: "floorRateLowslope",
    value: "Floor Rate Low Slope"
  },
  {
    id: "leadSourceType",
    value: "Lead Source Type"
  },
  {
    id: "leadFee",
    value: "Lead Fee"
  },
  {
    id: "baseCommissionAmount",
    value: "Base Commission Amount"
  },
  {
    id: "effectiveStartDate",
    value: "Effective Start Date"
  },
  {
    id: "effectiveEndDate",
    value: "Effective End Date"
  }];
  pricingType= [{  
    value: "Cash"
  },
  {    
    value: "Service Finance"
  },
  {    
    value: "Sunnova"
  },
  {    
    value: "Goodleap"
  },
  {    
    value: "Sunlight Financial"
  },
]
salesRepType= [{  
  value: "Solar"
},
{    
  value: "Roof"
},
{
  value: "Solar Pro"
},
{
  value: "Roofing Pro"
},
{
  value: "Roofing Only Rep"
},
{
  value: "Prime"
},
{
  value: "RRR Roofing"
}
]
leadSourceTypes = ["Fixed", "Roof Squares"];

  constructor(public apiService: ApiService, private toastMsg: ToastrService, private formBuilder: UntypedFormBuilder, private pipe: TableFilterPipe,
    private datePipe: DatePipe, private percentPipe: PercentPipe, private currencyPipe: CurrencyPipe, private dialog: MatDialog) {

  }

  ngOnInit() {
    if (!this.apiService.checkPermission('ViewRateTables')) {
      this.apiService.goBack();
      this.toastMsg.error("Insufficient Permissions. Please make sure your account can access this information.", "Permissions");
    }
    this.roofCommissionRateForm = this.formBuilder.group({
      pricingType: [, [Validators.required]],
      salesRepType: [, [Validators.required]],
      floorRate: [0, [Validators.required]],
      baseRate: [0, [Validators.required]],
      bonusTierRate: [0, [Validators.required]],
      effectiveStartDate: ['', [Validators.required]],
      floorRateLowSlope: ['', [Validators.required]],
      leadFee: ['', [Validators.required]],
      salesMetric: [0, [Validators.required]],
      lowSlopeMetric: [0, [Validators.required]],
      commissionAmount: [0, [Validators.required]],
      selfGenCommissionAmount: [0, [Validators.required]],
      leadSourceType: [this.leadSourceTypes[0], [Validators.required]],
      baseCommissionAmount: [0, [Validators.required]]
    });

    this.getAllRoofCommissionRates();
    this.getActiveRoofCommissionRates();

    this.roofGroup = [[this.roofCommissionRateForm.controls.salesMetric, this.roofCommissionRateForm.controls.lowSlopeMetric,this.roofCommissionRateForm.controls.commissionAmount, this.roofCommissionRateForm.controls.selfGenCommissionAmount]];

    this.onChanges();
  }

  onChanges() {
    this.roofCommissionRateForm.valueChanges.subscribe(val => {  
    });
  }

  clearDate(date: HTMLInputElement) {
    date.value = "";
    this.date = null;
    this.roofCommissionRateForm.controls.effectiveStartDate.setValue('');
    event.stopPropagation();
  }

  onSubmit() {    
    if (!this.roofCommissionRateForm.invalid) {
      var groupArr = [];
      this.roofGroup.forEach(x => {
        groupArr.push(
          {
            "salesMetric": x[0].value,
            "lowSlopeMetric": x[1].value,
            "commissionAmount": x[2].value,
            "selfGenCommissionAmount": x[3].value
          }
        );
      }); ;

      var body = {
        pricingType: this.roofCommissionRateForm.controls.pricingType.value,
        salesRepType: this.roofCommissionRateForm.controls.salesRepType.value,
        floorRate: this.roofCommissionRateForm.controls.floorRate.value,
        baseRate: this.roofCommissionRateForm.controls.baseRate.value,
        floorRateLowSlope: this.roofCommissionRateForm.controls.floorRateLowSlope.value,
        effectiveStartDate: this.roofCommissionRateForm.controls.effectiveStartDate.value,
        leadFee: this.roofCommissionRateForm.controls.leadFee.value,
        leadSourceType: this.roofCommissionRateForm.controls.leadSourceType.value,
        bonusTierRate: this.roofCommissionRateForm.controls.bonusTierRate.value,
        baseCommissionAmount: this.roofCommissionRateForm.controls.baseCommissionAmount.value,
        roofRates: groupArr
      } 
      this.apiService.post('RoofCommissionsRate', body)
        .subscribe(data => {
          this.toastMsg.success('Roof Commission Rates Successfully Added');
          this.isReloading = true ;
          this.getAllRoofCommissionRates();
          this.getActiveRoofCommissionRates();
          this.addInd = !this.addInd;
        }, (err: any) => {
          if (err?.err?.message)
            this.toastMsg.error(err.message, "Server Error!");
          if (typeof err === "string") {
            let isJsonObject = false;            
            let parsedErr: any;
            try {
              parsedErr = JSON.parse(err);
              isJsonObject =parsedErr && typeof parsedErr === "object" &&!Array.isArray(parsedErr);
            } catch (e) {
              isJsonObject = false;
            }
            if (isJsonObject) {
              this.toastMsg.error(parsedErr.result, "Error!");
            } else this.toastMsg.error(err, "Error!");
          }
          if (err?.result) {
            this.toastMsg.error(err.result, "Error!");
          }
        });
    }
  }

  getAllRoofCommissionRates() {
    this.apiService.get('RoofCommissionsRate/retrieveall')
      .subscribe(data => {
        this.allRoofCommissionRate = data;   
        if (this.roofCommissionRateGroup) {
          this.getRoofCommissionRateGroup(this.roofCommissionRateGroup[0][0]);
          this.roofCommissionSelectedGroup = this.roofCommissionRateGroup;
        }
      }, (err: any) => {
        this.toastMsg.error(err.message, 'Server Error!');
      });
  }

  getActiveRoofCommissionRates() {
    this.apiService.get('RoofCommissionsRate/retrieveactive')
      .subscribe(data => {        
        this.activeRoofCommissionRate = data;
        this.displayedColumns = this.columnNames.map(x => x.id);
        this.createTable();

      }, (err: any) => {
        this.toastMsg.error(err.message, 'Server Error!');
      });
  }

  

  getRoofCommissionRateGroup(commissionRate: any) {
    var roofCommissionRates = this.allRoofCommissionRate.filter(x => x.pricingType === commissionRate.pricingType && x.salesRepType === commissionRate.salesRepType);
    roofCommissionRates = Object.values(groupBy(roofCommissionRates, 'effectiveStartDate'));
    this.roofCommissionRateGroup = roofCommissionRates;
    this.roofCommissionSelectedGroup = null;
  }

  get ppaRate() { return this.roofCommissionRateForm.get('ppaRate'); }

  get pricePerKw() { return this.roofCommissionRateForm.get('pricePerKw'); }

  rowClick(commissionRate: any) {  
    var commissionRate = this.allRoofCommissionRate.filter(x => x.pricingType === commissionRate.pricingType && x.salesRepType === commissionRate.salesRepType);
    this.roofCommissionRate = commissionRate;
    commissionRate = Object.values(groupBy(commissionRate, 'effectiveStartDate'));
    const dialogRef = this.dialog.open(RoofCommissionRateDialogComponent, {
      width: '80%', data: { commissionRate }
    });
    if(this.roofCommissionRate && this.roofCommissionRate.length > 0){
      let effectiveDate = this.transformDate(this.roofCommissionRate[0]?.effectiveStartDate);
      this.roofCommissionRateForm.controls['pricingType'].setValue(this.roofCommissionRate[0]?.pricingType);
      this.roofCommissionRateForm.controls['salesRepType'].setValue(this.roofCommissionRate[0]?.salesRepType);
      this.roofCommissionRateForm.controls['floorRate'].setValue(this.roofCommissionRate[0]?.floorRate);
      this.roofCommissionRateForm.controls['baseRate'].setValue(this.roofCommissionRate[0]?.baseRate);
      this.roofCommissionRateForm.controls['effectiveStartDate'].setValue(effectiveDate);
      this.roofCommissionRateForm.controls['floorRateLowSlope'].setValue(this.roofCommissionRate[0]?.floorRateLowSlope);
      this.roofCommissionRateForm.controls['leadFee'].setValue(this.roofCommissionRate[0]?.leadFee);
      this.roofCommissionRateForm.controls['salesMetric'].setValue(this.roofCommissionRate[0]?.salesMetric);
      this.roofCommissionRateForm.controls['lowSlopeMetric'].setValue(this.roofCommissionRate[0]?.lowSlopeSalesMetric);
      this.roofCommissionRateForm.controls['commissionAmount'].setValue(this.roofCommissionRate[0]?.commissionAmount);
      this.roofCommissionRateForm.controls['selfGenCommissionAmount'].setValue(this.roofCommissionRate[0]?.selfGenCommissionAmount);
      this.roofCommissionRateForm.controls['leadSourceType'].setValue(this.roofCommissionRate[0].leadSourceType);
      this.roofCommissionRateForm.controls['bonusTierRate'].setValue(this.roofCommissionRate[0].bonusTierRate);
      this.roofCommissionRateForm.controls['baseCommissionAmount'].setValue(this.roofCommissionRate[0].baseCommissionAmount);
    }
    dialogRef.afterClosed().subscribe(result => {
    });
  }
  transformDate(dateStr: string): string | null {
    return this.datePipe.transform(dateStr, 'yyyy-MM-dd');
  }
  
  Add(){
    this.roofCommissionRate = this.tableArr;
    this.addInd = !this.addInd;
    this.isReloading = true ;
    if(!this.isReloading)
    {
      if (this.roofCommissionRateForm.controls.ppaRate) this.roofCommissionRateForm.controls.ppaRate.setValidators([Validators.required, rateNotExisting(this.allRoofCommissionRate)]);
    } else {
      this.roofCommissionRateForm.clearValidators();
    }
  }

  groupClick(group: any) {
    this.roofCommissionSelectedGroup = group;
  }
  addFormRow() {
    this.roofCommissionRateForm.addControl(`salesMetric${this.roofGroup.length}`, new UntypedFormControl(0, []));
    this.roofCommissionRateForm.addControl(`lowSlopeMetric${this.roofGroup.length}`, new UntypedFormControl(0, []));
    this.roofCommissionRateForm.addControl(`commissionAmount${this.roofGroup.length}`, new UntypedFormControl(0, []));
    this.roofCommissionRateForm.addControl(`selfGenCommissionAmount${this.roofGroup.length}`, new UntypedFormControl(0, []));
    var c1 = this.roofCommissionRateForm.get(`salesMetric${this.roofGroup.length}`);
    var c2 = this.roofCommissionRateForm.get(`lowSlopeMetric${this.roofGroup.length}`);
    var c3 = this.roofCommissionRateForm.get(`commissionAmount${this.roofGroup.length}`);
    var c4 = this.roofCommissionRateForm.get(`selfGenCommissionAmount${this.roofGroup.length}`);
    c1.setValidators([Validators.required]);
    c2.setValidators([Validators.required]);
    c3.setValidators([Validators.required]);
    c4.setValidators([Validators.required]);
    this.roofGroup.push([c1, c2,c3,c4]);
  }

  removeFormRow(index: number) {
    if (this.roofGroup.length == 1) return;

    this.roofGroup[index].slice(0).forEach(x => {
      this.roofCommissionRateForm.removeControl(getControlName(x));
    });

    this.roofGroup.splice(index, 1);
  }

  getControlName(control: AbstractControl) {
    return getControlName(control);
  }

  createTable() {
    let tableArr: Element[] = [];
    for (let i: number = 0; i <= this.activeRoofCommissionRate.length - 1; i++) {
      let currentRow = this.activeRoofCommissionRate[i];
      if(i==0)
      {
        this.tableArr[0] =this.activeRoofCommissionRate[0];
      }
      tableArr.push({
        effectiveEndDate: this.datePipe.transform(currentRow.effectiveEndDate), effectiveStartDate: this.datePipe.transform(currentRow.effectiveStartDate),
        pricingType: currentRow.pricingType, salesRepType: currentRow.salesRepType, floorRate: this.currencyPipe.transform(currentRow.floorRate),
        baseRate: this.currencyPipe.transform(currentRow.baseRate), floorRateLowslope: this.currencyPipe.transform(currentRow.floorRateLowslope), leadFee: this.currencyPipe.transform(currentRow.leadFee),
        roofCommissionMappingId: currentRow.roofCommissionMappingId, leadSourceType: currentRow.leadSourceType, bonusTierRate: this.currencyPipe.transform(currentRow.bonusTierRate), baseCommissionAmount: this.currencyPipe.transform(currentRow.baseCommissionAmount) 
      });
    }
    this.dataSource = new MatTableDataSource(tableArr);
    this.originalDataSource = tableArr;
    this.dataSource.sort = this.sort;
    this.dataSource.paginator = this.paginator;
  }
  searchForItem(): void {
    let filteredResults: Element[] = [];
    if (this.searchText == '') {
      this.dataSource = new MatTableDataSource(this.originalDataSource);
      this.dataSource.sort = this.sort;
      this.dataSource.paginator = this.paginator;
    } else {
      filteredResults = this.pipe.transform(this.originalDataSource, this.searchText);
      this.dataSource = new MatTableDataSource(filteredResults);
      this.dataSource.sort = this.sort;
      this.dataSource.paginator = this.paginator;
    }
  }
}

export interface Element {
  effectiveEndDate: string,
  effectiveStartDate: string,
  pricingType: string,
  salesRepType: string,
  floorRate: string,
  baseRate: string,
  floorRateLowslope: string,
  leadFee: string,
  roofCommissionMappingId: number, 
  leadSourceType: string,
  bonusTierRate: string,
  baseCommissionAmount: string
}