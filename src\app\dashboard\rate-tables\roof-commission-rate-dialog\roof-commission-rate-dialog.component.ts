import { Component, OnInit, Inject } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';

@Component({
  selector: 'app-roof-commission-rate-dialog',
  templateUrl: './roof-commission-rate-dialog.component.html',
  styleUrls: ['./roof-commission-rate-dialog.component.css']
})
export class RoofCommissionRateDialogComponent implements OnInit {
  roofCommissionRateGroup: Element[] = [];
  roofCommissionSelectedGroup: any;
  
  constructor(public dialogRef: MatDialogRef<RoofCommissionRateDialogComponent>, @Inject(MAT_DIALOG_DATA) public data: any) { }

  ngOnInit() {
    this.roofCommissionRateGroup = this.data.commissionRate; 
    this.roofCommissionRateGroup.sort((a, b) => {
      return <any>new Date(b[0].effectiveStartDate) - <any>new Date(a[0].effectiveStartDate);
    });
  }

  groupClick(group: any) {
    this.roofCommissionSelectedGroup = group;
  } 

}
