import { Component, OnInit, Inject } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { ApiService } from '../../../services/api.service';
import { ToastrService } from 'ngx-toastr';

@Component({
  selector: 'app-territory-rate-maintenance-dialog',
  templateUrl: './territory-rate-maintenance-dialog.component.html',
  styleUrls: ['./territory-rate-maintenance-dialog.component.css']
})
export class TerritoryRateMaintenanceDialogComponent implements OnInit {
  territoryRateGroup: Element[] = [];

  constructor(public dialogRef: MatDialogRef<TerritoryRateMaintenanceDialogComponent>,
    private apiService: ApiService, private toastMsg: ToastrService, @Inject(MAT_DIALOG_DATA) public data: any) { }


  ngOnInit() {
    this.territoryRateGroup = this.data.territoryRate;
    this.territoryRateGroup.sort((a, b) => {
      return <any>new Date(b.effectiveStartDate) - <any>new Date(a.effectiveStartDate);
    });
    
  }

}

export interface Element {
  effectiveStartDate: string,
  effectiveEndDate: string,
  baseRate: string,
  basePercentage: string,
  selfGenBonusPercentage: string,
  overagePercentage: string,
  referralPercentage: string,
  leadFee: string,
  minimumCommission: string,
  directMinimumCommission: string,
  selfGenOveragePercentage: string,
  floorRate: string,
  selfGenShareIndicator: string,
  commissionOnFloorIndicator: string
}