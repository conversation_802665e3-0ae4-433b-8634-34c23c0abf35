
<div class="page-title col-md-12 ">
    <h1>Search List</h1>
    <div class="breadcrumbs"><a href="#">Home</a>/<span>Search List</span>
    </div>
</div>

<div class="content">
        <div class="card">
            <div class="card-header-info">
                <h4 class="card-title"><i class="fas fa-search-dollar"></i> Search</h4>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-12">
                        <div class="float-right">
                            <button class="btn btn-primary" (click)="filter = !filter"><i class="material-icons">filter_list</i>
                                Filter</button>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-12">
                        <ng-container *ngIf="filter">
                            <div class="row">
                                <div class="col-md-12 pt-3 pb-3 gray-bg">
                                    <div class="row filter-row">
                                        <div class="form-group col-md-4">
                                            <label>Search</label>
                                            <input type="text" class="custom-input" [(ngModel)]="searchText">
                                        </div>
                                        <div class="form-group col-md-4">
                                            <label>Search Type</label>
                                            <ng-multiselect-dropdown class="container-multi-sel" [placeholder]="'Search'" [settings]="dropdownSettings" [data]="dropdownList"
                                                [(ngModel)]="multiselectControl.value" [ngModelOptions]="{standalone: true}" (onSelect)="onItemSelect($event)"
                                                (onDeSelect)="onDeSelect($event)"  (onSelectAll)="onSelectAll($event)">
                                            </ng-multiselect-dropdown>
                                        </div>
                                        <div class="form-group col-md-4">
                                            <label>Sales Territory</label>
                                            <select class="custom-select" [(ngModel)]="salesTerritory">
                                                <option [value]= "null">--NO FILTER--</option>
                                                <option *ngFor="let office of salesOffices" [value]="office">
                                                    {{office}}
                                                </option>
                                            </select>
                                        </div>
        
                                        <div class="form-group col-md-4">
                                            <label>Actual Install Start Date - From</label>
                                            <div class="input-group date-picker">
                                                <input #ActualStartDate type="date" class="custom-input"
                                                    [(ngModel)]="actualInstallStartDateRangeStart">
                                                <span *ngIf="ActualStartDate.value.length > 0" (click)="actualInstallStartDateRangeStart=null" class="mat-icon cal-reset"><i
                                                        class="far fa-calendar-times"></i></span>
                                                <span *ngIf="ActualStartDate.value.length <= 0" class="mat-icon cal-open"><i
                                                        class="far fa-calendar-alt"></i></span>
                                            </div>
                                        </div>
                                        <div class="form-group col-md-4">
                                            <label>Actual Install Start Date - To</label>
                                            <div class="input-group date-picker">
                                                <input #ActualStartDateEnd type="date" class="custom-input"
                                                    [(ngModel)]="actualInstallStartDateRangeEnd">
                                                <span *ngIf="ActualStartDateEnd.value.length > 0" (click)="actualInstallStartDateRangeEnd=null" class="mat-icon cal-reset"><i
                                                        class="far fa-calendar-times"></i></span>
                                                <span *ngIf="ActualStartDateEnd.value.length <= 0" class="mat-icon cal-open"><i
                                                        class="far fa-calendar-alt"></i></span>
                                            </div>
                                        </div>
                                        <div class="form-group col-md-4">
                                            <label>Date Contract Signed - From</label>
                                            <div class="input-group date-picker">
                                                <input #DateContractSignedGreater type="date" class="custom-input"
                                                    [(ngModel)]="dateContractSignedRangeStart">
                                                <span *ngIf="DateContractSignedGreater.value.length > 0"
                                                     (click)="dateContractSignedRangeStart=null" class="mat-icon cal-reset"><i class="far fa-calendar-times"></i></span>
                                                <span *ngIf="DateContractSignedGreater.value.length <= 0"
                                                    class="mat-icon cal-open"><i class="far fa-calendar-alt"></i></span>
        
                                            </div>
                                        </div>
                                        <div class="form-group col-md-4">
                                            <label>Date Contract Signed - To</label>
                                            <div class="input-group date-picker">
                                                <input #DaterContractSignedLess type="date" class="custom-input"
                                                    [(ngModel)]="dateContractSignedRangeEnd">
                                                <span *ngIf="DaterContractSignedLess.value.length > 0"
                                                    (click)="dateContractSignedRangeEnd=null" class="mat-icon cal-reset"><i class="far fa-calendar-times"></i></span>
                                                <span *ngIf="DaterContractSignedLess.value.length <= 0"
                                                    class="mat-icon cal-open"><i class="far fa-calendar-alt"></i></span>
                                            </div>
                                        </div>
                                        <div class="form-group col-md-4">
                                            <label>Demo Date Start- From</label>
                                            <div class="input-group date-picker">
                                                <input #DemoStartDate type="date" class="custom-input"
                                                    [(ngModel)]="demoDateStart">
                                                <span *ngIf="DemoStartDate.value.length > 0" class="mat-icon cal-reset"><i
                                                    (click)="demoDateStart=null" class="far fa-calendar-times"></i></span>
                                                <span *ngIf="DemoStartDate.value.length <= 0" class="mat-icon cal-open"><i
                                                        class="far fa-calendar-alt"></i></span>
        
                                            </div>
                                        </div>
                                        <div class="form-group col-md-4">
                                            <label>Demo Date Start - To</label>
                                            <div class="input-group date-picker">
                                                <input #DemoStartDateTo type="date" class="custom-input"
                                                    [(ngModel)]="demoDateEnd">
                                                <span *ngIf="DemoStartDateTo.value.length > 0" class="mat-icon cal-reset"><i
                                                    (click)="demoDateEnd=null" class="far fa-calendar-times"></i></span>
                                                <span *ngIf="DemoStartDateTo.value.length <= 0" class="mat-icon cal-open"><i
                                                        class="far fa-calendar-alt"></i></span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </ng-container>
                    </div>
                </div>
                <div class="row" *ngIf="filter">
                    <div class="col-md-12">
                        <div class="float-right">
                            <button (click)="onSearch()" [disabled]="searchText.trim() == 0" class="btn btn-primary"><i class="material-icons">search</i>
                                Search</button>
                            <button (click)="reset()" class="btn btn-primary"><i class="fas fa-sync"></i>
                                Reset</button>
                        </div>
        
                    </div>
                </div>
                <div class="row justify-content-center">
                    <span style="font-size: 15px;">{{promptMessage}}</span>
                </div>
            </div>
        
        </div>
        <div class="card" [class.hidden]="!opportunityDataSource">
            <div class="card-header-info card-header">
                <h4 class="card-title">Opportunities</h4>
            </div>
            <div class="card-body allusers">
                <div class="row">
                    <div class="col-md-12 mb-2">
                        <table mat-table 
                            matSort
                            #opportunityMatSort
                            [dataSource]="opportunityDataSource" 
                            class="mat-elevation-z8 search-table"
                            (matSortChange)="test($event)">
                            <ng-container matColumnDef="opportunityName">
                                <th mat-header-cell *matHeaderCellDef
                                    mat-sort-header class="table-header"> Opportunity Name </th>
                                <td mat-cell 
                                    *matCellDef="let element"
                                    data-td-head="Opportunity Name">
                                    <a [routerLink]="['../../../../ui/commissions/opportunitydetails',element.objectId]" (click)="viewOpportunity()"  class="search-link">
                                        {{element.opportunityName}}
                                    </a>
                                </td>
                            </ng-container>
                            <ng-container matColumnDef="dlNumber">
                                <th mat-header-cell *matHeaderCellDef
                                    mat-sort-header class="table-header"> DL Number  </th>
                                <td mat-cell 
                                    *matCellDef="let element"
                                    data-td-head="Stage"> {{element.dlNumber}} </td>
                            </ng-container>
                            <ng-container matColumnDef="stage">
                                <th mat-header-cell *matHeaderCellDef
                                    mat-sort-header class="table-header"> Stage  </th>
                                <td mat-cell 
                                    *matCellDef="let element"
                                    data-td-head="Stage"> {{element.stage}} </td>
                            </ng-container>
                            <ng-container matColumnDef="stageStaus">
                                <th mat-header-cell *matHeaderCellDef
                                    mat-sort-header class="table-header"> Stage Status</th>
                                <td mat-cell *matCellDef="let element"> {{element.stageStatus}} </td>
                            </ng-container>
                            <ng-container matColumnDef="systemSizeKWdc">
                                <th mat-header-cell *matHeaderCellDef
                                    mat-sort-header class="table-header">System Size in KWdc</th>
                                <td mat-cell *matCellDef="let element"> {{element.systemSizeKWdc}} </td>
                            </ng-container>
                            <ng-container matColumnDef="opportunityType">
                                <th mat-header-cell *matHeaderCellDef
                                    mat-sort-header class="table-header"> Opportunity Type </th>
                                <td mat-cell *matCellDef="let element"> {{element.opportunityType}} </td>
                            </ng-container>
                            <ng-container matColumnDef="salesTerritory">
                                <th mat-header-cell *matHeaderCellDef
                                    mat-sort-header class="table-header"> Sales Territory </th>
                                <td mat-cell *matCellDef="let element"> {{element.salesTerritory}} </td>
                            </ng-container>
    
                            <ng-container matColumnDef="utilityCompany">
                                <th mat-header-cell *matHeaderCellDef
                                    mat-sort-header class="table-header"> Utility Company </th>
                                <td mat-cell *matCellDef="let element"> {{element.utilityCompany}} </td>
                            </ng-container>
    
                            <tr mat-header-row *matHeaderRowDef="opportunityColumns"></tr>
                            <tr mat-row *matRowDef="let row; columns: opportunityColumns;"></tr>
                        </table>
                        <mat-paginator #opportunityPaginator [pageSizeOptions]="[ 10, 20]"
                            aria-label="Select page of periodic elements"></mat-paginator>
                    </div>
                </div>
            </div>
        </div>
        <div class="card" [class.hidden]="!contactDataSource">
            <div class="card-header-info card-header">
                <h4 class="card-title">Contacts</h4>
            </div>
            <div class="card-body allusers">
                <div class="row">
                    <div class="col-md-12 mb-2">
                        <table mat-table 
                            #contactMatSort
                            [dataSource]="contactDataSource" 
                            class="mat-elevation-z8 search-table my-table" 
                            matSort 
                            style="width: 100%">
                            <ng-container matColumnDef="contactName">
                                <th class="mat-column-width" mat-header-cell *matHeaderCellDef mat-sort-header>Name</th>
                                <td data-td-head="Name" mat-cell *matCellDef="let element">
                                    <a [routerLink]="['../../../../ui/commissions/salesrep',element.objectId]" (click)="viewContact()" class="search-link">
                                        {{element.contactName}}
                                    </a>
                                </td>
                            </ng-container>
    
                            <ng-container matColumnDef="contactEmail">
                                <th class="mat-column-width" mat-header-cell *matHeaderCellDef mat-sort-header> Email </th>
                                <td mat-cell *matCellDef="let element"> {{element.contactEmail}} </td>
                            </ng-container>
                            <ng-container matColumnDef="contactPhone">
                                <th class="mat-column-width" mat-header-cell *matHeaderCellDef mat-sort-header> Phone </th>
                                <td mat-cell *matCellDef="let element"> {{element.contactPhone}} </td>
                            </ng-container>
                            <ng-container matColumnDef="contactLegalName">
                                <th class="mat-column-width" mat-header-cell *matHeaderCellDef mat-sort-header> Legal Name </th>
                                <td mat-cell *matCellDef="let element"> {{element.contactLegalName}} </td>
                            </ng-container>
                            <ng-container matColumnDef="salesDivision">
                                <th class="mat-column-width" mat-header-cell *matHeaderCellDef mat-sort-header> Sales Division </th>
                                <td mat-cell *matCellDef="let element"> {{element.salesDivision}} </td>
                            </ng-container>
                            <ng-container matColumnDef="employmentStatus">
                                <th class="mat-column-width" mat-header-cell *matHeaderCellDef mat-sort-header> Employee Status </th>
                                <td mat-cell *matCellDef="let element"> {{element.employmentStatus}} </td>
                            </ng-container>
                            <ng-container matColumnDef="sales_Office">
                                <th class="mat-column-width" mat-header-cell *matHeaderCellDef mat-sort-header> Sales Office </th>
                                <td mat-cell *matCellDef="let element"> {{element.sales_Office}} </td>
                            </ng-container>
                            <ng-container matColumnDef="solarPro">
                                <th class="mat-column-width" mat-header-cell *matHeaderCellDef mat-sort-header> Solar Pro </th>
                                <td mat-cell *matCellDef="let element"> {{element.solarPro}} </td>
                            </ng-container>
                            <ng-container matColumnDef="startDate">
                                <th class="mat-column-width" mat-header-cell *matHeaderCellDef mat-sort-header> Start Date </th>
                                <td mat-cell *matCellDef="let element"> {{element.startDate |  date : 'mediumDate'  }} </td>
                            </ng-container>
                            <tr mat-header-row *matHeaderRowDef="contactColumns"></tr>
                            <tr mat-row *matRowDef="let row; columns: contactColumns;"></tr>
                        </table>
                        <mat-paginator #contactPaginator [pageSizeOptions]="[ 10, 20]"
                            aria-label="Select page of periodic elements"></mat-paginator>
                    </div>
                </div>
            </div>
        </div>
        <div class="card" [class.hidden]="!ruleDataSource">
            <div class="card-header-info card-header">
                <h4 class="card-title">Commission Rules</h4>
            </div>
            <div class="card-body allusers">
                <div class="row">
                    <div class="col-md-12 mb-2">
                        <table mat-table #ruleMatSort 
                            [dataSource]="ruleDataSource" 
                            class="mat-elevation-z8 search-table my-table" 
                            matSort style="width: 100%">
                            <ng-container matColumnDef="commissionRuleId">
                                <th class="mat-column-width" mat-header-cell *matHeaderCellDef mat-sort-header>#</th>
                                <td mat-cell *matCellDef="let element"> {{element.commissionRuleId}} </td>
                            </ng-container>
                            <ng-container matColumnDef="commissionRuleName">
                                <th class="mat-column-width" mat-header-cell *matHeaderCellDef mat-sort-header>Name</th>
                                <td mat-cell *matCellDef="let element">
                                    <a [routerLink]="[getViewUrl(element),element.objectId]" (click)="viewRule(element)" class="search-link">
                                        {{element.commissionRuleName}}
                                    </a>
                                </td>
                            </ng-container>
                            <ng-container matColumnDef="commissionRuleDescription">
                                <th class="mat-column-width" mat-header-cell *matHeaderCellDef mat-sort-header> Description </th>
                                <td mat-cell *matCellDef="let element"> {{element.commissionRuleDescription}} </td>
                            </ng-container>
                            <ng-container matColumnDef="ruleType">
                                <th class="mat-column-width" mat-header-cell *matHeaderCellDef mat-sort-header> Rule Type </th>
                                <td mat-cell *matCellDef="let element"> {{element.ruleType}} </td>
                            </ng-container>
                            <ng-container matColumnDef="noReclaim">
                                <th class="mat-column-width" mat-header-cell *matHeaderCellDef mat-sort-header> Reclaim </th>
                                <td mat-cell *matCellDef="let element"> {{element.noReclaim}} </td>
                            </ng-container>
                            <ng-container matColumnDef="numberOfBonuses">
                                <th class="mat-column-width" mat-header-cell *matHeaderCellDef mat-sort-header> No.of Bonus </th>
                                <td mat-cell *matCellDef="let element"> {{element.numberOfBonuses}} </td>
                            </ng-container>
                            <tr mat-header-row *matHeaderRowDef="ruleColumns"></tr>
                            <tr mat-row *matRowDef="let row; columns: ruleColumns;"></tr>
                        </table>
                        <mat-paginator #rulePaginator [pageSizeOptions]="[ 10, 20]"
                            aria-label="Select page of periodic elements"></mat-paginator>
                    </div>
                </div>
            </div>
        </div>
        <div class="card" [class.hidden]="!planDataSource">
            <div class="card-header-info card-header">
                <h4 class="card-title">Commission Plan</h4>
            </div>
            <div class="card-body allusers">
                <div class="row">
                    <div class="col-md-12 mb-2">
                        <table mat-table #plaMatSort 
                            [dataSource]="planDataSource" 
                            class="mat-elevation-z8 search-table my-table" 
                            matSort style="width: 100%">
                            <ng-container matColumnDef="planHeaderId">
                                <th class="mat-column-width" mat-header-cell *matHeaderCellDef mat-sort-header>#</th>
                                <td mat-cell *matCellDef="let element"> {{element.planHeaderId}} </td>
                            </ng-container>
                            <ng-container matColumnDef="planName">
                                <th class="mat-column-width" mat-header-cell *matHeaderCellDef mat-sort-header>Name</th>
                                <td mat-cell *matCellDef="let element">
                                    <a [routerLink]="['../../../../ui/commissions/viewPlan',element.planHeaderId]" (click)="viewPlan()" class="search-link">
                                        {{element.planName}}
                                    </a>
                                </td>
                            </ng-container>
                            <ng-container matColumnDef="planDescription">
                                <th class="mat-column-width" mat-header-cell *matHeaderCellDef mat-sort-header> Description </th>
                                <td mat-cell *matCellDef="let element"> {{element.planDescription}} </td>
                            </ng-container>
                            <ng-container matColumnDef="effectiveStartDate">
                                <th class="mat-column-width" mat-header-cell *matHeaderCellDef mat-sort-header>Start Date</th>
                                <td mat-cell *matCellDef="let element"> {{element.effectiveStartDate |  date : 'mediumDate'}} </td>
                            </ng-container>
                            <ng-container matColumnDef="effectiveEndDate">
                                <th class="mat-column-width" mat-header-cell *matHeaderCellDef mat-sort-header> End Date </th>
                                <td mat-cell *matCellDef="let element"> {{element.effectiveEndDate |  date : 'mediumDate'}} </td>
                            </ng-container>
                            <tr mat-header-row *matHeaderRowDef="planColumns"></tr>
                            <tr mat-row *matRowDef="let row; columns: planColumns;"></tr>
                        </table>
                        <mat-paginator #planPaginator [pageSizeOptions]="[ 10, 20]"
                            aria-label="Select page of periodic elements"></mat-paginator>
                    </div>
                </div>
            </div>
        </div>
        <div class="card" [class.hidden]="!formulaDataSource">
            <div class="card-header-info card-header">
                <h4 class="card-title">Commission Formula</h4>
            </div>
            <div class="card-body allusers">
                <div class="row">
                    <div class="col-md-12 mb-2">
                        <table mat-table #formulaMatSort 
                        [dataSource]="formulaDataSource" 
                        class="mat-elevation-z8 search-table my-table"
                        matSort style="width: 100%">
                            <ng-container matColumnDef="formulaId">
                                <th class="mat-column-width" mat-header-cell *matHeaderCellDef mat-sort-header>#</th>
                                <td mat-cell *matCellDef="let element"> {{element.formulaId}} </td>
                            </ng-container>
                            <ng-container matColumnDef="formulaName">
                                <th class="mat-column-width" mat-header-cell *matHeaderCellDef mat-sort-header>Name</th>
                                <td mat-cell *matCellDef="let element">
                                    <a [routerLink]="['../../../../ui/commissions/viewBaseFormula',element.formulaId]" (click)="viewBaseFormula()" class="search-link">
                                        {{element.formulaName}}
                                    </a>
                                </td>
                            </ng-container>
                            <ng-container matColumnDef="formulaDescription">
                                <th class="mat-column-width" mat-header-cell *matHeaderCellDef mat-sort-header> Description </th>
                                <td mat-cell *matCellDef="let element"> {{element.formulaDescription}} </td>
                            </ng-container>
                            <tr mat-header-row *matHeaderRowDef="formulaColumns"></tr>
                            <tr mat-row *matRowDef="let row; columns: formulaColumns;"></tr>
                        </table>
                        <mat-paginator #formulaPaginator [pageSizeOptions]="[ 10, 20]"
                            aria-label="Select page of periodic elements"></mat-paginator>
                    </div>
                </div>
            </div>
        </div>    
    <ng-container *ngIf="!disableSpinner">
        <div class="spinner-container">
          <img *ngIf="isEnableLoader" src="assets/images/Sun-GIF.gif" alt="Trinity Loading Spinner" class="spinner" style="width:300px !important;height: 170px !important;">
        </div>
    </ng-container>
</div>
