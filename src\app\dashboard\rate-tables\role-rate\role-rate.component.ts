import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, DatePipe } from '@angular/common';
import { Component, OnInit, ViewChild } from '@angular/core';
import { UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { MatDialog } from '@angular/material/dialog';
import { MatPaginator } from '@angular/material/paginator';
import { MatSort } from '@angular/material/sort';
import { MatTableDataSource } from '@angular/material/table';
import { ToastrService } from 'ngx-toastr';
import { ApiService } from 'src/app/services/api.service';
import { groupBy } from 'src/app/shared/group-by';
import { RoleRateDialogComponent } from '../role-rate-dialog/role-rate-dialog.component';

@Component({
  selector: 'app-role-rate',
  templateUrl: './role-rate.component.html',
  styleUrls: ['./role-rate.component.css']
})
export class RoleRateComponent implements OnInit {
  roleRateForm: UntypedFormGroup;
  allRoleRates:any
  activeRoleRates:any;
  roleRate:any;
  tieredData:any;
  stateData:any;
  columnNames = [
    {
      id: "role",
      value: "Role"
    }, 
    {
      id: "tierName",
      value: "Tier"
    }, 
    {
      id: "stateCode",
      value: "State"
    }, 
    {
      id: "amount",
      value: "Amount"
    }, 
    {
      id: "effectiveStartDate",
      value: "Start Date",
      dataType:'Date'

    }, 
    {
      id: "effectiveEndDate",
      value: "End Date",
      dataType:'Date'
    }, 
  ];
  originalDataSource;
  dataSource;
  displayedColumns = [];
  dateColumns:any;
  roleNames: string[] = ["VP", "RM", "SRM", "RVP", "SM", "ADM", "TM"];
  @ViewChild(MatSort, { static: true }) sort: MatSort;
  @ViewChild(MatPaginator, { static: true }) paginator: MatPaginator;
  addInd: boolean = false;
  constructor(public apiService: ApiService, private toastMsg: ToastrService, private formBuilder: UntypedFormBuilder,
    private datePipe: DatePipe,private currencyPipe: CurrencyPipe, private dialog: MatDialog) {
  }

  ngOnInit(): void {
    if (!this.apiService.checkPermission('ViewRateTables')) {
      this.apiService.goBack();
      this.toastMsg.error("Insufficient Permissions. Please make sure your account can access this information.", "Permissions");
    }
    this.roleRateForm = this.formBuilder.group({
      roleName: [, [Validators.required]],
      tierName: [, [Validators.required]],
      state: [0, [Validators.required]],
      amount: [0, [Validators.required]],
      effectiveStartDate: ['', [Validators.required]],
      effectiveEndDate: [''],
    });
    this.dateColumns = this.columnNames.filter(s => s.dataType =='Date');
    this.displayedColumns = this.columnNames.map(x => x.id);
    this.getActiveRoleRates();
    this.getAllRoleRates();
    this.getTieredData();
    this.getStateData();

  }
  getAllRoleRates() {
    this.apiService.get('BatteryOverrideTieredRates/getAllBatteryOverrideRate')
      .subscribe(data => {
        this.allRoleRates = data.result;   
      }, (err: any) => {
        this.toastMsg.error(err.message, 'Server Error!');
      });
  }
  getActiveRoleRates() {
    this.apiService.get('BatteryOverrideTieredRates/getActiveBatteryOverrideRate')
      .subscribe(data => {
        this.activeRoleRates = data.result;  
        this.createTable(); 
      }, (err: any) => {
        this.toastMsg.error(err.message, 'Server Error!');
      });
  }
  getTieredData() {
    this.apiService.get('BatteryOverrideTieredRates/dropdowns')
      .subscribe(data => {
        this.tieredData = data;   
      }, (err: any) => {
        this.toastMsg.error(err.message, 'Server Error!');
      });
  }
  getStateData() {
    this.apiService.get('BatteryCommissionRateMaintenance/dropdowns')
      .subscribe(data => {
        this.stateData = data;   
      }, (err: any) => {
        this.toastMsg.error(err.message, 'Server Error!');
      });
  }
  createTable() {
    let tableArr: Element[] = [];
    for (let i: number = 0; i <= this.activeRoleRates.length - 1; i++) {
      let currentRow = this.activeRoleRates[i];
      // if(i==0)
      // {
      //   this.tableArr[0] =this.activeRoleRates[0];
      // }
      tableArr.push({
        effectiveEndDate: this.datePipe.transform(currentRow.effectiveEndDate), 
        effectiveStartDate: this.datePipe.transform(currentRow.effectiveStartDate),
        role: currentRow.role,
        batteryOverrideTieredRateId: currentRow.batteryOverrideTieredRateId,
        batteryTierId: currentRow.batteryTierId,
        stateCode: currentRow.stateCode,
        stateCodeId: currentRow.stateCodeId,
        tierName: currentRow.tierName,
        amount: this.currencyPipe.transform(currentRow.amount),
      });
    }
    this.dataSource = new MatTableDataSource(tableArr);
    this.originalDataSource = tableArr;
    this.dataSource.sort = this.sort;
    this.dataSource.paginator = this.paginator;
  }
  rowClick(event:any){
    var roleRate = this.allRoleRates.filter(x => x.role === event.role && x.batteryTierId === event.batteryTierId && x.stateCodeId === event.stateCodeId);
    this.roleRate = roleRate;
    roleRate = Object.values(groupBy(roleRate, 'effectiveStartDate'));
    const dialogRef = this.dialog.open(RoleRateDialogComponent, {
      width: '80%', data: { roleRate }
    });
    if(this.roleRate && this.roleRate.length > 0){
      let effectiveDate = this.transformDate(this.roleRate[0]?.effectiveStartDate);
      let effectiveEndDate = this.transformDate(this.roleRate[0]?.effectiveEndDate);
      this.roleRateForm.controls['tierName'].setValue(this.roleRate[0]?.batteryTierId);
      this.roleRateForm.controls['roleName'].setValue(this.roleRate[0]?.role);
      this.roleRateForm.controls['amount'].setValue(this.roleRate[0]?.amount);
      this.roleRateForm.controls['state'].setValue(this.roleRate[0]?.stateCodeId);
      this.roleRateForm.controls['effectiveStartDate'].setValue(effectiveDate);
      this.roleRateForm.controls['effectiveEndDate'].setValue(effectiveEndDate);
    }
    dialogRef.afterClosed().subscribe(result => {
    });
  }
  transformDate(dateStr: string): string | null {
    return this.datePipe.transform(dateStr, 'yyyy-MM-dd');
  }
  onAdd(){
    this.addInd = !this.addInd;
    this.roleRateForm.reset();
      this.roleRateForm.clearValidators();
  }
  onSubmit(){
    if (!this.roleRateForm.invalid) {
      var body = {
        batteryTierId: +this.roleRateForm.controls.tierName.value,
        role: this.roleRateForm.controls.roleName.value,
        amount: this.roleRateForm.controls.amount.value,
        StateCodeId: +this.roleRateForm.controls.state.value,
        effectiveStartDate: this.roleRateForm.controls.effectiveStartDate.value,
        effectiveEndDate: this.roleRateForm.controls.effectiveEndDate.value,
      } 
      this.apiService.post('BatteryOverrideTieredRates/AddBatteryOverrideRate', body)
        .subscribe(data => {
          this.toastMsg.success('Role Rates Successfully Added');
          this.getAllRoleRates();
          this.getActiveRoleRates();
          this.addInd = !this.addInd;
        }, 
        (err: any) => {
          if (err?.err?.message)
            this.toastMsg.error(err.message, "Server Error!");
          if (typeof err === "string") {
            let isJsonObject = false;            
            let parsedErr: any;
            try {
              parsedErr = JSON.parse(err);
              isJsonObject =parsedErr && typeof parsedErr === "object" &&!Array.isArray(parsedErr);
            } catch (e) {
              isJsonObject = false;
            }
            if (isJsonObject) {
              this.toastMsg.error(parsedErr.result, "Error!");
            } else this.toastMsg.error(err, "Error!");
          }
          if (err?.result) {
            this.toastMsg.error(err.result, "Error!");
          }
        }
      );
    }
  }
  clearDate(date: HTMLInputElement,key:string,event:any) {
    date.value = "";
    if(key =='startDate')
      this.roleRateForm.controls.effectiveStartDate.setValue('');
    if(key =='endDate')
      this.roleRateForm.controls.effectiveEndDate.setValue('');
    event.stopPropagation();
  }
}
export interface Element {
 amount:string,
 batteryOverrideTieredRateId:number,
 batteryTierId:number,
 effectiveEndDate:string,
 effectiveStartDate:string,
 role:string,
 stateCode:string,
 stateCodeId:number,
 tierName:string
}
