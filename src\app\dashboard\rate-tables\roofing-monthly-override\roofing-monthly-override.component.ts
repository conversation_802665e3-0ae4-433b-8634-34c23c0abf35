import { DatePipe, DecimalPipe } from '@angular/common';
import { Component, OnInit, ViewChild } from '@angular/core';
import { UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { MatDialog } from '@angular/material/dialog';
import { MatPaginator } from '@angular/material/paginator';
import { MatSort } from '@angular/material/sort';
import { MatTableDataSource } from '@angular/material/table';
import { DEFAULT_IDB_DB_NAME } from '@ngx-pwa/local-storage/lib/tokens';
import { IDropdownSettings } from 'ng-multiselect-dropdown';
import { ToastrService } from 'ngx-toastr';
import { Observable, OperatorFunction } from 'rxjs';
import { debounceTime, distinctUntilChanged, filter, map } from 'rxjs/operators';
import { IContact } from 'src/app/model/one-time-payment.model';
import { ApiService } from 'src/app/services/api.service';
import { groupBy } from 'src/app/shared/group-by';
import { RoofingMonthlyOverrideDialogComponent } from '../roofing-monthly-override-dialog/roofing-monthly-override-dialog.component';

@Component({
  selector: 'app-roofing-monthly-override',
  templateUrl: './roofing-monthly-override.component.html',
  styleUrls: ['./roofing-monthly-override.component.css']
})
export class RoofingMonthlyOverrideComponent implements OnInit {
  columnNames = [{
    id: "contactName",
    value: "Contact Name"
  },
  {
    id: "paycomId",
    value: "Paycom Id"
  },
  {
    id: "salesDivision",
    value: "Sales Division"
  },
  {
    id: "team",
    value: "Team"
  },
  {
    id: "salesOffice",
    value: "Sales Office"
  },
  {
    id: "productType",
    value: "Product Type"
  },
  {
    id: "roofRateTypeName",
    value: "Type"
  },
  {
    id: "excludeMinCommission",
    value: "Exclude Minimum Commission"
  },
  {
    id: "rateAmount",
    value: "Rate Amount"
  },
  
  {
    id: "netDesc",
    value: "Net/Gross"
  },
  {
    id: "netValue",
    value: "Net Value"
  },
  {
    id: "roofInstallRangeName",
    value: "Roof Install Range Name"
  },
  {
    id: "effectiveStartDate",
    value: "Effective Start Date",
    dataType:'Date'
  },
  {
    id: "effectiveEndDate",
    value: "Effective End Date",
    dataType:'Date'
  }];
displayedColumns = [];
roofingData: any;
allRoofingData: any;
monthlyOverrides: any;
dropdownData: any;
tableArr: any[] = [];
dataSource: any;
originalDataSource: any;
addRow: boolean = false;
editRow: boolean = false;
disableDateField:boolean = false
roofEmployeeMonthlyOverrideRateId:number = 0;
roofingMonthlyForm: UntypedFormGroup;
roofingMonthlyEditForm: UntypedFormGroup;
contacts: IContact[] = [];
contactName: string;
roofInstallData: any;
roofRateData: any;
salesDivision: string;
salesOffice: string;
effectiveStartDate: string;
effectiveEndDate: string;
salesDivisions = ["Sales- Direct", "Sales- Traditional", "Sales- Outreach","Sales- Inside","All"];
productTypeList= ["Roof", "RR", "RRR"];
teamsData = ["All","Roofing","Non-Roofing"];
dropdownSettings:IDropdownSettings = {};
salesOffices: string[] = [];
dateColumns:any;
@ViewChild(MatSort, { static: true }) sort: MatSort;
@ViewChild(MatPaginator, { static: true }) paginator: MatPaginator;

formatter = (c: IContact) => c.contactName;
search: OperatorFunction<string, readonly { contactId, contactName }[]> = (text$: Observable<string>) => text$.pipe(
  debounceTime(0),
  distinctUntilChanged(),
  filter(term => term.length >= 2),
  map(term => this.contacts.filter(c => new RegExp(term, 'mi').test(c.contactName)).slice(0, 10))
)

  constructor(private formBuilder: UntypedFormBuilder, public apiService: ApiService, private toastMsg: ToastrService, private datePipe: DatePipe,private dialog: MatDialog) { }

  ngOnInit() {
    this.displayedColumns = this.columnNames.map(x => x.id);
    this.dateColumns = this.columnNames.filter(s => s.dataType =='Date');
    this.getRoofingOverride();
    this.getAllRoofingOverride();
    this.getDropdownData();
    this.initializeForm();
  }

  getRoofingOverride() {
    this.apiService.get('RoofEmployeeMonthlyOverrideRate/GetActiveMonthlyRoofingOverride')
      .subscribe(data => {
          this.roofingData = data;
          this.createTable();
      }, err => {
        this.toastMsg.error(err.message, "Error!");
      })
  }
  getAllRoofingOverride() {
    this.apiService.get('RoofEmployeeMonthlyOverrideRate/GetAllMonthlyRoofingOverride')
      .subscribe(data => {
          this.allRoofingData = data;
      }, err => {
        this.toastMsg.error(err.message, "Error!");
      })
  }
  getDropdownData() {
    this.apiService.get('RoofEmployeeMonthlyOverrideRate/dropdowns')
      .subscribe(data => {
          this.dropdownData = data;
          if (data && data['contacts']) {
            this.contacts = data['contacts'].map(type => { return <IContact>{ contactId: type.id, contactName: type.name } })
          }
          if(data && data['salesOffices']){
            this.salesOffices = data['salesOffices'].map(office => { return <string>office });
          }
          if(data && data['roofInstalls']){
            this.roofInstallData = data['roofInstalls'];
          }
          if(data && data['roofRates']){
            this.roofRateData = data['roofRates'];
          }
      }, err => {
        this.toastMsg.error(err.message, "Error!");
      })
  }
  createTable() {
    let tableArr: any[] = [];
    for(let i:number = 0; i <= this.roofingData.length - 1; i++) {
      let currentRow = this.roofingData[i];
      let selectedProductItems = [];
      let productTypeValue:string;
      if(currentRow.roofInd) selectedProductItems.push('Roof');
      if(currentRow.rrInd) selectedProductItems.push('RR');
      if(currentRow.rrrInd) selectedProductItems.push('RRR');
      productTypeValue = selectedProductItems.join().replace(/,/g, ':');
      tableArr.push({
        contactId: currentRow.contactId,
        contactName: currentRow.contactName,
        effectiveStartDate: this.datePipe.transform(currentRow.effectiveStartDate),
        effectiveEndDate: this.datePipe.transform(currentRow.effectiveEndDate),
        excludeMinCommission: currentRow.excludeMinCommission,
        netInd: currentRow.netInd,
        netDesc:currentRow.netInd == true ? 'Net':'Gross',
        netValue: currentRow.netValue,
        paycomId: currentRow.paycomId,
        rateAmount: currentRow.rateAmount,
        roofEmployeeMonthlyOverrideRateId: currentRow.roofEmployeeMonthlyOverrideRateId,
        roofInd: currentRow.roofInd,
        roofInstallRangeId: currentRow.roofInstallRangeId,
        roofInstallRangeName: currentRow.roofInstallRangeName,
        roofRateTypeId: currentRow.roofRateTypeId,
        roofRateTypeName: currentRow.roofRateTypeName,
        rrInd: currentRow.rrInd,
        rrrInd: currentRow.rrrInd,
        productType:productTypeValue,
        salesDivision: currentRow.salesDivision,
        salesOffice: currentRow.salesOffice,
        team: currentRow.team,
      });
    }
    this.dataSource = new MatTableDataSource(tableArr);
    this.originalDataSource = tableArr;
    this.dataSource.sort = this.sort;
    this.dataSource.paginator = this.paginator;
  }
  onAdd() {
      this.addRow = !this.addRow;
      this.editRow = false;
      this.initializeForm();
  }
  onSubmit() {
    var values = {
      newRoofEmployeeMonthlyOverrideRate:{
      contactId: this.roofingMonthlyForm.controls.contactId.value.contactId,
      salesDivision: this.roofingMonthlyForm.controls.salesDivision.value,
      salesOffice: this.roofingMonthlyForm.controls.salesOffice.value,
      rateAmount: this.roofingMonthlyForm.controls.rateAmount.value,
      excludeMinCommission: this.roofingMonthlyForm.controls.excludeMinCommission.value,
      roofInstallRangeId:this.roofingMonthlyForm.controls.roofInstallRange.value ? +this.roofingMonthlyForm.controls.roofInstallRange.value:null,
      roofRateTypeId: +this.roofingMonthlyForm.controls.roofRateTypeId.value,
      netInd: +this.roofingMonthlyForm.controls.netInd.value,
      netValue: this.roofingMonthlyForm.controls.netValue.value,
      roofInd:this.roofingMonthlyForm.controls.productType.value ? this.roofingMonthlyForm.controls.productType.value.some(s=> s=='Roof'):false,
      rrrInd:this.roofingMonthlyForm.controls.productType.value ? this.roofingMonthlyForm.controls.productType.value.some(s=> s=='RRR'):false,
      rrInd:this.roofingMonthlyForm.controls.productType.value ? this.roofingMonthlyForm.controls.productType.value.some(s=> s=='RR'):false,
      effectiveStartDate: this.roofingMonthlyForm.controls.effectiveStartDate.value,
      team: this.roofingMonthlyForm.controls.team.value,
      }
    }
      this.apiService.post('RoofEmployeeMonthlyOverrideRate', values)
      .subscribe(data => {
        this.toastMsg.success('Roofing monthly overrides Added Successfully');
        this.getRoofingOverride();
        this.getAllRoofingOverride();
        this.addRow = !this.addRow;
        this.initializeForm();
      }, (err: any) => {
          if (err?.err?.message)
            this.toastMsg.error(err.message, "Server Error!");
          if (typeof err === "string") {
            let isJsonObject = false;            
            let parsedErr: any;
            try {
              parsedErr = JSON.parse(err);
              isJsonObject =parsedErr && typeof parsedErr === "object" &&!Array.isArray(parsedErr);
            } catch (e) {
              isJsonObject = false;
            }
            if (isJsonObject) {
              this.toastMsg.error(parsedErr.result, "Error!");
            } else this.toastMsg.error(err, "Error!");
          }
          if (err?.result) {
            this.toastMsg.error(err.result, "Error!");
          }
        });
    
  }
  onEditSubmit() {
      var values = {
        newRoofEmployeeMonthlyOverrideRate:{
        roofEmployeeMonthlyOverrideRateId:this.roofEmployeeMonthlyOverrideRateId,
        effectiveEndDate: this.roofingMonthlyEditForm.controls.effectiveEndDate.value
        }
      }
      this.apiService.post('RoofEmployeeMonthlyOverrideRate/UpdateMonthlyRoofingOverride', values)
      .subscribe(data => {
        this.toastMsg.success('Roofing monthly overrides Updated Successfully');
        this.getRoofingOverride();
        this.getAllRoofingOverride();
        this.editRow = !this.editRow;
        this.initializeForm();
      }, (err: any) => {
          if (err?.err?.message)
            this.toastMsg.error(err.message, "Server Error!");
          if (typeof err === "string") {
            let isJsonObject = false;            
            let parsedErr: any;
            try {
              parsedErr = JSON.parse(err);
              isJsonObject =parsedErr && typeof parsedErr === "object" &&!Array.isArray(parsedErr);
            } catch (e) {
              isJsonObject = false;
            }
            if (isJsonObject) {
              this.toastMsg.error(parsedErr.result, "Error!");
            } else this.toastMsg.error(err, "Error!");
          }
          if (err?.result) {
            this.toastMsg.error(err.result, "Error!");
          }
        });
  }

rowClick(event: any) {
  this.editRow = true;
  this.addRow = false;
  this.roofEmployeeMonthlyOverrideRateId = event.roofEmployeeMonthlyOverrideRateId;
  var monthlyOverrides = this.allRoofingData.filter(x => x.contactId === event.contactId && x.salesDivision === event.salesDivision && x.salesOffice === event.salesOffice && x.team == event.team && x.roofInstallRangeId == event.roofInstallRangeId);
  this.monthlyOverrides = monthlyOverrides;
  monthlyOverrides = Object.values(groupBy(monthlyOverrides, 'effectiveStartDate'));
  const dialogRef = this.dialog.open(RoofingMonthlyOverrideDialogComponent, {
    width: '80%', data: { monthlyOverrides }
  });
  if(this.monthlyOverrides && this.monthlyOverrides.length > 0){
    this.roofingMonthlyEditForm.controls['contactId'].setValue(event.contactId);
    this.roofingMonthlyEditForm.controls['contactName'].setValue(event.contactName);
    this.roofingMonthlyEditForm.controls['salesDivision'].setValue(event.salesDivision);
    this.roofingMonthlyEditForm.controls['salesOffice'].setValue(event.salesOffice);
    this.roofingMonthlyEditForm.controls['rateAmount'].setValue(event.rateAmount);
    this.roofingMonthlyEditForm.controls['team'].setValue(event.team);
    this.roofingMonthlyEditForm.controls['roofInstallRange'].setValue(event.roofInstallRangeName);
    this.roofingMonthlyEditForm.controls['roofRateTypeId'].setValue(event.roofRateTypeName);
    this.roofingMonthlyEditForm.controls['netInd'].setValue(event.netInd ? '1':'0');
    this.roofingMonthlyEditForm.controls['netValue'].setValue(event.netValue);
    this.roofingMonthlyEditForm.controls['excludeMinCommission'].setValue(event.excludeMinCommission);
    let selectedItems = [];
    if(event.roofInd) selectedItems.push('Roof');
    if(event.rrInd) selectedItems.push('RR');
    if(event.rrrInd) selectedItems.push('RRR');
    this.roofingMonthlyEditForm.controls['productType'].setValue(selectedItems);
    this.roofingMonthlyEditForm.controls['effectiveStartDate'].setValue(this.datePipe.transform(event.effectiveStartDate, 'yyyy-MM-dd'));
    this.roofingMonthlyEditForm.controls['effectiveEndDate'].setValue(this.datePipe.transform(event.effectiveEndDate, 'yyyy-MM-dd'));
    if(event.effectiveEndDate) {
      this.disableDateField = true;
      this.roofingMonthlyEditForm.controls['effectiveEndDate'].disable();
    }
    else{
      this.disableDateField = false;
      this.roofingMonthlyEditForm.controls['effectiveEndDate'].enable();
    }
  }
  dialogRef.afterClosed().subscribe(result => {
  });
}


clearAddStartDate(date: HTMLInputElement) {
  date.value = "";
  this.roofingMonthlyForm.controls.effectiveStartDate.setValue('');
}

clearEditEndDate(date: HTMLInputElement) {
  date.value = "";
  this.roofingMonthlyEditForm.controls.effectiveEndDate.setValue('');
}

initializeForm(){
  this.roofingMonthlyForm = this.formBuilder.group({
    contactId: ["", [Validators.required]],
    contactName: ["",],
    salesDivision: ["", [Validators.required]],
    salesOffice: ["", [Validators.required]],
    rateAmount: [null, [Validators.required]],
    excludeMinCommission: [true],
    roofRateTypeId: [null,[Validators.required]],
    roofInstallRange: [null],
    netInd: ["0"],
    netValue: [null],
    productType: [''],
    effectiveStartDate: ["", [Validators.required]],
    team: ["", [Validators.required]],
  });
  this.roofingMonthlyEditForm = this.formBuilder.group({
    contactId: [{value: 0, disabled: true}],
    contactName: [{value: "", disabled: true}],
    salesDivision: [{value: "", disabled: true}],
    salesOffice: [{value: "", disabled: true}],
    rateAmount: [{value: "", disabled: true}],
    excludeMinCommission: [{value: true, disabled: true}],
    roofRateTypeId: [{value: "", disabled: true}],
    roofInstallRange: [{value: "", disabled: true}],
    netInd: [{value: "0", disabled: true}],
    netValue: [{value: "", disabled: true}],
    productType: [{value: [], disabled: true}],
    effectiveStartDate: [{value: "", disabled: true}],
    effectiveEndDate: ["", [Validators.required]],
    team: [[{value: "", disabled: true}]],
  });
}
}
