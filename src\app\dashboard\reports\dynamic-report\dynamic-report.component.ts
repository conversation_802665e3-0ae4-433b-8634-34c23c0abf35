import { ChangeDetectorRef, Component, OnInit, ViewChild } from '@angular/core';
import { ApiService } from 'src/app/services/api.service';
import { environment } from "src/environments/environment";
import { ToastrService } from 'ngx-toastr';
import { IOutreachOpportunity, OutreachOpportunity } from 'src/app/model/outreach-opportunity.model';
import { HttpClient, HttpParams } from '@angular/common/http';
import { ApiResponse } from 'src/app/services/api.response';
import * as FileSaver from 'file-saver';
import { MatPaginator } from '@angular/material/paginator';
import { MatSort } from '@angular/material/sort';
import { MatTableDataSource } from '@angular/material/table';
declare var $: any;

@Component({
  selector: 'app-dynamic-report',
  templateUrl: './dynamic-report.component.html',
  styleUrls: ['./dynamic-report.component.css']
})
export class DynamicReportComponent implements OnInit {
  // @ViewChild(MatSort, { static: true }) sort: MatSort;
  @ViewChild(MatSort) set content(sort: MatSort) {
    this.dynamicReport.sort = sort;
  } 
  @ViewChild(MatPaginator, { static: true }) paginator: MatPaginator;
  dynamicReport: MatTableDataSource<any> = new MatTableDataSource([]);
  pageSizeOptions: number[] = [10, 20, 50];
  pageSize: number = 20;
  reportNames: any[] = [];
  selectedReports: any[] = [];
  filteredReportNames: any[] =[];
  categoryNames: any[] = [];
  filteredcategoryNames: any[] =[];
  productNames: any[] = ["Solar", "Roof", "Battery", "R&R"];
  productReportMapping: any[] = [];
  filter: boolean = false;
  filterBox: boolean = false;
  result: boolean = false;
  filterFields: any[] = [];
  filterValues: any[] = [];
  filterString: string = "";
  columns: Array<any>;
  displayedColumns: Array<any>;
  selectedReportId: number;
  selectedReportName: string = '';
  selectReportCategoryId: number = 0;
  selectProductId: number;
  description: string = "Select report to view the report filter and result.";
  public date: Date;
  selectedProductName: string;
  sortField: string;
  sortColumn: string ='';
  sortOrder: string ='';  
  pageNumber: number = 1;
  totalCount: number;
  fromDate:any;

  constructor(private apiService: ApiService, private toastMsg: ToastrService, private http: HttpClient,private changeDetectorRef: ChangeDetectorRef) { }

  ngOnInit() {
    // if (!this.apiService.checkPermission('DynamicReports')) {
    //   this.apiService.goBack();
    //   this.toastMsg.error("Insufficient Permissions. Please make sure your account can access this information.", "Permissions");
    // }
    this.getReportCategoriesNames();
    this.getProductNames();
  }

  ngAfterViewInit() {
    // this.dynamicReport.sort = this.sort;
  }

  getReportCategoriesNames() {
    this.apiService.get('DynamicReport/ReportCategories')
      .subscribe(data => {
        if (data["statusCode"] === "201" && data.result) {
          this.categoryNames = data.result;
          this.getReportNames();
        }
      }, (err: any) => {
        this.toastMsg.error(err.message, 'Server Error!')
      });
  }

  getProductNames() {
    this.apiService.get('DynamicReport/ProductReportMapping')
      .subscribe(data => {
        if (data["statusCode"] === "201" && data.result) {
          this.productReportMapping = data.result;
        }
      }, (err: any) => {
        this.toastMsg.error(err.message, 'Server Error!')
      });
  }

  onChangeProduct(product: any) {
    this.selectedProductName = product.target.value;
    var filteredcategoryIds = [];
    filteredcategoryIds = this.productReportMapping.filter(r => r.productName == product.target.value);
    this.filteredcategoryNames = [];
    for (let i: number = 0; i < filteredcategoryIds.length; i++) {
      var c = this.categoryNames.filter(r => r.dynamicReportCategoryId == filteredcategoryIds[i].dynamicReportCategoryId);
      var foundArray = this.filteredcategoryNames.filter(r => r.categoryName == c[0].categoryName);
      if (foundArray.length == 0) {
        this.filteredcategoryNames.push(c[0]);
      }
    }
    this.filteredcategoryNames.sort(this.dynamicSort("categoryName"));
    this.dynamicReport.data = [];
    this.filteredReportNames = [];
    this.selectReportCategoryId = 0;
    this.filterBox = false;
    this.filter = false;
    this.result = false;
    this.description = "Select report to view the report filter and result.";
  }

  dynamicSort(property) {
    var sortOrder = 1;
    if(property[0] === "-") {
        sortOrder = -1;
        property = property.substr(1);
    }
    return function (a,b) {
        /* next line works with strings and numbers, 
         * and you may want to customize it to your needs
         */
        var result = (a[property] < b[property]) ? -1 : (a[property] > b[property]) ? 1 : 0;
        return result * sortOrder;
    }
}

  onChangeCategory(category: any) {
    
    var filteredReportNamesLevel1 = this.reportNames.filter(r => r.reportCategoryId == category.target.value);

    // apply product filters
    var filteredReportIdsLevel2 = this.productReportMapping.filter(r => r.dynamicReportCategoryId == category.target.value && r.productName == this.selectedProductName);
    this.filteredReportNames = [];
    for (let i: number = 0; i < filteredReportIdsLevel2.length; i++) {
      var c = filteredReportNamesLevel1.filter(r => r.dynamicReportId == filteredReportIdsLevel2[i].dynamicReportId);
      if (c.length > 0) {
        this.filteredReportNames.push(c[0]);
      }
    }
    this.filteredReportNames.sort(this.dynamicSort("reportName"));
    this.dynamicReport.data = [];
    this.filterBox = false;
    this.filter = false;
    this.result = false;
    this.description = "Select report to view the report filter and result.";
  }

  getReportNames() {
    this.apiService.get('DynamicReport/RetrieveAll')
      .subscribe(data => {
        if (data["statusCode"] === "201" && data.result) {
          this.reportNames = data.result;
        }
      }, (err: any) => {
        this.toastMsg.error(err.message, 'Server Error!')
      });
  }

  onChangeReport(report: any) {
    if (report.target.value == "0" || report.target.value == "Select Report") {
      this.description = "Select report to view the report filter and result.";
      return;
    }
    this.getFilterFields(report.target.value);
    this.dynamicReport.data = [];
    this.filterBox = true;
    this.filter = true;
    this.result = false;
    this.selectedReportId = report.target.value;
    this.selectedReports = this.reportNames.filter(r => r.dynamicReportId == report.target.value);
    if (this.selectedReports.length > 0) {
      this.description = this.selectedReports[0].reportDescription;
      this.selectedReportName = this.selectedReports[0].reportName;
    }
  }

  getFilterFields(id: number) {
    this.apiService.get('DynamicReport/RetrieveFilters?dynamicReportId=' + id)
      .subscribe(data => {
        if (data["statusCode"] === "201" && data.result) {
         this.filterFields = data.result;
        }
      }, (err: any) => {
        this.toastMsg.error(err.message, 'Server Error!')
      });
  }

  getValue(val: any) {
    let values = val.split(':');
    if (values.length == 3 ) {
      if (values[0] == "URL") {
        return "<a href='" + values[1] + "'>" + values[2] + "</a>";
      }
      if (values[0] == "API") {
        let url= values[1] as string;
        url = environment.apiBaseUrl + url;
        return "<a href='" + url + "'>" + values[2] + "</a>";
      }
    }
    return val;
  }

  getReportData(id: number, filter: string) {    
    this.columns = []
    this.sortColumn ='';
    this.sortOrder ='';  
    this.pageNumber = 1;
    this.apiService.get('DynamicReport/RetrieveResult?dynamicReportId=' + id + "&filterString=" + filter+'&pageNumber='+this.pageNumber+'&pageSize='+this.pageSize+'&sortColumn='+this.sortColumn+'&sortDirection='+this.sortOrder)
      .subscribe(data => {        
        if (data["statusCode"] === "201" && data.result) {
          this.dynamicReport.data.length = 0;
          this.dynamicReport.data = data.result.data;          
          this.totalCount = data.result.totalCount;
          this.pageNumber= data.result.pageNumber;
          this.pageSize= data.result.pageSize;          
          this.result = true;          
          const columns = this.dynamicReport.data
            .reduce((columns, row) => {
              return [...columns, ...Object.keys(row)]
            }, [])
            .reduce((columns, column) => {
              if (column !== "rowNum") {
                return columns.includes(column) ? columns : [...columns, column];
              }              
              return columns;
            }, [])          
          this.columns = columns.map(column => {
            return {
              columnDef: column,
              header: column,
              cell: (element: any) => `${element[column] ? element[column] : ``}`,
              isHovered: false,
              sortOrder: 'asc'
            }            
          })   
          this.changeDetectorRef.markForCheck();
          this.displayedColumns = this.columns.map(c => c.columnDef);
        }
      }, (err: any) => {
        this.toastMsg.error(err.message, 'Server Error!')
      });
      this.changeDetectorRef.markForCheck();      
  } 

  getReports(id: number, filter: string){
    this.apiService.get('DynamicReport/RetrieveResult?dynamicReportId=' + id + "&filterString=" + filter+'&pageNumber='+this.pageNumber+'&pageSize='+this.pageSize+'&sortColumn='+this.sortColumn+'&sortDirection='+this.sortOrder)
      .subscribe(data => {
        if (data["statusCode"] === "201" && data.result) {
          this.dynamicReport.data.length = 0;
          this.dynamicReport.data = data.result.data;
          this.totalCount = data.result.totalCount;
          this.pageNumber= data.result.pageNumber;
          this.pageSize= data.result.pageSize;          
        }
      }, (err: any) => {
        this.toastMsg.error(err.message, 'Server Error!')
      });
  }  

  onSearchClick() {
    this.filterValues.length = 0;
    this.filterString = "";
    for (let i: number = 0; i < this.filterFields.length; i++) {
      this.filterValues.push({ name: this.filterFields[i].name, value: $('#f' + (i + 1)).val() })
      this.filterString = this.filterString + this.filterFields[i].name + "=" + $('#f' + (i + 1)).val() + ","
    }
    this.getReportData(this.selectedReportId, this.filterString);    

  }
  generate(){
    let fromDate ='';
    let toDate ='';
    let filterValues =[];
    for (let i: number = 0; i < this.filterFields.length; i++) {
      filterValues.push({ name: this.filterFields[i].name, value: $('#f' + (i + 1)).val() })
    }
    fromDate = filterValues[0].value;
    toDate = filterValues[1].value ? filterValues[1].value: null;
    let payload={
      startDate: fromDate,
      endDate: toDate
    }
     this.http.post(`${this.apiService.workflowBaseUrl}Workflow/RunPPAPlanExecutionV2`, payload)
      .subscribe((data: ApiResponse) => {
        if(data && data.statusCode == '201' &&  data.message == "Success"){
          this.toastMsg.success(data?.result);
        }  
      }, (err: any) => {
        this.toastMsg.error(err.message, "Server Error!");
      });
  }

  onSort(colheader,index){    
    colheader = colheader.replace("&", "_")
    this.sortField = colheader;
    this.sortColumn = colheader.replace(/\s+/g, '_');
    this.sortOrder = this.columns[index].sortOrder;    
    this.getReports(this.selectedReportId, this.filterString);    
    this.columns[index].sortOrder = this.columns[index].sortOrder === 'asc'? 'desc' : 'asc';
    this.columns.forEach((column, i) => {
      if (i !== index) {
        column.sortOrder = 'asc';
      }      
    });
    this.changeDetectorRef.markForCheck();
  }

    onPageChange(event) {      
      this.pageNumber = event.pageIndex + 1;
      this.pageSize = event.pageSize;
      this.getReports(this.selectedReportId, this.filterString);
    }
  getExcelWorksheet() {
    this.http.get(`${environment.apiBaseUrl}DynamicReport/export?dynamicReportId=${this.selectedReportId}&filterString=${this.filterString}`, { responseType: 'blob' })
      .subscribe(data => {
        this.downLoadFile(data, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8");
      }, err => {
        this.toastMsg.error(err.message, "Error!");
      });
  }

  /**
   * Method is use to download file.
   * @param data - Array Buffer data
   * @param type - type of the document.
   */
  downLoadFile(data: any, type: string) {
    let blob = new Blob([data], { type: type });
    let date: Date = new Date();

    FileSaver.saveAs(blob, `report_${date}.xlsx`);
  }

  openOpportunity(id) {    
    const url = `#/ui/commissions/opportunitydetails/${id}`;
    window.open(url, '_blank');
  }

  openContact (id) {    
    const url = `#/ui/commissions/salesrep/${id}`;
    window.open(url, '_blank');
  }

  toTitleCase(val: any) {       
    val = val.charAt(0).toUpperCase() + val.substr(1); 
    return val.replace('Ppa','PPA').replace('_',' ');
  }


  clearDate(date: HTMLInputElement) {
    date.value = "";
    this.date = null;    
        event.stopPropagation();
      }

  onChangeFilter() {
    let filterValues =[];
    for (let i: number = 0; i < this.filterFields.length; i++) {
      filterValues.push({ name: this.filterFields[i].name, value: $('#f' + (i + 1)).val() })
    }
    this.fromDate = filterValues[0].value;
    
      }


}