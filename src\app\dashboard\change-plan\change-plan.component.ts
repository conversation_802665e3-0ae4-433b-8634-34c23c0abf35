import { Component, OnInit } from '@angular/core';
import { Router, ActivatedRoute } from '@angular/router';
import { ApiService } from "../../services/api.service";
import { ToastrService } from 'ngx-toastr';
import { IRulePrompt } from 'src/app/model/rule-prompt.model';
import { IRuleInput } from 'src/app/model/rule-input.model';
import { IRuleItem } from 'src/app/model/rule-item.model';
import { IPrompt, Prompt } from 'src/app/model/prompt.model';
import { MatDialog } from '@angular/material/dialog';
import { EmployeeIncentiveDialogComponent, AddOnRule } from './employee-incentive-dialog/employee-incentive-dialog.component';
import { IRuleData } from 'src/app/model/rule-data.model';
import { IPlanDetail } from 'src/app/model/plan.model';
import { BasePayStructureDialogComponent } from './base-pay-structure-dialog/base-pay-structure-dialog.component';
import { PaymentBookDialogComponent } from './payment-book-dialog/payment-book-dialog.component';
import { PaymentBookScheduleDialogComponent } from './payment-book-schedule-dialog/payment-book-schedule-dialog.component';
import { AddPlanInclusionDialogComponent } from '../sales-rep-configuration/add-plan-inclusion-dialog/add-plan-inclusion-dialog/add-plan-inclusion-dialog.component';
import { IInclusionRules } from 'src/app/model/IInclusionRules';
declare var $: any;

@Component({
  selector: 'app-change-plan',
  templateUrl: './change-plan.component.html',
  styleUrls: ['./change-plan.component.css']
})
export class ChangePlanComponent implements OnInit {
  // basicPlan = [{"parent":"Base Pay","child":["On Watt Sold","Territory Base"]},{"parent":"Bonus","child":["Sonnova Lease","Sunnovas Loan"]},{"parent":"Bonus Incentives","child":["Backend","Intro - Install","Intro - Demo","Outreach Contact"]},
  // {"parent":"Rate Incentives","child":["High Flyer","President’s Club"]},{"parent":"Payment Books","child":["Draw","Straight Pay"]}];

  plansList: any;
  planDetails: any;
  contactsDetails: any;
  contactId: number;
  planId: any;
  basicPlan: any[] = [];
  planStartDate: string;
  planName: string;
  planstartday: string;
  allRules: any[];
  prompts: any[];
  promptsValues: object = {};
  metadataPrompts: Prompt[] = [];
  addOns: AddOnRule[] = [];
  hasBasePay: boolean = false;
  checked: boolean = true;
  continueLastPlan: boolean = false;
  excludedRules: any[] = [];
  excludedRuleTypes: any[] = [];
  planInclusionRules: IInclusionRules;

  constructor(private router: Router, public apiService: ApiService, private toastMsg: ToastrService, private activatedRoute: ActivatedRoute, private dialog: MatDialog) {
    // this.contactId = this.activatedRoute.snapshot.params.contact_id
  }

  ngOnInit() {
    this.activatedRoute.params.subscribe(params => {
      this.contactId = params.contact_id;

      if (!this.apiService.checkPermission('AssignPlan')) {
        this.apiService.goBack();
        this.toastMsg.error("Insufficient Permissions. Please make sure your account can access this information.", "Permissions");
      }
      $.fn.extend({
        treed: function (o) {

          var openedClass = 'fa-minus-circle';
          var closedClass = 'fa-plus-circle ';

          if (typeof o != 'undefined') {
            if (typeof o.openedClass != 'undefined') {
              openedClass = o.openedClass;
            }
            if (typeof o.closedClass != 'undefined') {
              closedClass = o.closedClass;
            }
          };

          //initialize each of the top levels
          var tree = $(this);
          tree.addClass("treeview");
          $('#tree1').find('li').has("ul").each(function () {
            var branch = $(this); //li with children ul
            // console.log(branch, "branch");
            branch.prepend("<i class='indicator fa " + closedClass + "'></i>");
            branch.addClass('branch');
            branch.on('click', function (e) {
              if (this == e.target) {
                var icon = $(this).children('i:first');
                icon.toggleClass(openedClass + " " + closedClass);
                $(this).children().children().toggle();
              }
            })
            branch.children().children().toggle();
          });
          //fire event from the dynamically added icon
          tree.find('.branch .indicator').each(function () {
            $(this).on('click', function () {
              $(this).closest('li').click();
            });
          });
          //fire event to open branch if the li contains an anchor instead of text
          tree.find('.branch>a').each(function () {
            $(this).on('click', function (e) {
              $(this).closest('li').click();
              e.preventDefault();
            });
          });
          //fire event to open branch if the li contains a button instead of text
          tree.find('.branch>button').each(function () {
            $(this).on('click', function (e) {
              $(this).closest('li').click();
              e.preventDefault();
            });
          });
        }
      });

      //Initialization of treeviews
      $('#tree1').treed();

      //Get plans list
      this.getPlanList()

      this.getRuleTypes()

      if (this.contactId)
        this.getContactsDetails();
    })
  }

  cancel() {
    this.router.navigate(['/ui/commissions/salesrep', this.contactId]);
  }

  includeRule(rule: any) {
    var found = false;
    if (rule.ruleTypeName == "Rate Incentive Goal" || rule.ruleTypeName == "Bonus Incentive Goal") {
      // prepare the list of excluded rule type ids
      for (let index = 0; index < this.excludedRuleTypes.length; index++) {
        if (this.excludedRuleTypes[index] == rule.ruleId && !rule.isInclusion) {
          // found it so remove 
          this.excludedRuleTypes[index] = 0;
          found = true;
          break;
        }
      }
      if (!found) {
        if (rule.isInclusion){
          this.allRules.splice(this.allRules.findIndex(x => x.ruleId == rule.ruleId && x.isInclusion), 1);
          let ruleArray = this.basicPlan.filter(item => item.parent == "Contact Plan Inclusions");
          if (ruleArray && ruleArray.length > 0) {
            ruleArray[0].child.splice(ruleArray[0].child.findIndex(x => x.ruleId == rule.ruleId && x.isInclusion), 1)
            // if (row.promptAssignPlan || (row.prompts && row.prompts.length > 0)) {
            //   this.prompts.push(row)
            // }
          }
        }
        else {
          this.excludedRuleTypes.push(rule.ruleId);
        }
      }
    } else {
      // prepare the list of excluded rule ids
      for (let index = 0; index < this.excludedRules.length; index++) {
        if (this.excludedRules[index] == rule.ruleId && !rule.isInclusion) {
          // found it so remove 
          this.excludedRules[index] = 0;
          found = true;
          break;
        }
      }
      if (!found) {
        if (rule.isInclusion){
          this.allRules.splice(this.allRules.findIndex(x => x.ruleId == rule.ruleId && x.isInclusion), 1);
          let ruleArray = this.basicPlan.filter(item => item.parent == "Contact Plan Inclusions");
          if (ruleArray && ruleArray.length > 0) {
            ruleArray[0].child.splice(ruleArray[0].child.findIndex(x => x.ruleId == rule.ruleId && x.isInclusion), 1)
            // if (row.promptAssignPlan || (row.prompts && row.prompts.length > 0)) {
            //   this.prompts.push(row)
            // }
          }
          if (rule.ruleTypeName == "Base Pay") {
            this.addOns.forEach(x => {
              if (x.basePayRuleId == rule.ruleId) {
                var reset = this.allRules.find(x => x.ruleTypeName == "Base Pay");
                x.basePayRuleId = reset.ruleId,
                x.basePayRuleName = reset.ruleName
              }
            })
          }
        }
        else {
          this.excludedRules.push(rule.ruleId);
        }
      }
    }
  }

  /**
   * Get plans list
   */
  getPlanList() {
    this.apiService.get('Plans')
      .subscribe(data => {
        if (data["statusCode"] === "201" && data.result) {
          this.plansList = data.result
          this.getRulePrompts();
          // console.log("Plan Details", this.plansList)
        } else {
          this.toastMsg.error("No plans found.", 'Server Error!')
        }
      }, (err: any) => {
        // console.log(err)
        this.toastMsg.error(err.message, 'Server Error!')
      });
  }

  // /**
  //  * Get plan details
  //  */
  // getPlanDetails(event: any){
  //   this.apiService.get('Plan/GetPlan/'+event.target.value)
  //   .subscribe(data => {
  //     if (data["statusCode"] === "201" && data.result) {
  //       this.planDetails = data.result
  // //       console.log("Plan Details", this.planDetails)
  //     }else{
  //       this.toastMsg.error("No plans found.", 'Server Error!')
  //     }
  //   }, (err: any) => {
  // //     console.log(err)
  //     this.toastMsg.error(err.message, 'Server Error!')
  //   });
  // }

  getRuleTypes() {
    this.apiService.get('getdata/GetRuleType')
      .subscribe(data => {
        if (data.statusCode === "201" && data.result) {
          data.result.forEach((row: any) => {
            if (["Employee Incentive", "Base Pay Structure", "Payment Book", "One Time Payment"].includes(row.ruleCd)) return;
            this.basicPlan.push({ "parent": row.ruleCd, "child": [""] });
            // Object.keys(this.basicPlan).forEach(key => {
            //   if (this.basicPlan[key] == )
            // })
            console.log("row: ", this.basicPlan);

          });
          // //console.log("rules", data.result)
          this.basicPlan.push({ "parent": "Contact Plan Inclusions", "child": [""] })
        }
      }, (err: any) => {
        this.toastMsg.error(err.message, 'Server Error!')
      });
  }

  /**
   * Get Plan details
   */
  changePlan(event: any) {
    this.allRules = [];
    this.prompts = [];
    this.promptsValues = {};
    this.addOns = [];
    this.planId = event.target.value
    this.excludedRules = [];
    this.excludedRuleTypes = [];
    this.basicPlan.forEach(item => {
      item.child = [];
    });

    this.apiService.get('Plans/' + event.target.value)
      .subscribe(data => {
        if (data["statusCode"] === "201" && data.result && data.result.planDetails) {
          // console.log("Selected Plan Details", data.result)
          data.result.planDetails.forEach((row: any) => {
            let ruleArray = this.basicPlan.filter(item => item.parent == row.ruleTypeName);
            if (ruleArray && ruleArray.length > 0) {
              ruleArray[0].child = [];
              row.rules.forEach((row1: any) => {
                if (row1.ruleName) {
                  let rule = {
                    ruleName: row1.ruleName,
                    ruleId: row1.ruleId,
                    promptAssignPlan: row1.promptAssignPlan,
                    ruleTypeName: row.ruleTypeName,
                    prompts: row1.prompts
                  };
                  ruleArray[0].child.push(rule);
                  this.allRules.push(rule);
                  if (row1.promptAssignPlan || (row1.prompts && row1.prompts.length > 0)) {
                    this.prompts.push(rule)
                  }
                }
              });
            }
          })
          this.getRulePrompts();
          // console.log("rules===", this.basicPlan);
          // console.log("Prompts:", this.prompts);
          //this.planStartDate = this.apiService.dateFormat(new Date())
          this.planName = data.result.planName
          //$('#planstartday').val(this.planStartDate)
        } else {
          this.toastMsg.error("No plans found.", 'Server Error!')
        }
      }, (err: any) => {
        // console.log(err)
        this.toastMsg.error(err.message, 'Server Error!')
      });
    // console.log("changed", this.basicPlan);
    // for(let i:number = 0; i < this.basicPlan.length; i++){
    //   if (this.basicPlan[i].parent == 'Base Pay') {
    //     if(this.basicPlan[i].child[0].length >= 1) {
    //       this.hasBasePay = true;
    //       console.log("found");
    //     } else {this.hasBasePay = false;}
    //   }
    // }
  }

  /**
   * Get contact details
   */
  getContactsDetails() {
    this.apiService.get('Contacts/' + this.contactId)
      .subscribe(data => {
        if (data["statusCode"] === "201" && data.result && data.result[0]) {
          this.contactsDetails = data.result[0];
          this.getPlanDetails();
          // console.log("Contacts Details", this.contactsDetails, this.planDetails)
        } else {
          this.toastMsg.error("No contacts found.", 'Server Error!')
        }
      }, (err: any) => {
        // console.log(err)
        this.toastMsg.error(err.message, 'Server Error!')
      });
  }

  /**
   * Get Rule Prompts
   */
  getRulePrompts(): void {
    if (!this.planId) {
      return;
    }

    this.apiService.get(`Rule/Prompts/${this.planId}`)
      .subscribe(data => {
        if (data && data.statusCode == "201") {
          this.metadataPrompts = data.result.map(prompt => {
            return <IPrompt>{
              ruleId: prompt.ruleId,
              schema: prompt.schema,
              tableName: prompt.tableName,
              columnName: prompt.columnName,
              displayName: prompt.displayName
            }
          }).map(iPrompt => {
            return <Prompt>{ ...iPrompt }
          });

          // console.log("Metadata Prompts", this.metadataPrompts);
        }
      }, (err: any) => {
        // this.toastMsg.error("Server Error", "Error");
        return;
      });
  }

  /**
   * Get plan details
   */
  getPlanDetails() {
    this.apiService.get('PlanAssign/' + this.contactId)
      .subscribe(data => {
        if (data["statusCode"] === "201" && data.result) {
          this.planDetails = data.result
          // console.log("Plan Details", this.planDetails)
        } else {
          this.toastMsg.error("No plans found.", 'Server Error!')
        }
      }, (err: any) => {
        // console.log(err)
        //this.toastMsg.error(err.message, 'Server Error!')
      });
  }

  /**
   * Create plan assign
   */
  submitPlanAssign() {

    if (this.checkBasePay()) {
      this.toastMsg.error("If a Base Pay is present a mapped Base Pay Structure must also be present", "Base Pay Structure Missing");
    }

    this.addOns.forEach(addOn => {
      var addOnPromptsValues = addOn.promptValues;
      
      if (addOn.basePayRuleId > 0) {
        addOnPromptsValues[addOn.ruleId].basePayCommissionRuleId = addOn.basePayRuleId;
      }

      if (addOnPromptsValues[addOn.ruleId] == null)
        addOnPromptsValues[addOn.ruleId] = {
          commissionRuleId: addOn.ruleId,
          ruleTypeName: addOn.ruleTypeName,
          basePayCommissionRuleId: 0
        };
      if (addOnPromptsValues[addOn.ruleId].commissionRuleId == null) {
        addOnPromptsValues[addOn.ruleId].commissionRuleId = addOn.ruleId;
        addOnPromptsValues[addOn.ruleId].ruleTypeName = addOn.ruleTypeName;
      }
      if (addOnPromptsValues[addOn.ruleId].ruleItems == null)
        addOnPromptsValues[addOn.ruleId].ruleItems = [];

      if (addOnPromptsValues[addOn.ruleId] != null)
        addOnPromptsValues[addOn.ruleId].ruleTypeName = addOn.ruleTypeName;

      // addOnPromptsValues[addOn.ruleId].ruleItems.push(<IRuleItem>{
      //   itemId: null,
      //   ruleItemInputs: addOn.prompts.map(p => {return <IRuleInput>{
      //     columnName: p.columnName,
      //     columnValue: addOn.promptValues[addOn.ruleId].ruleItems.find(ri => ri.ruleItemInputs[0].columnName == p.columnName)
      //   }})
      // });

      // console.log("addOnPromptsValues", addOnPromptsValues);

      this.promptsValues[addOn.ruleId] = addOnPromptsValues[addOn.ruleId];

      // console.log("promptsValues", this.promptsValues);

      // this.promptsValues = {
      //   ...this.promptsValues,
      //   ...addOnPromptsValues
      // }
    });

    // console.log("promptsValues AFTER", this.promptsValues);

    this.allRules.forEach(rule => {
      if (this.prompts.filter(x => x.ruleId === rule.ruleId).length > 0) return;

      //console.log(this.promptsValues[rule.ruleId]);
      if (!this.promptsValues[rule.ruleId] || (!this.promptsValues[rule.ruleId].isInclusion && rule.isInclusion)) {
        this.promptsValues[rule.ruleId] = <IRulePrompt>{
          commissionRuleId: rule.ruleId,
          ruleTypeName: rule.ruleTypeName,
          isInclusion: rule.isInclusion ? rule.isInclusion : false,
          inclusionStartDate: rule.inclusionStartDate,
          inclusionEndDate: rule.inclusionEndDate,
          ruleItems: [
            <IRuleItem>{
              itemId: null,
              ruleItemInputs: []
            }
          ]
        }
      }
    })

    var rules = Object.values(this.promptsValues);

    if (this.addOns.filter(x => x.ruleTypeName == "Base Pay Structure").length > 1) {
      var structureRules = rules.filter(x => x.basePayCommissionRuleId != null).map(x => x.basePayCommissionRuleId);
      var bpRules = this.addOns.filter(x => x.basePayRuleId != null && x.basePayRuleId > 0).map(y => y.basePayRuleId);
      var missingStructures = bpRules.filter(x => !structureRules.includes(x));

      if (missingStructures && missingStructures.length > 0) {
        //console.log(missingStructures)
        missingStructures.forEach((x: any) => {
          var struc = this.addOns.filter(a => a.basePayRuleId == x)[0];
          rules.push(<IRulePrompt>{
            commissionRuleId: struc.ruleId,
            ruleTypeName: struc.ruleTypeName,
            basePayCommissionRuleId: struc.basePayRuleId,
            isInclusion: struc.isInclusion ? struc.isInclusion : false,
            inclusionStartDate: undefined,
            inclusionEndDate: undefined,
            ruleItems: struc.promptValues[struc.ruleId].ruleItems
          })
        })
      }
    }

    let formData = {
      ContactId: this.contactId,
      PlanId: this.planId,
      StartDate: this.planstartday,//new Date(),//this.apiService.dateFormat(new Date()),
      EndDate: "",//this.apiService.dateFormat(new Date()),
      Comments: "",
      PlanRules: rules,
      ExcludedRules: this.excludedRules,
      ExcludedRuleTypes: this.excludedRuleTypes,
      ContinuePlanInd: this.continueLastPlan
    }

    var datesValid = true;
    var eiInputsValid = true;

    rules.forEach((x: IRulePrompt) => {
      x.ruleItems.forEach((y: IRuleItem) => {

        // Validate Dates

        let startDateInputs = y.ruleItemInputs.filter(input => input.columnName == 'Start_Date');
        let endDateInputs = y.ruleItemInputs.filter(input => input.columnName == 'End_Date');

        if (startDateInputs.length > 0 && endDateInputs.length > 0) {
          let startDate = new Date(startDateInputs[0].columnValue);
          let endDate = new Date(endDateInputs[0].columnValue);

          if (startDate > endDate) datesValid = false;
        }

        // Validate Employee Incentive Inputs

        // let numberOfBonuses = y.ruleItemInputs.filter(input => input.columnName == 'Number_Of_Bonuses');
        // let offerAmount = y.ruleItemInputs.filter(input => input.columnName == 'Offer_Amount');

        if (y.ruleItemInputs.find(x => x.columnName != "Start_Date" && x.columnName != "End_Date" && ((x.columnValue == null || x.columnValue == "") && x.columnValue !== 0)) != null) {
          eiInputsValid = false;
        }

        // if (numberOfBonuses.length > 0 && offerAmount.length > 0) {
        //   if (numberOfBonuses.filter(input => input.columnValue == null).length > 0) {
        //     eiInputsValid = false;
        //   }

        //   if (offerAmount.filter(input => input.columnValue == null).length > 0) {
        //     eiInputsValid = false;
        //   }
        // }
      });
    });

    if (!formData.ContactId || !formData.PlanId || !eiInputsValid) {
      this.toastMsg.warning("Please fill the all input fields.", 'Server Error!')
      return false;
    } else if (!this.planstartday) {
      this.toastMsg.warning("Please enter a start date for the new plan.")
    } else if (!datesValid) {
      this.toastMsg.warning("Start Dates cannot be greater than End Dates");
      return false;
    } else {
      //console.log(formData)
      this.apiService.post('planassign', formData)
        .subscribe(data => {
          if (data["statusCode"] === "201" && data.result) {
            this.toastMsg.success("Plan assigned successfully.", 'Success!')
            this.router.navigate(['/ui/commissions/salesrepconfiguration', this.contactId]);
          } else {
            this.toastMsg.error("No plans found.", 'Server Error!')
          }
        }, (err: any) => {
          // console.log(err)
          this.toastMsg.error(err.message, 'Server Error!')
        });
    }
  }

  onRulePromptChange(prompt: IRulePrompt) {
    this.promptsValues[prompt.commissionRuleId] = prompt;
    // console.log("Prompts:", this.promptsValues);
  }

  addEmployeeIncentive() {
    var employeeIncentiveRules: IPlanDetail[];
    this.apiService.get('GetData/EmployeeIncentiveRules')
      .subscribe(data => {
        if (data && data.result) {
          // console.log(data);
          employeeIncentiveRules = data.result.map(x => { return <IPlanDetail>x });
          var existingRules = this.addOns.map(x => { return x.ruleId });
          employeeIncentiveRules[0].rules = employeeIncentiveRules[0].rules.filter(x => !existingRules.includes(x.ruleId));

          const dialogRef = this.dialog.open(EmployeeIncentiveDialogComponent, {
            width: '80%',

            data: {
              employeeIncentiveRules: employeeIncentiveRules,
              prompts: this.metadataPrompts
            }
          });

          dialogRef.afterClosed().subscribe(result => {
            // console.log(result);
            if (result) {
              this.addOns.push(result);
              this.addOns = this.addOns.slice();
            }
          });
        }
      }, err => {
        this.toastMsg.error(err.message, "Error");
      });
  }

  addPlanInclusion(){
    this.apiService.get(`ContactPlanInclusion/${this.planId}`)
    .subscribe(data => {
      if (data && data.result) {
        //console.log(data)
        this.planInclusionRules = data.result;

        var bpRules = this.allRules.filter(x => x.ruleTypeName == "Base Pay" && !this.excludedRules.includes(x.ruleId)).map(x => x.ruleId);
        this.planInclusionRules.basePay.splice(this.planInclusionRules.basePay.findIndex(i => i.commissionRuleId == bpRules.find( x => x == i.commissionRuleId)), 1)

        var bonusRules = this.allRules.filter(x => x.ruleTypeName == "Bonus" && !this.excludedRules.includes(x.ruleId)).map(x => x.ruleId);
        if (bonusRules && bonusRules.length > 0)
          this.planInclusionRules.bonuses.splice(this.planInclusionRules.bonuses.findIndex(i => i.commissionRuleId == bonusRules.find( x => x == i.commissionRuleId)), 1)

        var bigRules = this.allRules.filter(x => x.ruleTypeName == "Bonus Incentive Goal" && !this.excludedRuleTypes.includes(x.ruleId)).map(x => x.ruleId);
        this.planInclusionRules.bonusIncentiveGoals.splice(this.planInclusionRules.bonusIncentiveGoals.findIndex(i => i.commissionRuleTypeId == bigRules.find(x => x == i.commissionRuleTypeId)), 1)

        var rigRules = this.allRules.filter(x => x.ruleTypeName == "Rate Incentive Goal" && !this.excludedRuleTypes.includes(x.ruleId)).map(x => x.ruleId);
        this.planInclusionRules.rateIncentiveGoals.splice(this.planInclusionRules.rateIncentiveGoals.findIndex(i => i.commissionRuleTypeId == rigRules.find(x => x == i.commissionRuleTypeId)), 1)


        const inclusionRef = this.dialog.open(AddPlanInclusionDialogComponent, {
          width: '80%',
          data:{
            planInclusionRules: this.planInclusionRules,
            component: "changePlan",
            planId: this.planId
          }
        })

        inclusionRef.afterClosed().subscribe(result => { 
          if(result) {
            //console.log(result);
            var ruleName = "";
            if(result.commissionRuleTypeName == "Base Pay")
               ruleName = this.planInclusionRules.basePay.find(x => x.commissionRuleId == result.commissionRuleId).commissionRuleName
            else if(result.commissionRuleTypeName == "Bonus")
              ruleName = this.planInclusionRules.bonuses.find(x => x.commissionRuleId == result.commissionRuleId).commissionRuleName
            else if(result.commissionRuleTypeName == "Bonus Incentive Goal")
              ruleName = this.planInclusionRules.bonusIncentiveGoals.find(x => x.commissionRuleTypeId == result.commissionRuleTypeId).commissionRuleTypeName
            else if (result.commissionRuleTypeName == "Rate Incentive Goal")
              ruleName = this.planInclusionRules.rateIncentiveGoals.find(x => x.commissionRuleTypeId == result.commissionRuleTypeId).commissionRuleTypeName

            var inclusion: any = {
              isInclusion: true,
              promptAssignPlan: null,
              prompts: [],
              ruleId: result.commissionRuleTypeName == "Bonus Incentive Goal" || result.commissionRuleTypeName == "Rate Incentive Goal" ? result.commissionRuleTypeId : result.commissionRuleId,
              ruleName: ruleName,
              ruleTypeName: result.commissionRuleTypeName,
              inclusionStartDate: result.effectiveStartDate,
              inclusionEndDate: result.effectiveEndDate
            }

            let ruleArray = this.basicPlan.filter(item => item.parent == "Contact Plan Inclusions");
            //console.log(inclusion)
            if (ruleArray && ruleArray.length > 0) {
              //ruleArray[0].child = [];
              ruleArray[0].child.push(inclusion);
              this.allRules.push(inclusion);
              // if (row.promptAssignPlan || (row.prompts && row.prompts.length > 0)) {
              //   this.prompts.push(row)
              // }
            }
            //console.log(this.basicPlan)
            if(result.commissionRuleTypeName == "Base Pay")
              this.checkBasePay();
          }
        })

      }
    } , (err: any) => {
      console.log(err)
    });
  }

  addPaymentBookSchedule() {
    var paymentBookSchedulesRules: IPlanDetail[];
    this.apiService.get('GetData/paymentBookRules') // to be added in backend
      .subscribe(data => {
        if (data && data.result) {
          // console.log(data);
          var paymentBookRules: IPlanDetail[];
          paymentBookRules = data.result.map(x => { return <IPlanDetail>x });

          const dialogRef = this.dialog.open(PaymentBookScheduleDialogComponent, {
            width: '80%',
            data: {
              paymentBookSchedulesRules: paymentBookSchedulesRules,
              paymentBookRules: paymentBookRules,
              prompts: this.metadataPrompts
            }
          });

          dialogRef.afterClosed().subscribe(result => {
            // console.log(result);
            if (result) {
              this.addOns.push(result);
              this.addOns = this.addOns.slice();
            }
          });
        }
      }, err => {
        this.toastMsg.error(err.message, "Error");
      });
  }

  addBasePayStructure() {
    var basePayStructureRules: IPlanDetail[];
    this.apiService.get('GetData/basePayStructureRules')
      .subscribe(data => {
        if (data && data.result) {
          // console.log(data);
          basePayStructureRules = data.result.map(x => { return <IPlanDetail>x });
          //var existingRules = this.addOns.map(x => { return x.ruleId });
          //basePayStructureRules[0].rules = basePayStructureRules[0].rules.filter(x => !existingRules.includes(x.ruleId));

          const dialogRef = this.dialog.open(BasePayStructureDialogComponent, {
            width: '80%',

            data: {
              basePayStructureRules: basePayStructureRules,
              basePayRules: this.allRules.filter(x => x.ruleTypeName == "Base Pay"),
              prompts: this.metadataPrompts
            }
          });

          dialogRef.afterClosed().subscribe(result => {
            // console.log(result);
            if (result) {
              this.addOns.push(result);
              this.addOns = this.addOns.slice();
            }
          });
        }
      }, err => {
        this.toastMsg.error(err.message, "Error");
      });
  }

  addPaymentBook() {
    var paymentBookRules: IPlanDetail[];
    this.apiService.get('GetData/paymentBookRules')
      .subscribe(data => {
        if (data && data.result) {
          // console.log(data);
          paymentBookRules = data.result.map(x => { return <IPlanDetail>x });
          var existingRules = this.addOns.map(x => { return x.ruleId });
          paymentBookRules[0].rules = paymentBookRules[0].rules.filter(x => !existingRules.includes(x.ruleId));

          const dialogRef = this.dialog.open(PaymentBookDialogComponent, {
            width: '50%',

            data: {
              paymentBookRules: paymentBookRules,
              prompts: this.metadataPrompts
            }
          });

          dialogRef.afterClosed().subscribe(result => {
            // console.log(result);
            if (result) {
              this.addOns.push(result);
              this.addOns = this.addOns.slice();
            }
          });
        }
      }, err => {
        this.toastMsg.error(err.message, "Error");
      });
  }

  onAddOnsChange(addons: AddOnRule[]) {
    this.addOns = addons;
  }

  paymentBookAssigned() {
    return this.addOns.find(x => x.ruleTypeName == "Payment Book") != null;
  }
  checkBasePay() {
    if (!this.allRules)
      return false;

    var r = this.allRules.filter(x => x.ruleTypeName == "Base Pay");
    if (r == undefined || r.length == 0) {
      return false;
    }

    var s = this.addOns.filter(x => x.ruleTypeName == "Base Pay Structure");
    var planBpRule = r.find(x => !x.isInclusion);
    var bpRules = s.map(x => x.basePayRuleId.toString());
    var duplicates = bpRules.filter((item, index) => bpRules.indexOf(item) !== index);
    if (r.length == s.length || r.length < s.length)
      return false;
    else if (this.excludedRules.includes(parseInt(planBpRule.ruleId)) && this.allRules.find(a => a.ruleId.toString() == planBpRule.ruleId.toString() && a.isInclusion) !== undefined
             && s.filter(x => x.basePayRuleId.toString() == planBpRule.ruleId).length == 1 && (r.length-1 == s.length || r.length-1 < s.length)){
      return false;
    }
    else if (duplicates && duplicates.length > 0) {
      return false;
    }
    else
      return true;
  }

  checkNewPlanStartDate() {
    return this.planstartday != null
  }

  checkCanViewContactDetails() {
    return this.apiService.checkPermission('ViewSalesRepDetail');
  }

  getCurrentBasePayRules() {
    if (this.allRules && this.allRules.length > 0) {
      var bpRules = this.allRules.filter(x => x.ruleTypeName == 'Base Pay');
      var filtered = bpRules.map(x => x.ruleId.toString());
      var duplicates = filtered.filter((item, index) => filtered.indexOf(item) !== index);
      if (duplicates && duplicates.length > 0) {
        return bpRules.filter(x => x.isInclusion);
      }
      else {
        return bpRules;
      }
    }
    else {
      return undefined;
    }
  }

  checkUniqueBasePayMappings(){
    var notUnique = false;
    if (this.addOns && this.addOns.length > 0) {
      var exists = this.addOns.filter(x => x.ruleTypeName == "Base Pay Structure").map(x => x.basePayRuleId.toString());
      var duplicates = exists.filter((item, index) => exists.indexOf(item) !== index);
      if (duplicates && duplicates.length > 0){ 
        notUnique = true;
      }
    }
    return notUnique;

  }

  checkExcludedRuleInclusion(childItem) {
    if (childItem) {
      var exclusion = this.excludedRules.find(x => x == childItem.ruleId);
      var inclusion = this.allRules.find(x => x.ruleId == childItem.ruleId && x.isInclusion);
      //console.log(inclusion)
      if (exclusion && !childItem.isInclusion && inclusion)
        return true;
      var rtExclusion = this.excludedRuleTypes.find(x => x == childItem.ruleId)
      if (rtExclusion && !childItem.isInclusion && inclusion)
        return true;
    }
    return false;
  }
}
