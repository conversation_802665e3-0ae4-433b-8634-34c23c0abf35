import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { RateTablesRoutingModule } from './rate-tables-routing.module';
import { RateTablesComponent } from './rate-tables.component';
import { TerritoryRateMaintenanceComponent } from './territory-rate-maintenance/territory-rate-maintenance.component';
import { OutReachConfigurationComponent } from './out-reach-configuration-maintenance/out-reach-configuration-maintenance.component';
import { NgxCurrencyModule } from 'ngx-currency';
import { NgxPaginationModule } from 'ngx-pagination';
import { FinancePartnerDeductionMaintenanceComponent } from './finance-partner-deduction-maintenance/finance-partner-deduction-maintenance.component';
import { ModuleDeductionMaintenanceComponent } from './module-deduction-maintenance/module-deduction-maintenance.component';
import { InverterDeductionMaintenanceComponent } from './inverter-deduction-maintenance/inverter-deduction-maintenance.component';
import { InstallationTypeDeductionMaintenanceComponent } from './installation-type-deduction-maintenance/installation-type-deduction-maintenance.component';
import { PurchaseMethodDeductionMaintenanceComponent } from './purchase-method-deduction-maintenance/purchase-method-deduction-maintenance.component';
import { ProductPurchaseMethodDeductionMaintenanceComponent } from './product-purchase-method-deduction-maintenance/product-purchase-method-deduction-maintenance.component';
import { PermitDeductionMaintenanceComponent } from './permit-deduction-maintenance/permit-deduction-maintenance.component';
import { PpaBonusRateMaintenanceComponent } from './ppa-bonus-rate-maintenance/ppa-bonus-rate-maintenance.component';
import { PPABonusFlatRateMaintenanceComponent } from './ppa-bonus-flat-rate-maintenance/ppa-bonus-flat-rate-maintenance.component';
import { PpwBonusRateMaintenanceComponent } from './ppw-bonus-rate-maintenance/ppw-bonus-rate-maintenance.component';
import { BatteryCommissionRateMaintenanceComponent } from './Battery_Commission_Rate-maintenance/Battery_Commission_Rate-maintenance.component';
import { BatteryRateMaintenanceDialogComponent } from './Battery_Commission_Rate-maintenance-dialog/Battery_Commission_Rate-maintenance-dialog.component';
import { MainPipeModule } from 'src/app/pipe/main-pipe.module';
import { MatSortModule } from '@angular/material/sort';
import { MatTableModule } from '@angular/material/table';
import { CdkTableModule } from '@angular/cdk/table';
import { MatPaginatorModule } from '@angular/material/paginator';
import { TableFilterPipe } from '../../pipe/table-filter.pipe';
import { DatePipe, PercentPipe, CurrencyPipe } from '@angular/common';
import { SharedModule } from 'src/app/shared-module/shared.module';
import { FinancePartnerDeductionDialogComponent } from './finance-partner-deduction-dialog/finance-partner-deduction-dialog.component';
import { TerritoryRateMaintenanceDialogComponent } from './territory-rate-maintenance-dialog/territory-rate-maintenance-dialog.component';
import { ModuleDeductionMaintenanceDialogComponent } from './module-deduction-maintenance-dialog/module-deduction-maintenance-dialog.component';
import { InstallationTypeDeductionMaintenanceDialogComponent } from './installation-type-deduction-maintenance-dialog/installation-type-deduction-maintenance-dialog.component';
import { PurchaseMethodDeductionMaintenanceDialogComponent } from './purchase-method-deduction-maintenance-dialog/purchase-method-deduction-maintenance-dialog.component';
import { ProductPurchaseMethodDeductionMaintenanceDialogComponent } from './product-purchase-method-deduction-maintenance-dialog/product-purchase-method-deduction-maintenance-dialog.component';
import { PpaBonusRateMaintenanceDialogComponent } from './ppa-bonus-rate-maintenance-dialog/ppa-bonus-rate-maintenance-dialog.component';
import { OutReachConfigurationMaintenanceDialogComponent } from './out-reach-configuration-maintenance-dialog/out-reach-configuration-maintenance-dialog.component';
import { PermitDeductionMaintenanceDialogComponent } from './permit-deduction-maintenance-dialog/permit-deduction-maintenance-dialog.component';
import { TeiredOverageComponent } from './teired-overage/teired-overage-maintenance.component';
import { ProductRateMaintenanceComponent } from './product-rate-maintenance/product-rate-maintenance.component';
import { EmployeeOverrideRateComponent } from './employee-override-rate/employee-override-rate.component';
import { EmployeeOverrideRoofRateComponent } from './employee-override-roof-rate/employee-override-roof-rate.component';
import { NgbTypeaheadModule } from '@ng-bootstrap/ng-bootstrap';
import { EmployeeOverrideRateTierComponent } from './employee-override-rate-tier/employee-override-rate-tier.component';
import { EmployeeRoleComponent } from './employee-role/employee-role.component';
import { EmployeeOverrideRateDialogComponent } from './employee-override-rate/employee-override-rate-dialog/employee-override-rate-dialog.component';
import { EmployeeOverrideRoofRateDialogComponent } from './employee-override-roof-rate/employee-override-roof-rate-dialog/employee-override-roof-rate-dialog.component';
import { BatteryRetrofitCommissionRateMaintenanceComponent } from './battery-retrofit-commission-rate-maintenance/battery-retrofit-commission-rate-maintenance.component';
import { BatteryRetrofitCommissionRateMaintenanceDialogComponent } from './battery-retrofit-commission-rate-maintenance-dialog/battery-retrofit-commission-rate-maintenance-dialog.component';
import { BatteryPurchaseMethodDeductionMaintetanceComponent } from './battery-purchase-method-deduction-maintetance/battery-purchase-method-deduction-maintetance.component';
import { RoofCommissionRatesComponent } from './roof-commission-rates/roof-commission-rates.component';
import { RoofCommissionRateDialogComponent } from './roof-commission-rate-dialog/roof-commission-rate-dialog.component';
import { MatDialogModule } from '@angular/material/dialog';
import { GridMatTableComponent } from 'src/app/business-dashboard/component/grid-mat-table/grid-mat-table.component';
import { GridFilterComponent } from 'src/app/business-dashboard/component/grid-filter/grid-filter.component';
import { RoleRateComponent } from './role-rate/role-rate.component';
import { RoleRateDialogComponent } from './role-rate-dialog/role-rate-dialog.component';
import { TieredAmountComponent } from './tiered-amount/tiered-amount.component';
import { TieredAmountDialogComponent } from './tiered-amount-dialog/tiered-amount-dialog.component';
import { BatteryEmployeeRoleComponent } from './battery-employee-role/battery-employee-role.component';
import { RoofingMonthlyOverrideComponent } from './roofing-monthly-override/roofing-monthly-override.component';
import { RoofingMonthlyOverrideDialogComponent } from './roofing-monthly-override-dialog/roofing-monthly-override-dialog.component';
import { NgMultiSelectDropDownModule } from 'ng-multiselect-dropdown';
import { MatRadioModule } from '@angular/material/radio';
import { SplitCommissionRatesComponent } from './split-commission-rates/split-commission-rates.component';
import { SplitCommissionRatesDialogComponent } from './split-commission-rates-dialog/split-commission-rates-dialog.component';

@NgModule({
  declarations: [
    RateTablesComponent,
    TerritoryRateMaintenanceComponent,
    OutReachConfigurationComponent,
    FinancePartnerDeductionMaintenanceComponent,
    ModuleDeductionMaintenanceComponent,
    InverterDeductionMaintenanceComponent,
    InstallationTypeDeductionMaintenanceComponent,
    PurchaseMethodDeductionMaintenanceComponent,
    ProductPurchaseMethodDeductionMaintenanceComponent,
    PermitDeductionMaintenanceComponent,
    PpaBonusRateMaintenanceComponent,
    PPABonusFlatRateMaintenanceComponent,
    PpwBonusRateMaintenanceComponent,
    BatteryCommissionRateMaintenanceComponent,
    TeiredOverageComponent,
    ProductRateMaintenanceComponent,
    EmployeeOverrideRateComponent,
    EmployeeOverrideRoofRateComponent,
    EmployeeOverrideRateTierComponent,
    EmployeeRoleComponent,
    EmployeeOverrideRateDialogComponent,
    EmployeeOverrideRoofRateDialogComponent,
    BatteryRetrofitCommissionRateMaintenanceComponent,
    BatteryPurchaseMethodDeductionMaintetanceComponent,
    RoofCommissionRatesComponent,
    RoofCommissionRateDialogComponent,
    RoleRateComponent,
    RoleRateDialogComponent,
    TieredAmountComponent,
    TieredAmountDialogComponent,
    BatteryEmployeeRoleComponent,
    RoofingMonthlyOverrideComponent,
    RoofingMonthlyOverrideDialogComponent,
    SplitCommissionRatesComponent,
    SplitCommissionRatesDialogComponent
  ],
  imports: [
    CommonModule,
    RateTablesRoutingModule,
    NgxCurrencyModule,
    NgxPaginationModule,
    MatDialogModule,
    FormsModule,
    ReactiveFormsModule,
    MainPipeModule,
    MatTableModule,
    MatSortModule,
    CdkTableModule,
    MatPaginatorModule,
    SharedModule,
    NgbTypeaheadModule,
    GridFilterComponent,
    GridMatTableComponent,
    NgMultiSelectDropDownModule.forRoot(),
    MatRadioModule,
  ],
  providers: [
    TableFilterPipe,
    DatePipe,
    PercentPipe,
    CurrencyPipe
  ]
})
export class RateTablesModule { }