import { Component, OnInit, Input, SimpleChanges, ViewChild } from '@angular/core';
import { ApiService } from 'src/app/services/api.service';
import { ToastrService } from 'ngx-toastr';
import { ICommission } from 'src/app/model/commission.model';
import {MatSort} from '@angular/material/sort';
import {MatTableDataSource} from '@angular/material/table';
import { DataSource } from '@angular/cdk/table';
import {MatPaginator} from '@angular/material/paginator';
import { CurrencyPipe } from '@angular/common';

@Component({
  selector: 'app-evaluate-commission',
  templateUrl: './evaluate-commission.component.html',
  styleUrls: ['./evaluate-commission.component.css']
})
export class EvaluateCommissionComponent implements OnInit {
  @Input() opportunityId: number;
  commissions: ICommission[] = [];
  p: number = 1;

  originalDataSource;
  dataSource;
  displayedColumns = [];
  @ViewChild(MatSort, {static: true}) sort: MatSort;
  @ViewChild(MatPaginator, {static: true}) paginator: MatPaginator;

  columnNames = [{
    id: "details",
    value: "Details"

  }, {
    id: "paidTo",
    value: "Paid To"
  },
  {
    id: "calculatedVia",
    value: "Calculated Via (Plan/Rule)"
  },
  {
    id: "commissionAmount",
    value: "Commission Amount"
  }];

  constructor(private apiService: ApiService, private toastMsg: ToastrService, private currencyPipe: CurrencyPipe) { }

  ngOnInit(): void {
  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes.opportunityId) {
      this.getCommissions(changes.opportunityId.currentValue);
    }
  }

  getCommissions(opportunityId: number) {
    this.apiService.get(`Commissions/ByOpportunity/${opportunityId}/${true}`)
      .subscribe(data => {
        if (data && data.result) {
          this.commissions = data.result.map(comm => {return <ICommission>comm});
          this.displayedColumns = this.columnNames.map(x => x.id);
        this.createTable();
        }
      }, err => {
        this.toastMsg.error(err.message, "Error!");
      })
  }

  createTable() {
    let tableArr: Element[] = [];
    for(let i:number = 0; i <= this.commissions.length - 1; i++) {
      let currentRow = this.commissions[i];
      tableArr.push({commissionAmount: this.currencyPipe.transform(currentRow.commissionAmount), commissionId: currentRow.commissionId, contactId: currentRow.contactId,
      opportunityId: currentRow.opportunityId, opportunityName: currentRow.opportunityName, paidTo: currentRow.paidTo,
    calculatedVia: `${currentRow.planName}/${currentRow.ruleName}`});
    }
    this.dataSource = new MatTableDataSource(tableArr);
    this.originalDataSource = tableArr;
    this.dataSource.sort = this.sort;
    this.dataSource.paginator = this.paginator;
  }

}
export interface Element {
  commissionAmount: string,
  commissionId: number,
  contactId: number,
  opportunityId: number,
  opportunityName: string,
  paidTo: string,
  calculatedVia: string,

}

