import { Component, OnInit, ViewEncapsulation, Input, AfterViewInit,SimpleChanges, ViewChildren, QueryList } from "@angular/core";
import { ApiService } from "../../../services/api.service";
import { ToastrService } from "ngx-toastr";
import { IBasePayStructure } from "../../../model/base-pay-structure.model";
import { IPayStreamItem } from 'src/app/model/base-pay-structure.model';
import { IPaymentType } from 'src/app/model/payment-type.model';
import { StageComponent } from './stage/stage.component';
import { Router, ActivatedRoute } from "@angular/router";
import { ChangeDetectorRef } from '@angular/core';
declare var $: any;

@Component({
  selector: ".app-basepay-structure",
  templateUrl: "./basepay-structure.component.html",
  styleUrls: ["./basepay-structure.component.css"],
  encapsulation: ViewEncapsulation.None
})
export class BasepayStructureComponent implements OnInit, AfterViewInit {
  @ViewChildren(StageComponent) stageComponents !: QueryList<StageComponent>;
  @Input() showBasePayStructure: boolean;
  @Input() cloneRule: any = {};
  basePayStructure: IBasePayStructure = <IBasePayStructure>{
    numberOfPayments: 1,
    promptAssignPlan: true,
    startDate: null
  };
  paymentTypes: IPaymentType[];
  numberOfStages: number = 1;
  basePayStructures: IBasePayStructure[];
  basePayStructureClones: IBasePayStructure[] = [];
  errors: boolean = false;
  disabled: boolean = false;

  constructor(private apiService: ApiService, private toastMsg: ToastrService, private router: Router,
    private cdref: ChangeDetectorRef) {
    this.apiService.hideLoader = true;
  }

  ngOnInit() { }

  ngOnChanges(changes: SimpleChanges) {
    if (changes.cloneRule&&Object.keys(changes.cloneRule).length > 0) {
      this.prePopulateRuleData();
    }
  }

  ngAfterViewInit() {

    if (this.stageComponents && this.stageComponents.length > 0 && (<any>this.stageComponents)._results && (<any>this.stageComponents)._results.length > 0) {
      this.disabled = (<any>this.stageComponents)._results[0].disabled;
    }
    (<any>this.stageComponents).changes.subscribe(change => {
      this.getBasePayStructures();
    })

    this.getBasePayStructures();
  }

  getBasePayStructures() {
    setTimeout(() => {
      this.basePayStructures = this.stageComponents.map(p => p.basePayStructure);
    }, 1000);
  }

  submitBasePayStructure() {
    this.getBasePayStructures();

    if ($("input[name='rule_name']").val().trim() == "") {
      this.toastMsg.error("Please enter a rule name", "Error");
      return;
    }
    if ($("input[name='Description']").val().trim() == "") {
      this.toastMsg.error("Please enter a rule description", "Error");
      return;
    }
    if ($("select[name='rule_type_id']").val().trim() == "") {
      this.toastMsg.error("Please select a rule type", "Error");
      return;
    }

    let formBasePayStructures = [];

    this.errors = false;



    if (this.basePayStructures && this.basePayStructures.length > 0) {
      this.stageComponents.forEach((stage, i) => {
        if (stage.basePayStructure.promptAssignPlan) {
          stage.basePayStructure.startDate = null;
        } else if (!stage.basePayStructure.promptAssignPlan && (!stage.basePayStructure.startDate)) {
          this.toastMsg.error("Please select dates", "Error");
          this.errors = true;
          return;
        }

        if (this.basePayStructures[i + 1]  && !stage.basePayStructure.promptAssignPlan && new Date(stage.basePayStructure.startDate) >= new Date(this.basePayStructures[i + 1].startDate)) {
          this.toastMsg.error("Start Dates are not in a proper order", "Error");
          this.errors = true;
          return;
        }

        let payStream = [];
        if (!stage.basePayStructure.numberOfPayments) {
          this.toastMsg.error("All Number of Payments fields must be filled", "Error");
          this.errors = true;
          return;
        } else {
          Object.values(stage.payStreamItems).forEach((x: IPayStreamItem) => {

            // console.log("Payment Stream Item", x);

            if (!x.percentage || x.percentage == null) {
              this.toastMsg.error("All Percentage fields in Pay Streams must be filled", "Error");
              this.errors = true;
              return;
            } else if (!x.payBasedOn || x.payBasedOn == null) {
              this.toastMsg.error("All Pay Based On fields in Pay Streams must be filled", "Error");
              this.errors = true;
              return;
            } else if (this.disabled == false && !x.daysInAdvance && x.daysInAdvance!=0) {
              this.toastMsg.error("All Days In Advance fields in Pay Streams must be filled", "Error");
              this.errors = true;
              return;
            } else if (!x.paymentTypeId || x.paymentTypeId == null) {
              this.toastMsg.error("All Payment Type fields in Pay Streams must be filled", "Error");
              this.errors = true;
              return;
            }

            let stream = {
              percentage: x.percentage,
              stage: x.stage,
              paymentDueDateMappingId: x.paymentDueDateMappingId,
              payBasedOn: x.payBasedOn,
              daysInAdvance: x.daysInAdvance,
              paymentTypeId: x.paymentTypeId.toString()
            };

            payStream.push(stream);
          });
        }

        if (payStream.length > 0) {
          formBasePayStructures.push({
            basePayStructureName: stage.basePayStructure.basePayStructureName,
            numberOfPayments: stage.basePayStructure.numberOfPayments,
            promptAssignPlan: stage.basePayStructure.promptAssignPlan.toString(),
            startDate: stage.basePayStructure.startDate,
            payStream: payStream
          });
        }
      })
    } else {
      this.toastMsg.error("Error", "Error");
      this.errors = true;
    }

    if (this.errors) return;

    let formData = {
      ruleTypeId: $("select[name='rule_type_id']").val(),
      ruleName: $("input[name='rule_name']").val(),
      description: $("input[name='Description']").val(),
      numberOfStages: this.numberOfStages,
      promptAssignPlan: this.basePayStructure.promptAssignPlan,
      basePayStructures: formBasePayStructures
    };
    this.cdref.detectChanges();


    this.apiService.post("BasePayStructures", formData).subscribe(
      data => {
        if (data["statusCode"] === "200" || data["statusCode"] === "201") {
          this.toastMsg.success(
            "Base pay structure has been created successfully",
            "Success!"
          );
          setTimeout(() => {
            location.reload()
          }, 1000);
          this.router.navigate(['/ui/commissions/rule/create']);
         
        } else {
          this.toastMsg.error("Server", "Error!");
        }
      },
      (err: any) => {
        this.toastMsg.error(err.message, "Error!");
      }
    );
  }

  forLoop(n: number) {
    let arr = [];

    for (let i = 0; i < n; i++) {
      arr.push(i + 1);
    }

    return arr;
  }

  /**
   * Pre-populate clone data
   */
  prePopulateRuleData() {
    if (Object.keys(this.cloneRule).length > 0) {
      this.numberOfStages = this.cloneRule.numberOfStages;
      this.basePayStructure.promptAssignPlan = this.cloneRule.promptAssignPlan;
      this.basePayStructureClones = this.cloneRule.basePayStructures;
      this.cdref.detectChanges();
    }
  }


  onGoBack() {
    if (confirm("Your unsaved progress will be deleted, do you wish to continue?")) {
      this.router.navigate(['/ui/commissions'])
    } else
      return false

  }

  resetForm() {
    this.basePayStructure.numberOfPayments = 1
    this.basePayStructure.promptAssignPlan = true
    this.basePayStructure.startDate = null
    $("input[name='rule_name']").val(""),
      $("input[name='Description']").val("")
    this.numberOfStages = 1

  }

}