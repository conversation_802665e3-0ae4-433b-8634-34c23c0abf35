import { Component, OnInit, ViewChild } from '@angular/core';
import { UntypedForm<PERSON>uilder, UntypedFormGroup, Validators, UntypedFormControl, AbstractControl } from "@angular/forms";
import { ApiService } from "../../../services/api.service";
import { ToastrService } from 'ngx-toastr';
import { maxPermitDeductionDate, rateNotExisting } from '../../../shared/validators';
import { groupBy } from '../../../shared/group-by';
import { getControlName } from '../../../shared/get-control-name';
import { MatSort } from '@angular/material/sort';
import { MatTableDataSource } from '@angular/material/table';
import { DataSource } from '@angular/cdk/table';
import { MatPaginator } from '@angular/material/paginator';
import { TableFilterPipe } from '../../../pipe/table-filter.pipe';
import { DatePipe, PercentPipe, CurrencyPipe } from '@angular/common';
// import { StringMap } from '@angular/compiler/src/compiler_facade_interface';
import { MatDialog } from '@angular/material/dialog';
import { PpaBonusRateMaintenanceDialogComponent } from '../ppa-bonus-rate-maintenance-dialog/ppa-bonus-rate-maintenance-dialog.component';

@Component({
  selector: 'app-ppa-bonus-rate-maintenance',
  templateUrl: './ppa-bonus-rate-maintenance.component.html',
  styleUrls: ['./ppa-bonus-rate-maintenance.component.css']
})
export class PpaBonusRateMaintenanceComponent implements OnInit {
  allPpaBonusRates: any;
  activePpaBonusRates: any;
  ppaBonusRateGroup: any;
  ppaBonusRateSelectedGroup: any;
  dropdowns: any;
  ppaBonusRateForm: UntypedFormGroup;
  ppaPpkwGroup: AbstractControl[][];
  addInd: boolean = false;
  isReloading :boolean = false ;
  stateCodeDefault: number = 1;
  financePartnerDefault: number = 1;
  purchaseMethodDefault: number = 1;
  utilityCompanyDefault: number = 1;
  p: number = 1;
  tableArr: Element[] = [];
  PPABonusRate; 
   isPPABonusRateRateSelected : boolean = false;
  searchText: string = "";
  public date: Date;
  originalDataSource;
  dataSource;
  displayedColumns = [];
  @ViewChild(MatSort, { static: true }) sort: MatSort;
  @ViewChild(MatPaginator, { static: true }) paginator: MatPaginator;

  columnNames = [{
    id: "stateCode",
    value: "State Code"

  }, {
    id: "utilityCompany",
    value: "Utility Company"
  },
  {
    id: "financePartner",
    value: "Finance Partner"
  },
  {
    id: "purchaseMethod",
    value: "Purchase Method"
  },
  {
    id: "effectiveStartDate",
    value: "Effective Start Date"
  },
  {
    id: "effectiveEndDate",
    value: "Effective End Date"
  }];

  constructor(public apiService: ApiService, private toastMsg: ToastrService, private formBuilder: UntypedFormBuilder, private pipe: TableFilterPipe,
    private datePipe: DatePipe, private percentPipe: PercentPipe, private currencyPipe: CurrencyPipe, private dialog: MatDialog) {

  }

  ngOnInit() {
    if (!this.apiService.checkPermission('ViewRateTables')) {
      // this.router.navigate(['/ui/dashboard'])
      this.apiService.goBack();
      this.toastMsg.error("Insufficient Permissions. Please make sure your account can access this information.", "Permissions");
    }
    this.ppaBonusRateForm = this.formBuilder.group({
      stateCode: [this.stateCodeDefault, [Validators.required]],
      financePartner: [this.financePartnerDefault, [Validators.required]],
      purchaseMethod: [this.purchaseMethodDefault, [Validators.required]],
      utilityCompany: [this.utilityCompanyDefault, [Validators.required]],
      effectiveStartDate: ['', [Validators.required]],
      ppaRate: [0, []],
      pricePerKw: [0, [Validators.required]],
    });

    this.getDropdowns();

    this.ppaPpkwGroup = [[this.ppaBonusRateForm.controls.ppaRate, this.ppaBonusRateForm.controls.pricePerKw]];

    this.onChanges();
  }

  onChanges() {
    this.ppaBonusRateForm.valueChanges.subscribe(val => {
      // console.log(this.ppaBonusRateForm.errors);
    });
  }

  clearDate(date: HTMLInputElement) {
    date.value = "";
    this.date = null;
    this.ppaBonusRateForm.controls.effectiveStartDate.setValue('');
    event.stopPropagation();
  }

  onSubmit() {
    if (!this.ppaBonusRateForm.invalid) {
      var groupArr = [];
      this.ppaPpkwGroup.forEach(x => {
        groupArr.push(
          {
            "PpaRate": x[0].value,
            "PricePerKw": x[1].value
          }
        );
      });

      // console.log(groupArr);

      var body = {
        stateCodeId: this.ppaBonusRateForm.controls.stateCode.value,
        financePartnerId: this.ppaBonusRateForm.controls.financePartner.value,
        purchaseMethodId: this.ppaBonusRateForm.controls.purchaseMethod.value,
        utilityCompanyId: this.ppaBonusRateForm.controls.utilityCompany.value,
        effectiveStartDate: this.ppaBonusRateForm.controls.effectiveStartDate.value,
        ppaPpwGroups: groupArr
      }
      // console.log("Body  136 => "+JSON.stringify(body));

      this.apiService.post('PpaBonusRateMaintenance', body)
        .subscribe(data => {
          this.toastMsg.success('Permit Deduction Successfully Added');
          this.isReloading = true ;
          this.getAllPpaBonusRates();
          this.getActivePpaBonusRates();
          this.addInd = !this.addInd;
        }, (err: any) => {
          if (err?.err?.message)
            this.toastMsg.error(err.message, "Server Error!");
          if (typeof err === "string") {
            let isJsonObject = false;            
            let parsedErr: any;
            try {
              parsedErr = JSON.parse(err);
              isJsonObject =parsedErr && typeof parsedErr === "object" &&!Array.isArray(parsedErr);
            } catch (e) {
              isJsonObject = false;
            }
            if (isJsonObject) {
              this.toastMsg.error(parsedErr.result, "Error!");
            } else this.toastMsg.error(err, "Error!");
          }
          if (err?.result) {
            this.toastMsg.error(err.result, "Error!");
          }
        });
    }
  }

  getAllPpaBonusRates() {
    this.apiService.get('PpaBonusRateMaintenance/retrieveall')
      .subscribe(data => {
        this.allPpaBonusRates = data;
        if(!this.isReloading)
        {
           if (this.ppaBonusRateForm.controls.ppaRate) this.ppaBonusRateForm.controls.ppaRate.setValidators([Validators.required, rateNotExisting(this.allPpaBonusRates)]);
        } else {
          this.ppaBonusRateForm.clearValidators();
        }
      
        if (this.ppaBonusRateGroup) {
          this.getPpaBonusRateGroup(this.ppaBonusRateGroup[0][0]);
          this.ppaBonusRateSelectedGroup = this.ppaBonusRateGroup;
        }
      }, (err: any) => {
        this.toastMsg.error(err.message, 'Server Error!');
      });
  }

  getActivePpaBonusRates() {
    this.apiService.get('PpaBonusRateMaintenance/retrieveactive')
      .subscribe(data => {
        // console.log('ppa', data);
        this.activePpaBonusRates = data;
        this.displayedColumns = this.columnNames.map(x => x.id);
        this.createTable();

      }, (err: any) => {
        this.toastMsg.error(err.message, 'Server Error!');
      });
  }

  getDropdowns() {
    this.apiService.get('PpaBonusRateMaintenance/dropdowns')
      .subscribe(data => {
        this.dropdowns = data;
        this.getAllPpaBonusRates();
        this.getActivePpaBonusRates();
      }, (err: any) => {
        this.toastMsg.error(err.message, 'Server Error!');
      });
  }

  getPpaBonusRateGroup(ppaBonusRate: any) {
    var ppaBonusRates = this.allPpaBonusRates.filter(x => x.financePartnerId === ppaBonusRate.financePartnerId && x.purchaseMethodId === ppaBonusRate.purchaseMethodId && x.stateCodeId === ppaBonusRate.stateCodeId && x.utilityCompanyId === ppaBonusRate.utilityCompanyId);
    ppaBonusRates = Object.values(groupBy(ppaBonusRates, 'effectiveStartDate'));
    // console.log(ppaBonusRates);
    this.ppaBonusRateGroup = ppaBonusRates;
    this.ppaBonusRateSelectedGroup = null;
  }

  get ppaRate() { return this.ppaBonusRateForm.get('ppaRate'); }

  get pricePerKw() { return this.ppaBonusRateForm.get('pricePerKw'); }

  rowClick(ppaBonusRate: any) {
    var ppaBonusRate = this.allPpaBonusRates.filter(x => x.financePartnerId === ppaBonusRate.financePartnerId && x.purchaseMethodId === ppaBonusRate.purchaseMethodId && x.stateCodeId === ppaBonusRate.stateCodeId && x.utilityCompanyId === ppaBonusRate.utilityCompanyId);
    this.PPABonusRate = ppaBonusRate;
    this.isPPABonusRateRateSelected = true;
    // console.log("PPABonusRate rowClick 209= >" + JSON.stringify(this.PPABonusRate));
    // console.log("this.PPABonusRate  = >" + JSON.stringify(this.PPABonusRate));
    ppaBonusRate = Object.values(groupBy(ppaBonusRate, 'effectiveStartDate'));
    const dialogRef = this.dialog.open(PpaBonusRateMaintenanceDialogComponent, {
      width: '80%', data: { ppaBonusRate }
    });
    this.ppaBonusRateForm.controls['stateCode'].setValue(this.PPABonusRate[0].stateCodeId);
    this.ppaBonusRateForm.controls['financePartner'].setValue(this.PPABonusRate[0].financePartnerId);
    this.ppaBonusRateForm.controls['purchaseMethod'].setValue(this.PPABonusRate[0].purchaseMethodId);
    this.ppaBonusRateForm.controls['utilityCompany'].setValue(this.PPABonusRate[0].utilityCompanyId);
   
    dialogRef.afterClosed().subscribe(result => {
      // console.log(result);
    });
  }

  
Add(){
  this.PPABonusRate = this.tableArr;
  this.addInd = !this.addInd;
  this.isReloading = true ;
  if(!this.isReloading)
  {
     if (this.ppaBonusRateForm.controls.ppaRate) this.ppaBonusRateForm.controls.ppaRate.setValidators([Validators.required, rateNotExisting(this.allPpaBonusRates)]);
  } else {
    this.ppaBonusRateForm.clearValidators();
  }
  // console.log("Add this.PPABonusRate => 225   " +JSON.stringify(this.PPABonusRate));
  this.ppaBonusRateForm.controls['financePartner'].setValue(this.PPABonusRate[0].stateCodeId);
  this.ppaBonusRateForm.controls['financePartner'].setValue(this.PPABonusRate[0].financePartnerId);
  this.ppaBonusRateForm.controls['purchaseMethod'].setValue(this.PPABonusRate[0].purchaseMethodId);
  this.ppaBonusRateForm.controls['utilityCompany'].setValue(this.PPABonusRate[0].utilityCompanyId);
}



  groupClick(group: any) {
    this.ppaBonusRateSelectedGroup = group;
  }

  addFormRow() {
    this.ppaBonusRateForm.addControl(`ppaRate${this.ppaPpkwGroup.length}`, new UntypedFormControl(0, []));
    this.ppaBonusRateForm.addControl(`pricePerKw${this.ppaPpkwGroup.length}`, new UntypedFormControl(0, []));
    var c1 = this.ppaBonusRateForm.get(`ppaRate${this.ppaPpkwGroup.length}`);
    var c2 = this.ppaBonusRateForm.get(`pricePerKw${this.ppaPpkwGroup.length}`);
    c1.setValidators([Validators.required, rateNotExisting(this.allPpaBonusRates)]);
    c2.setValidators([Validators.required]);
    this.ppaPpkwGroup.push([c1, c2]);
  }

  removeFormRow(index: number) {
    if (this.ppaPpkwGroup.length == 1) return;

    this.ppaPpkwGroup[index].slice(0).forEach(x => {
      this.ppaBonusRateForm.removeControl(getControlName(x));
    });

    this.ppaPpkwGroup.splice(index, 1);
  }

  getControlName(control: AbstractControl) {
    return getControlName(control);
  }

  createTable() {
    let tableArr: Element[] = [];
    for (let i: number = 0; i <= this.activePpaBonusRates.length - 1; i++) {
      let currentRow = this.activePpaBonusRates[i];
      if(i==0)
      {
        this.tableArr[0] =this.activePpaBonusRates[0];
        // console.log(" this.tableArr = 264 > "+ JSON.stringify(this.tableArr));
      }
      tableArr.push({
        effectiveEndDate: this.datePipe.transform(currentRow.effectiveEndDate), effectiveStartDate: this.datePipe.transform(currentRow.effectiveStartDate),
        financePartner: currentRow.financePartner, financePartnerId: currentRow.financePartnerId, ppaRate: this.currencyPipe.transform(currentRow.ppaRate),
        pricePerKw: this.currencyPipe.transform(currentRow.pricePerKw), purchaseMethod: currentRow.purchaseMethod, purchaseMethodId: currentRow.purchaseMethodId,
        purchaseMethodPpaBonusMappingId: currentRow.purchaseMethodPpaBonusMappingId, stateCode: currentRow.stateCode, stateCodeId: currentRow.stateCodeId,
        utilityCompany: currentRow.utilityCompany, utilityCompanyId: currentRow.utilityCompanyId, ppwBonusPricePerKw: currentRow.ppwBonusPricePerKw, ppwBonusMetric: currentRow.ppwBonusMetric
      });
    }
    this.dataSource = new MatTableDataSource(tableArr);
    this.originalDataSource = tableArr;
    this.dataSource.sort = this.sort;
    this.dataSource.paginator = this.paginator;
  }
  searchForItem(): void {
    let filteredResults: Element[] = [];
    if (this.searchText == '') {
      this.dataSource = new MatTableDataSource(this.originalDataSource);
      this.dataSource.sort = this.sort;
      this.dataSource.paginator = this.paginator;
    } else {
      // filteredResults = this.originalDataSource.filter(option => option.stateCode.toLowerCase().includes(this.searchText));
      filteredResults = this.pipe.transform(this.originalDataSource, this.searchText);
      this.dataSource = new MatTableDataSource(filteredResults);
      this.dataSource.sort = this.sort;
      this.dataSource.paginator = this.paginator;
    }
  }
}

export interface Element {
  effectiveEndDate: string,
  effectiveStartDate: string,
  financePartner: string,
  financePartnerId: number,
  ppaRate: string,
  pricePerKw: string,
  purchaseMethod: string,
  purchaseMethodId: number,
  purchaseMethodPpaBonusMappingId: number,
  stateCode: string,
  stateCodeId: number,
  utilityCompany: string,
  utilityCompanyId: number,
  ppwBonusPricePerKw: number,
  ppwBonusMetric: number
}
