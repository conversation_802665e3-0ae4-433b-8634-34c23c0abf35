import { Component, Inject, OnInit } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';

@Component({
  selector: 'app-roofing-monthly-override-dialog',
  templateUrl: './roofing-monthly-override-dialog.component.html',
  styleUrls: ['./roofing-monthly-override-dialog.component.css']
})
export class RoofingMonthlyOverrideDialogComponent implements OnInit {
  monthlyOverrideGroup: Element[] = [];
  monthlyOverrideSelectedGroup: any;
  selectedProductType =[];
  productTypeList:string
  constructor(public dialogRef: MatDialogRef<RoofingMonthlyOverrideDialogComponent>, @Inject(MAT_DIALOG_DATA) public data: any) { }

  ngOnInit() {
    this.monthlyOverrideGroup = this.data.monthlyOverrides; 
    this.monthlyOverrideGroup.sort((a, b) => {
      return <any>new Date(b[0].effectiveStartDate) - <any>new Date(a[0].effectiveStartDate);
    });
  }
  groupClick(group: any) {
    this.monthlyOverrideSelectedGroup = group;
    if(this.monthlyOverrideSelectedGroup && this.monthlyOverrideSelectedGroup.length > 0){
        if(this.monthlyOverrideSelectedGroup[0].roofInd) this.selectedProductType.push('Roof');
        if(this.monthlyOverrideSelectedGroup[0].rrInd) this.selectedProductType.push('RR');
        if(this.monthlyOverrideSelectedGroup[0].rrrInd) this.selectedProductType.push('RRR');
        this.productTypeList = this.selectedProductType.join().replace(/,/g, ':');
    }
  } 

}
