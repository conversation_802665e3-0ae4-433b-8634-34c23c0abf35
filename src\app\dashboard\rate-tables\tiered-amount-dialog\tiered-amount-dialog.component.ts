import { Component, Inject, OnInit } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';

@Component({
  selector: 'app-tiered-amount-dialog',
  templateUrl: './tiered-amount-dialog.component.html',
  styleUrls: ['./tiered-amount-dialog.component.css']
})
export class TieredAmountDialogComponent implements OnInit {
  tieredAmountGroup: Element[] = [];
  tierAmountSelectedGroup: any;
  
  constructor(public dialogRef: MatDialogRef<TieredAmountDialogComponent>, @Inject(MAT_DIALOG_DATA) public data: any) { }

  ngOnInit() {
    this.tieredAmountGroup = this.data.tieredAmounts; 
    this.tieredAmountGroup.sort((a, b) => {
      return <any>new Date(b[0].effectiveStartDate) - <any>new Date(a[0].effectiveStartDate);
    });
  }

  groupClick(group: any) {
    this.tierAmountSelectedGroup = group;
  } 

}
