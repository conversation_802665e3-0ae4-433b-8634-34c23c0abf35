import { Component, OnInit, ViewChild } from '@angular/core';
import { UntypedFormBuilder, UntypedFormGroup, Validators } from "@angular/forms";
import { ApiService } from "../../../services/api.service";
import { ToastrService } from 'ngx-toastr';
import { dateLessThanDate, maxProductRateDate } from '../../../shared/validators';
import {MatSort} from '@angular/material/sort';
import {MatTableDataSource} from '@angular/material/table';
import { DataSource } from '@angular/cdk/table';
import {MatPaginator} from '@angular/material/paginator';
import { TableFilterPipe } from '../../../pipe/table-filter.pipe';
import { DatePipe, PercentPipe, CurrencyPipe } from '@angular/common';
import { ProductRateDialogComponent } from '../product-rate-dialog/product-rate-dialog.component';
import { MatDialog } from '@angular/material/dialog';

@Component({
  selector: 'app-product-rate-maintenance',
  templateUrl: './product-rate-maintenance.component.html',
  styleUrls: ['./product-rate-maintenance.component.css']
})
export class ProductRateMaintenanceComponent implements OnInit {
  allProductRate: any;
  activeProductRates: any;
  productRateGroup: any;
  dropdowns: any;
  productRateForm: UntypedFormGroup;
  addInd: boolean = false;
  salesTerritoryDefault: number = 1;
  productTypeDefault: number = 1;
  form: any;
  p: number = 1;
  searchText: string = "";
  public date: Date;
  originalDataSource;
  dataSource;
  displayedColumns = [];
  @ViewChild(MatSort, {static: true}) sort: MatSort;
  @ViewChild(MatPaginator, {static: true}) paginator: MatPaginator;

  columnNames = [
    {
      id: "productType",
      value: "Product Type"
      },
    {
      id: "salesTerritory",
      value: "Sales Territory"
  
    },     
    {
      id: "effectiveStartDate",
      value: "Effective Start Date"
    },
    {
      id: "effectiveEndDate",
      value: "Effective End Date"
    },
    {
      id: "minimumCommission",
      value: "Minimum Commission"
    },
    {
      id: "floorRate",
      value: "Floor Rate"
    },
    {
      id: "baseRate",
      value: "Base Rate"
    }];

  constructor(public apiService: ApiService, private toastMsg: ToastrService, private formBuilder: UntypedFormBuilder, private pipe: TableFilterPipe, 
    private datePipe: DatePipe, private percentPipe: PercentPipe, private currencyPipe: CurrencyPipe,private dialog: MatDialog) {

  }

  ngOnInit() {
    if (!this.apiService.checkPermission('ViewRateTables')) {
      // this.router.navigate(['/ui/dashboard'])
      this.apiService.goBack();
      this.toastMsg.error("Insufficient Permissions. Please make sure your account can access this information.", "Permissions");
    }
    this.getDropdowns();

    this.productRateForm = this.formBuilder.group({
      salesTerritory: [this.salesTerritoryDefault, [Validators.required]],
      productType: [this.productTypeDefault, [Validators.required]],
      effectiveStartDate: ['', [Validators.required]],
      minimumCommission: [0, [Validators.required, Validators.max(20)]],
      floorRate:[''],
      baseRate:[''],
    });

    this.onChanges();
  }

  onChanges() {
    this.productRateForm.valueChanges.subscribe(val => {
      // console.log(this.productRateForm.errors);
    });
  }
  clearDate(date: HTMLInputElement) {
    date.value = "";
    this.date = null; 
    this.productRateForm.controls.effectiveStartDate.setValue('');
        event.stopPropagation();
      }
      
  onSubmit() {
    if (!this.productRateForm.invalid) {
      var values = {
        salesTerritoryId: this.productRateForm.controls.salesTerritory.value,
        productTypeId: this.productRateForm.controls.productType.value,
        effectiveStartDate: this.productRateForm.controls.effectiveStartDate.value,
        minimumCommission: this.productRateForm.controls.minimumCommission.value,
        floorRate: this.productRateForm.controls.floorRate.value,
        baseRate: this.productRateForm.controls.baseRate.value
      }

      var body = {
        NewProductRate: values
      }

      this.apiService.post('ProductRateMaintenance', body)
        .subscribe(data => {
          this.toastMsg.success('Product Rate Successfully Added');
          this.getallProductRate();
          this.getactiveProductRates();
          this.addInd = !this.addInd;
        }, (err: any) => {
          this.toastMsg.error(err.message, 'Server Error!');
        });
    }
  }

  getallProductRate() {
    this.apiService.get('ProductRateMaintenance/retrieveall')
      .subscribe(data => {
        this.allProductRate = data;

        this.productRateForm.setValidators([maxProductRateDate(this.allProductRate)]);

        if (this.productRateGroup) this.getproductRateGroup(this.productRateGroup[0]);
      }, (err: any) => {
        this.toastMsg.error(err.message, 'Server Error!');
      });
  }

  getactiveProductRates() {
    this.apiService.get('ProductRateMaintenance/retrieveactive')
      .subscribe(data => {
        this.activeProductRates = data;
        this.displayedColumns = this.columnNames.map(x => x.id);
        this.createTable();
      }, (err: any) => {
        this.toastMsg.error(err.message, 'Server Error!');
      });
  }

  getDropdowns() {
    this.apiService.get('ProductRateMaintenance/dropdowns')
      .subscribe(data => {
        this.dropdowns = data;
        this.getallProductRate();
        this.getactiveProductRates();
      }, (err: any) => {
        this.toastMsg.error(err.message, 'Server Error!');
      });
  }

  getproductRateGroup(productRate: any) {
    var productRates = this.allProductRate.filter(x => x.productRateId === productRate.productRateId);

    this.productRateGroup = productRates;
  }

  get minimumCommission() { return this.productRateForm.get('minimumCommission'); }

  rowClick(productRate: any) {
    var productRate = this.allProductRate.filter(x => x.salesTerritoryId === productRate.salesTerritoryId && x.productTypeId === productRate.productTypeId);    
    const dialogRef = this.dialog.open(ProductRateDialogComponent, {
      width: '80%', data: { productRate }
    });
    dialogRef.afterClosed().subscribe(result => {
      console.log(result);
    });
  }

  createTable() {
    let tableArr: Element[] = [];
    for(let i:number = 0; i <= this.activeProductRates.length - 1; i++) {
      let currentRow = this.activeProductRates[i];
      tableArr.push({activeInd: currentRow.activeInd, effectiveEndDate: this.datePipe.transform(currentRow.effectiveEndDate), effectiveStartDate: this.datePipe.transform(currentRow.effectiveStartDate),
        productRateId: currentRow.productRateId, productType: currentRow.productType, productTypeId: currentRow.productTypeId, salesTerritory: currentRow.salesTerritory,
      minimumCommission: this.currencyPipe.transform(currentRow.minimumCommission,"USD",true,"1.3-3"),
      floorRate: this.currencyPipe.transform(currentRow.floorRate,"USD",true,"1.3-3"),
      baseRate: this.currencyPipe.transform(currentRow.baseRate,"USD",true,"1.3-3"),
      salesTerritoryId: currentRow.salesTerritoryId
      });
    }
    this.dataSource = new MatTableDataSource(tableArr);
    this.originalDataSource = tableArr;
    this.dataSource.sort = this.sort;
    this.dataSource.paginator = this.paginator;
  }

  searchForItem():void {
    let filteredResults: Element[] = [];
    if (this.searchText == '') {
      this.dataSource = new MatTableDataSource(this.originalDataSource);
      this.dataSource.sort = this.sort;
    this.dataSource.paginator = this.paginator;
    } else {
      // filteredResults = this.originalDataSource.filter(option => option.salesTerritory.toLowerCase().includes(this.searchText));
      filteredResults = this.pipe.transform(this.originalDataSource, this.searchText);
      this.dataSource = new MatTableDataSource(filteredResults);
      this.dataSource.sort = this.sort;
      this.dataSource.paginator = this.paginator;
    }
  }
}

export interface Element {
  productType: string,
  productTypeId: number,
  effectiveStartDate: string,
  effectiveEndDate: string,  
  activeInd: boolean,
  minimumCommission: string,
  floorRate: string,
  baseRate: string,
  salesTerritory: string,
  salesTerritoryId: number,
  productRateId : number
}
